{"message": "成功", "products": [{"baggageRules": [{"baggageNumber": "-1", "baggageWeight": "20", "handNumber": "1", "handWeight": "8", "passengerType": 1, "segmentIndex": 1}], "contactName": "王子钦", "contactPhone": "15875447422", "createTime": "2024-09-27 11:49:24", "orderSerialNo": "H09M1K6P3PE05BD220", "orderState": 5, "passengers": [{"birthday": "2007-06-03 00:00:00", "creditNo": "500108200706031214", "creditType": 1, "firstName": "王子钦", "gender": 1, "gmtCreditValidate": "2025-03-27 00:00:00", "lastName": "", "linkPhone": "15875447422", "nation": "中国", "nationCode": "CN", "passengerType": 1}], "payments": [{"payMoney": "819.36", "payResult": 1, "payTime": "2024-09-27 11:50:30", "tradeNo": "240927115030PY8f3206000141C858"}], "prices": [{"netPrice": "710", "passengerType": 1, "price": "719.36", "tax": "100.00", "taxDetail": {"tax": 50, "yq": 50}}], "productCode": 2, "productType": "0", "purchaserSerialNo": "1839512643605045248", "segments": [{"airline": "MU", "arrAirport": "XIY", "arrCity": "SIA", "arrTerminal": "T3", "arrTime": "2024-12-02 16:35:00", "cabinClassCode": "Y", "cabinCode": "V", "depAirport": "CAN", "depCity": "CAN", "depTerminal": "T1", "depTime": "2024-12-02 12:10:00", "flightNo": "MU9894", "flightShare": false, "operatingAirline": "", "operatingFlightNo": "MU9894", "segmentType": 1, "sequence": 1}], "tickets": [{"firstName": "王子钦", "lastName": "", "pnr": "PHFYQZ", "ticketNo": "7812281867644", "ticketTime": "2024-09-27 11:51:01"}], "totalPrice": "819.36"}], "success": true, "traceId": "H09M1K6P3PE05BD220"}