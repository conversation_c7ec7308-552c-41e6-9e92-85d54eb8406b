spring:
  messages:
    basename: i18n/messages
    encoding: UTF-8
    cache-duration: 3600
  application:
    name: flight-service
  cache:
    type: redis
  profiles:
    active: &profileActive '@profiles.active@'
  cloud:
    nacos:
      config:
        server-addr: &serverAddr '@nacos.server.addr@'
        file-extension: yaml
        username: &nacosUserName dev
        password: &nacosPassword '@Dev@2023715'
        namespace: *profileActive

logging:
  config: classpath:log4j2.yml

dubbo:
  application:
    name: dubbo-provider-flight
    qos-enable: false
    check-serializable: false
    trust-serialize-class-level: 2
    serialize-check-status: DISABLE
    auto-trust-serialize-class: true
  protocol:
    name: tri
    port: -1
    serialization: fastjson2
    prefer-serialization: fastjson2
  registry:
    username: *nacosUserName
    password: *nacosPassword
    parameters:
      register-consumer-url: true
      namespace: '@dubbo.nacos.namespace@'
    address: '@dubbo.nacos.server.addr@'
  consumer:
    timeout: 200000
  #    check: false
  provider:
    timeout: 200000

mybatis-plus:
  configuration:
    # MyBatis 配置
    map-underscore-to-camel-case: true
  global-config:
    # 全局配置
    db-config:
      # 数据库配置
      id-type: auto
    banner: true