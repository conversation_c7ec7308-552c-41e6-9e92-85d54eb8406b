package com.somytrip.provider.hotel.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.somytrip.entity.hotel.swl.HotelSwlEntity;
import com.somytrip.entity.hotel.swl.HotelSwlListVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDate;
import java.util.List;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.provider.hotel.mapper
 * @interfaceName: HotelSwlMapper
 * @author: shadow
 * @description: 深文旅酒店mapper
 * @date: 2024/12/12 15:56
 * @version: 1.0
 */
@Mapper
public interface HotelSwlMapper extends BaseMapper<HotelSwlEntity> {

    @Select("SELECT t1.id            AS hotel_id, " +
            "       t1.hotel_name, " +
            "       t1.lon, " +
            "       t1.lat, " +
            "       t1.address, " +
            "       t1.star, " +
            "       t1.description   AS `desc`, " +
            "       t1.park_info, " +
            "       t1.cover, " +
            "       t1.tels, " +
            "       MAX(t2.min_rate) AS start_price " +
            "FROM hotel_swl t1 " +
            "         LEFT JOIN hotel_min_rate t2 ON t1.hotel_origin = t2.hotel_origin AND t1.hotel_id = t2.hotel_id " +
            "WHERE !t1.is_del AND t2.date = #{date} " +
            "GROUP BY t1.hotel_origin, t1.hotel_id, t2.date")
    List<HotelSwlListVo> queryList(LocalDate date);
}
