package com.somytrip.provider.hotel.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.provider.hotel.config
 * @className: SMSConfig
 * @author: shadow
 * @description: SMS配置
 * @date: 2024/11/2 15:35
 * @version: 1.0
 */
@Data
@Configuration
@ConfigurationProperties("third-party.sms")
public class SMSConfig {

    private boolean enable;
}
