package com.somytrip.provider.hotel.utils.request;

import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.somytrip.entity.hotel.SZJL.SZJLReqBody;
import com.somytrip.entity.hotel.SZJL.SZJLRespBody;
import com.somytrip.provider.hotel.config.SZJLConfig;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.zip.GZIPInputStream;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.provider.hotel.utils.request
 * @className: SZJLRequestUtil
 * @author: shadow
 * @description: 深圳捷旅请求工具类
 * @date: 2024/10/13 15:18
 * @version: 1.0
 */
@Slf4j
public class SZJLRequestUtil {

    private static final Integer SUCCESS_CODE = 0;

    private static final OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(60, TimeUnit.SECONDS)
            .readTimeout(60, TimeUnit.SECONDS)
            .build();

    /**
     * 封装get请求
     *
     * @param url     url
     * @param reqBody 请求参数
     * @return com.somytrip.entity.hotel.SZJL.SZJLRespBody
     * <AUTHOR>
     * @date 2024/10/17 15:16
     */
    public static <T> SZJLRespBody getRequest(String url, SZJLReqBody<T> reqBody) {

//        log.info("reqBody: {}", JSON.toJSONString(reqBody));

        SZJLRespBody resp = null;

        try {
            HttpUrl httpUrl = HttpUrl.parse(url);
            if (httpUrl == null) {
                return null;
            }

            HttpUrl.Builder urlBuilder = httpUrl.newBuilder();
            // 添加参数
            urlBuilder.addQueryParameter("reqData", JSON.toJSONString(reqBody));
//            log.info("url: {}", urlBuilder.build());
            Request request = new Request.Builder()
                    .url(urlBuilder.build().toString())
                    .get()
                    .addHeader("Content-Type", "application/x-www-form-urlencoded")
                    .addHeader("Accept-Encoding", "gzip, deflate")
//                    .addHeader("Accept", "application/json")
                    .build();
            StringBuilder responseStr = new StringBuilder();

            try (Response response = client.newCall(request).execute()) {
//                if (!response.isSuccessful()) {
//                    throw new IOException("Unexpected code " + response);
//                }
                if (response.body() == null) {
                    log.error("SZJL Request(POST), ResponseBody is Null");
                    return null;
                }

                try (GZIPInputStream gis = new GZIPInputStream(response.body().byteStream())) {
                    // 从gis读取解压缩后的数据
                    BufferedReader reader = new BufferedReader(new InputStreamReader(gis, StandardCharsets.UTF_8));
                    String line;
                    while ((line = reader.readLine()) != null) {
                        responseStr.append(line);
                    }
                }
//                log.info("responseStr: {}", responseStr);
                JSONObject responseObj = JSON.parseObject(responseStr.toString());
                resp = responseObj.toJavaObject(SZJLRespBody.class);
            }

//            response = client.newCall(request).execute();
//            ResponseBody body = response.body();
//            if (body == null) {
//                log.error("SZJL Request(GET), ResponseBody is Null");
//                return null;
//            }
//            responseObj = JSONObject.parseObject(body.string());
//            log.info("responseObj: {}", JSON.toJSONString(responseObj));
//            return responseObj.toJavaObject(SZJLRespBody.class);
        } catch (SocketTimeoutException e) {
            log.error(e.getMessage());
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }

        return resp;
    }

    /**
     * 封装post请求
     *
     * @param url     url
     * @param reqBody 请求参数
     * @return com.somytrip.entity.hotel.SZJL.SZJLRespBody
     * <AUTHOR>
     * @date 2024/10/17 15:16
     */
    public static <T> SZJLRespBody postRequest(String url, SZJLReqBody<T> reqBody) {

//        log.info("reqBody: {}", JSON.toJSONString(reqBody));

        // OkHttp请求体
        RequestBody okHttpRequestBody = getOkHttpRequestBody(reqBody);
//        log.info("requestBody: {}", JSONObject.toJSONString(reqBody));

        try {
            HttpUrl httpUrl = HttpUrl.parse(url);
            if (httpUrl == null) {
                return null;
            }
            HttpUrl.Builder urlBuilder = httpUrl.newBuilder();
//            log.info("url: {}", urlBuilder.build());
            Request request = new Request.Builder()
                    .url(urlBuilder.build().toString())
                    .post(okHttpRequestBody)
                    .addHeader("Content-Type", "application/x-www-form-urlencoded")
                    .addHeader("Accept-Encoding", "gzip, deflate")
                    .build();
//            response = client.newCall(request).execute();

            StringBuilder responseStr = new StringBuilder();
            try (Response response = client.newCall(request).execute()) {
//                if (!response.isSuccessful()) {
//                    throw new IOException("Unexpected code " + response);
//                }
                if (response.body() == null) {
                    log.error("SZJL Request(POST), ResponseBody is Null");
                    return null;
                }

                try (GZIPInputStream gis = new GZIPInputStream(response.body().byteStream())) {
                    // 从gis读取解压缩后的数据
                    BufferedReader reader = new BufferedReader(new InputStreamReader(gis, StandardCharsets.UTF_8));
                    String line;
                    while ((line = reader.readLine()) != null) {
                        responseStr.append(line);
                    }
                }
//                log.info("responseStr: {}", responseStr);
                JSONObject jsonObject = JSON.parseObject(responseStr.toString());
                return jsonObject.toJavaObject(SZJLRespBody.class);
            }
        } catch (ConnectException e) {
            log.error(e.getMessage(), e);
            return null;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return null;
    }

    /**
     * 获取请求体
     *
     * @param requestBody 请求参数
     * @return okhttp3.RequestBody
     * <AUTHOR>
     * @date 2024/10/17 15:17
     */
    private static <T> RequestBody getOkHttpRequestBody(SZJLReqBody<T> requestBody) {
        return new MultipartBody.Builder().setType(MultipartBody.FORM)
                .addFormDataPart("reqData", JSON.toJSONString(requestBody))
                .build();
    }

    /**
     * 解析响应结果
     *
     * @param resp        响应
     * @param resultClass 响应类型
     * @return T
     * <AUTHOR>
     * @date 2024/10/17 15:17
     */
    public static <T> T parseResp(SZJLRespBody resp, Class<T> resultClass) {
        return parseResp(resp, null, resultClass);
    }

    /**
     * 解析响应结果
     *
     * @param resp        响应
     * @param respKey     响应外层key
     * @param resultClass 响应类型
     * @return T
     * <AUTHOR>
     * @date 2024/10/17 15:17
     */
    public static <T> T parseResp(SZJLRespBody resp, String respKey, Class<T> resultClass) {

        if (resp == null) return null;

        Integer code = resp.getCode();
        if (!Objects.equals(code, SUCCESS_CODE)) {
            String errorMsg = resp.getErrorMsg();
            String respId = resp.getRespId();
            log.warn("errorMsg: {}, respId: {}", errorMsg, respId);
            return null;
        }

        String result = resp.getResult();
        if (StringUtils.isBlank(result)) return null;

        JSONObject resultObj = JSON.parseObject(result);
        if (StringUtils.isNotBlank(respKey)) {
            resultObj = resultObj.getJSONObject(respKey);
        }
//        return JSON.parseObject(result, resultClass);
        return resultObj.toJavaObject(resultClass);
    }

    public static String getUrlParam(Object paramObj) {
        return "?reqData=" + URLEncoder.encode(JSONObject.toJSONString(paramObj), StandardCharsets.UTF_8);
//        return "?reqData=" + JSONObject.toJSONString(paramObj);
    }

    /**
     * 封装请求体
     *
     * @param data       输入请求参数
     * @param szjlConfig 配置
     * @return com.somytrip.entity.hotel.SZJL.SZJLReqBody<T>
     * <AUTHOR>
     * @date 2024/10/17 15:18
     */
    public static <T> SZJLReqBody<T> getReqBody(T data, SZJLConfig szjlConfig) {

        SZJLReqBody<T> reqBody = new SZJLReqBody<>();
        reqBody.setData(data);
        SZJLReqBody.Head head = new SZJLReqBody.Head();
        head.setAppKey(szjlConfig.getAppKey());
        head.setVersion(szjlConfig.getVersion());
        String timestamp = String.valueOf(new Date().getTime());
        String sign = getSign(szjlConfig.getAppKey(), szjlConfig.getSecretKey(), timestamp);
//        log.info("sign: {}", sign);
        head.setTimestamp(timestamp);
        head.setSign(sign);
        reqBody.setHead(head);

        return reqBody;
    }

    private static String getSign(String appKey, String secretKey, String timestamp) {
        return DigestUtil.md5Hex(DigestUtil.md5Hex(secretKey + appKey).toLowerCase() + timestamp).toLowerCase();
    }
}
