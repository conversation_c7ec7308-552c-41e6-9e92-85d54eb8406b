package com.somytrip.provider.hotel.utils.request;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.somytrip.entity.enums.hotel.errorcode.WEBBEDSErrorCode;
import com.somytrip.entity.enums.hotel.method.WEBBEDSMethod;
import com.somytrip.entity.hotel.WEBBEDS.WEBBEDSBaseReq;
import com.somytrip.entity.hotel.WEBBEDS.WEBBEDSRequestBody;
import com.somytrip.exception.BusinessException;
import com.somytrip.provider.hotel.utils.SHA1Util;
import com.somytrip.provider.hotel.utils.SnowFlakeUtil;
import com.somytrip.utils.ThreadLocalManagerUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName: WEBBEDSRequestUtil
 * @Description: WEBBEDS酒店请求工具类
 * @Author: shadow
 * @Date: 2024/3/18 18:57
 */
@Component
@Slf4j
public class WEBBEDSRequestUtil {

    OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(60, TimeUnit.SECONDS)
            .readTimeout(60, TimeUnit.SECONDS)
            .build();
    @Value("${webbeds.request.host:http://b2b.akuhotel.com}")
    private String HOST;
    // 用户名
    @Value("${webbeds.request.userName:akuagent-api-1357297779111}")
    private String USERNAME;
    // 密码(需要SHA1)
    @Value("${webbeds.request.password:rM89sEYj7k6G}")
    private String PASSWORD;

    /**
     * 获取AccessToken
     *
     * @return String AccessToken
     */
    public String getAccessToken() {

        // response
        Response response;
        // client

        // 响应JSON对象
        JSONObject responseObj = new JSONObject();
        // token
        String token;

        WEBBEDSMethod method = WEBBEDSMethod.TOKEN;

        try {
            HttpUrl httpUrl = HttpUrl.parse(HOST + method.getUrl());
            if (httpUrl == null) {
                return null;
            }
            HttpUrl.Builder urlBuilder = httpUrl.newBuilder();
            // 添加参数
            urlBuilder.addQueryParameter("username", USERNAME);
            urlBuilder.addQueryParameter("password", SHA1Util.encode(PASSWORD));

            Request request = new Request.Builder()
                    .url(urlBuilder.build().toString())
                    .get()
                    .addHeader("Content-Type", "application/x-www-form-urlencoded")
                    .addHeader("Accept", "application/json")
                    .build();
            response = client.newCall(request).execute();
            ResponseBody body = response.body();
            if (body == null) {
                log.error("Body is Null");
                return null;
            }
            responseObj = JSONObject.parseObject(body.string());
//            log.info("responseObj: {}", responseObj);
        } catch (SocketTimeoutException e) {
            log.error(e.getMessage());
//            throw new BusinessException(e.getMessage());
            return null;
        } catch (IOException e) {
            log.error("请求失败");
            log.error(e.getMessage(), e);
            throw new BusinessException("请求失败");
        }

        // code = 1 即成功
        Integer code = responseObj.getInteger("code");
        if (code != 1) {
            log.info("code: {}", code);
            return null;
        }

        token = responseObj.getString("token");
        return "Bearer " + token;
    }

    /**
     * 根据当前环境语言转换为WEBBEDS语言参数
     * 目前WEBBEDS语言参数包括: en、zh-Hans
     *
     * @return String WEBBEDS语言参数
     */
    public String getLanguage() {
        String systemLanguage = ThreadLocalManagerUtil.getLanguage();
        return Objects.equals("zh_CN", systemLanguage) ? "zh-Hans" : "en";
    }

    /**
     * 获取请求体
     *
     * @param requestParam 请求参数
     * @param <T>          请求体类型
     * @return WEBBEDSRequestBody<T> WEBBEDS请求体
     */
    public <T extends WEBBEDSBaseReq> WEBBEDSRequestBody<T> getRequestBody(T requestParam) {

        WEBBEDSRequestBody<T> webbedsRequestBody = new WEBBEDSRequestBody<>();
        // 设置语言
        requestParam.setLanguage(getLanguage());
        webbedsRequestBody.setRequestParam(requestParam);
        return webbedsRequestBody;
    }

    /**
     * 请求接口(JSONObject)
     *
     * @param requestBody 请求体
     * @param method      请求方法枚举
     * @param <T>         请求体类型
     * @return JSONObject 结果json对象
     */
    public <T> JSONObject generalRequestObj(WEBBEDSRequestBody<T> requestBody, WEBBEDSMethod method) {

        String responseStr = generalRequest(requestBody, method);
        return JSONObject.parseObject(responseStr);
    }

    /**
     * 请求接口(JSONArray)
     *
     * @param requestBody 请求体
     * @param method      请求方法枚举
     * @param <T>         请求体类型
     * @return JSONArray 结果json数组
     */
    public <T> JSONArray generalRequestArr(WEBBEDSRequestBody<T> requestBody, WEBBEDSMethod method) {

        String responseStr = generalRequest(requestBody, method);
        return JSONArray.parseArray(responseStr);
    }

    /**
     * 请求接口
     *
     * @param requestBody 请求体
     * @param method      请求方法枚举
     * @param <T>         请求体类型
     * @return String 结果json字符串
     */
    public <T> String generalRequest(WEBBEDSRequestBody<T> requestBody, WEBBEDSMethod method) {
        String result = null;
        Response response;

        // AccessToken
        String accessToken = getAccessToken();
        if (StringUtils.isBlank(accessToken)) {
            // TODO: 未获取到token
            throw new BusinessException("");
        }

        // 生成device_token
        SnowFlakeUtil snowFlakeUtil = new SnowFlakeUtil(1, 2);
        String deviceToken = "WB" + snowFlakeUtil.nextId();

        // OkHttp请求体
        RequestBody okHttpRequestBody = getOkHttpRequestBody(requestBody);
//        log.info("requestBody: {}", JSONObject.toJSONString(requestBody));

        // 拼接url
        String url = HOST + method.getUrl();
        String responseStr = null;

        try {
            HttpUrl httpUrl = HttpUrl.parse(url);
            if (httpUrl == null) {
                log.error("httpUrl is null");
                throw new BusinessException("1");
            }
            HttpUrl.Builder urlBuilder = httpUrl.newBuilder();
            Request request = new Request.Builder()
                    .url(urlBuilder.build().toString())
                    .post(okHttpRequestBody)
                    .addHeader("Authorization", accessToken)
                    .addHeader("device_token", deviceToken)
                    .build();
            response = client.newCall(request).execute();

            // 读取响应体
            if (!response.isSuccessful()) {
                log.error("code: {}, message: {}", response.code(), response.message());
                throw new BusinessException("Request Fail");
            }
            if (response.body() == null) {
                throw new BusinessException("ResponseResult Body is Null");
            }

            responseStr = response.body().string();
//            log.info("responseStr: {}", responseStr);
            JSONObject jsonObject = JSON.parseObject(responseStr);
//            log.info("jsonObject: {}", jsonObject);
            if (jsonObject == null) {
                throw new BusinessException("ResponseResult Obj is Null");
            }
            Integer code = jsonObject.getInteger("code");
            // 请求结果不成功
            if (code != 1) {
                WEBBEDSErrorCode webbedsErrorCode = WEBBEDSErrorCode.getWEBBEDSErrorCodeByCode(code);
                log.error("WEBBEDS Result, Code: {}, ErrorType: {}, ErrorDesc: {}",
                        code, webbedsErrorCode.getErrorType(), webbedsErrorCode.getErrorDesc());
                log.error("Error Msg: {}", jsonObject.getString("msg"));
            }
            result = jsonObject.getString("data");
            if (result == null) {
                log.warn("WEBBEDS ResponseResult Data is Null, responseObj: {}", jsonObject);
            }
        } catch (ConnectException e) {
            log.error("Connect Fail");
            log.error(e.getMessage());
            return null;
        } catch (Exception e) {
            log.error("请求失败");
            log.error("responseStr: {}", responseStr);
            log.error(e.getMessage(), e);
            throw new BusinessException("请求失败");
        }

//        return responseObj;
        return result;
    }

    /**
     * 获取OkHttp请求体
     *
     * @param requestBody WEBBEDS请求体
     * @param <T>         WEBBEDS请求体类型
     * @return RequestBody OKHttp请求体
     */
    private <T> RequestBody getOkHttpRequestBody(WEBBEDSRequestBody<T> requestBody) {
        JSONObject body = JSONObject.from(requestBody.getRequestParam());
        MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
        return RequestBody.create(body.toJSONString(), mediaType);
    }
}
