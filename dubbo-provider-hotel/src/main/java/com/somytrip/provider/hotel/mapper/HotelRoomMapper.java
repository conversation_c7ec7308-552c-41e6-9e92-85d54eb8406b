package com.somytrip.provider.hotel.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.somytrip.entity.dto.hotel.PaginationDto;
import com.somytrip.entity.enums.hotel.HotelOrigin;
import com.somytrip.entity.hotel.HotelRoomEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName: HotelRoomMapper
 * @Description: 酒店房型Mapper
 * @Author: shadow
 * @Date: 2024/2/2 15:54
 */
@Mapper
public interface HotelRoomMapper extends BaseMapper<HotelRoomEntity> {

    List<HotelRoomEntity> queryRoomList(@Param("pagination") PaginationDto pagination);

    List<HotelRoomEntity> queryListFromHotelIdAndRoomIds(@Param("hotelOrigin") HotelOrigin hotelOrigin,
                                                         @Param("hotelId") String hotelId,
                                                         @Param("roomIds") List<String> roomIds,
                                                         @Param("lang") String lang);
}
