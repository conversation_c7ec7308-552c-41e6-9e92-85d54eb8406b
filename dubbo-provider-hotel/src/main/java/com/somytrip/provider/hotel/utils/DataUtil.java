package com.somytrip.provider.hotel.utils;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.thread.NamedThreadFactory;
import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.somytrip.entity.enums.RedisKey;
import com.somytrip.provider.hotel.service.RedisService;
import com.somytrip.utils.EqualsUtil;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName: DataUtil
 * @Description: 数据工具类
 * @Author: shadow
 * @Date: 2024/2/22 10:12
 */
@Slf4j
public class DataUtil {

    // 默认更新数据大小
    public static final Integer UPDATE_SIZE = 5000;
    public static final int MAX_RETRIES = 10;

    public static <T> void updateData(T newEntity, T existingEntity, BaseMapper<T> mapper, String... ignoreProperties) {

        if (existingEntity == null) {
            int insertResult = mapper.insert(newEntity);
            if (insertResult != 1) {
                log.warn("Insert Entity Fail, Entity: {}", newEntity);
            }
        } else {
            if (!EqualsUtil.equals(newEntity, existingEntity, ignoreProperties)) {
                try {
                    Method setId = newEntity.getClass().getMethod("setId", existingEntity.getClass().getMethod("getId").getReturnType());
                    setId.invoke(newEntity, existingEntity.getClass().getMethod("getId").invoke(existingEntity));
                } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
                    log.error(e.getMessage(), e);
                }
                int updateResult = mapper.updateById(newEntity);
                if (updateResult != 1) {
                    log.warn("Update Entity Fail, Entity: {}", newEntity);
                }
            }
        }
    }

    /**
     * 处理异步批量更新数据(主键相同则更新)
     * 将数据加入数据列表 当列表长度达到预设更新长度时 执行异步更新
     *
     * @param dataList 数据Entity列表
     * @param data     本次加入的Entity对象
     * @param service  数据Entity对应IService
     * <AUTHOR>
     * @date 2024/4/8 9:43
     */
    public static <T> void processBatchSaveAsync(List<T> dataList, T data, IService<T> service) {

        processBatchSaveAsync(dataList, data, service, false);
    }

    public static <T> void processBatchSaveAsync(List<T> dataList, T data, IService<T> service, boolean last) {

        dataList.add(data);
        if (dataList.size() == UPDATE_SIZE || last) {
            batchSaveAsync(dataList, service);
            dataList.clear();
        }
    }

    /**
     * 异步批量更新数据(主键相同则更新)
     *
     * @param dataList 数据Entity列表
     * @param service  数据Entity对应IService
     * <AUTHOR>
     * @date 2024/3/27 9:38
     */
    public static <T> void batchSaveAsync(List<T> dataList, IService<T> service) {

        long start = System.currentTimeMillis();

        ThreadPoolExecutor poolExecutor = new ThreadPoolExecutor(
                5,
                5,
                30,
                TimeUnit.SECONDS,
                new LinkedBlockingDeque<>(10),
                new NamedThreadFactory("执行线程", false),
                (r, executor) -> log.info("拒绝: {}", r)
        );

        List<List<T>> partition = ListUtil.partition(dataList, 1000);

        // 使用CountDownLatch保证所有线程都执行完成
        CountDownLatch latch = new CountDownLatch(5);
        partition.forEach(item -> {
            poolExecutor.execute(() -> {
//                service.saveOrUpdateBatch(item, 1000);
//                latch.countDown();
                try {
                    executeWithRetry(item, service);
                } finally {
                    latch.countDown();
                }
            });
        });
        try {
            // 设定超时时间
            boolean await = latch.await(10, TimeUnit.SECONDS);
            log.info("await: {}", await);
        } catch (InterruptedException e) {
            log.error(e.getMessage(), e);
        }
        long end = System.currentTimeMillis();

        log.info("Time Consuming: {}, Update {}", end - start, service);
        // 关闭线程池
        poolExecutor.shutdown();
    }

    private static <T> void executeWithRetry(List<T> item, IService<T> service) {
        executeWithRetry(item, service, MAX_RETRIES);
    }

    private static <T> void executeWithRetry(List<T> item, IService<T> service, int maxRetries) {
        int retries = 0;
        while (retries < maxRetries) {
            try {
                service.saveOrUpdateBatch(item, 1000);
                return;
            } catch (Exception e) {
                retries++;
                log.error("Task failed[{}], retrying... (Attempt {})", item.get(0).getClass(), retries, e);
                if (retries >= maxRetries) {
                    log.error("Max retries reached, giving up.", e);
                }
            }
        }
    }

    @Deprecated
    public static <T> void updateFromCache(RedisKey redisKey, RedisService redisService, IService<T> service, Class<T> clazz) {

        int start = 0;
        int pageSize = 5000;

        while (true) {
            List<Object> objects = redisService.lGet(redisKey.getKey(), start, start + pageSize - 1);
            log.info("size: {}", objects.size());
            List<T> dataList = JSONArray.from(objects).toJavaList(clazz);

            batchSaveAsync(dataList, service);
            if (objects.size() < pageSize) {
                break;
            }
            start += pageSize;
        }
        log.info("UpdateFromCache Ok[{}]", clazz);
        // FIXME: 确保全部成功才删除
//        // 删除redis记录
//        redisService.del(redisKey.getMd5Key());
    }
}
