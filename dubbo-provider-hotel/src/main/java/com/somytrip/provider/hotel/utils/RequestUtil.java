package com.somytrip.provider.hotel.utils;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * @ClassName: RequestUtil
 * @Description:
 * @Author: shadow
 * @Date: 2024/3/8 9:40
 */
public class RequestUtil {

    /**
     * 获取HttpServletRequest
     *
     * @return HttpServletRequest
     */
    public static HttpServletRequest getHttpServletRequest() {
        ServletRequestAttributes servletRequestAttributes =
                (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (servletRequestAttributes == null) {
            return null;
        }
        return servletRequestAttributes.getRequest();
    }

    /**
     * 获取请求ip地址(不返回内网地址)
     *
     * @param request HttpServletRequest
     * @return String ip地址
     */
    public static String getIpAddr(HttpServletRequest request) {

        if (request == null) {
            return null;
        }

        //目前则是网关ip
        String ip = request.getHeader("X-Real-IP");
        if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
            return ip;
        }
        ip = request.getHeader("X-Forwarded-For");
        if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
            int index = ip.indexOf(',');
            if (index != -1) {
                //只获取第一个值
                return ip.substring(0, index);
            } else {
                return ip;
            }
        } else {
            // 取不到真实ip则返回空，不能返回内网地址。
            return "";
        }
    }
}
