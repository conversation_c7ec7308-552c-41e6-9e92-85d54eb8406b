package com.somytrip.provider.hotel.utils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

/**
 * @ClassName: GzipUtil
 * @Description:
 * @Author: shadow
 * @Date: 2024/1/12 15:34
 */
public class GzipUtil {

    public static String compress(String str) throws Exception {
        if (str == null || str.isEmpty()) {
            return str;
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        GZIPOutputStream gzip = new GZIPOutputStream(out);
        gzip.write(str.getBytes(StandardCharsets.UTF_8));
        gzip.close();
        byte[] bytes = out.toByteArray();
        out.close();
        return Base64.getEncoder().encodeToString(bytes);
    }

    public static String uncompress(String str) throws Exception {
        if (str == null || str.isEmpty()) {
            return str;
        }
        byte[] bytes = Base64.getDecoder().decode(str);
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        ByteArrayInputStream in = new ByteArrayInputStream(bytes);
        GZIPInputStream gunzip = new GZIPInputStream(in);
        byte[] buffer = new byte[256];
        int n;
        while ((n = gunzip.read(buffer)) >= 0) {
            out.write(buffer, 0, n);
        }
        return out.toString(StandardCharsets.UTF_8);
    }
}
