package com.somytrip.provider.hotel.utils.request;

import com.somytrip.entity.dto.hotel.PaginationDto;
import com.somytrip.entity.hotel.NCNB.NCNBReqBody;
import lombok.extern.slf4j.Slf4j;

/**
 * @ClassName: NCNBRequestUtil
 * @Description: 捷旅请求工具类
 * @Author: shadow
 * @Date: 2024/3/10 15:23
 */
@Slf4j
public class NCNBRequestUtil {

    /**
     * 获取请求体
     *
     * @param searchConditions 搜索参数
     * @param pagination       分页参数
     * @param <T>              请求体类型
     * @return NCNBReqBody<T> 捷旅请求体
     */
    public static <T> NCNBReqBody<T> getRequestBody(T searchConditions, PaginationDto pagination) {

        NCNBReqBody<T> reqBody = getRequestBody(searchConditions);
        NCNBReqBody.ScrollingInfo scrollingInfo = new NCNBReqBody.ScrollingInfo(pagination);
        reqBody.setScrollingInfo(scrollingInfo);
        return reqBody;
    }

    /**
     * 获取请求体
     *
     * @param searchConditions 搜索参数
     * @param isQueryAll       是否查询全部
     * @param <T>              请求体类型
     * @return NCNBReqBody<T> 捷旅请求体
     */
    public static <T> NCNBReqBody<T> getRequestBody(T searchConditions, boolean isQueryAll) {

        NCNBReqBody<T> reqBody = getRequestBody(searchConditions);
        if (isQueryAll) {
            // 设置查询全部
            NCNBReqBody.ScrollingInfo scrollingInfo = new NCNBReqBody.ScrollingInfo();
            scrollingInfo.setDisplayReq(40);
            reqBody.setScrollingInfo(scrollingInfo);
        }
        return reqBody;
    }

    /**
     * 获取请求体
     *
     * @param searchConditions 搜索参数
     * @param <T>              请求体类型
     * @return NCNBReqBody<T> 捷旅请求体
     */
    public static <T> NCNBReqBody<T> getRequestBody(T searchConditions) {

        NCNBReqBody<T> reqBody = new NCNBReqBody<>();
        reqBody.setSearchConditions(searchConditions);
        return reqBody;
    }
}
