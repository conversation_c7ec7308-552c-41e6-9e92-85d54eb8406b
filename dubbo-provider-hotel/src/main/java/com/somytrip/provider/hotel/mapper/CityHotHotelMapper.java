package com.somytrip.provider.hotel.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.somytrip.entity.dto.city.CityHotHotelEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.provider.hotel.mapper
 * @interfaceName: CityHotHotelMapper
 * @author: shadow
 * @description: 热门城市(酒店)Mapper
 * @date: 2024/4/19 10:17
 * @version: 1.0
 */
@Mapper
public interface CityHotHotelMapper extends BaseMapper<CityHotHotelEntity> {

    @Select("SELECT city_code FROM city_hot_hotel")
    List<String> queryCityCodes();
}
