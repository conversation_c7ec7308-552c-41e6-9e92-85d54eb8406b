package com.somytrip.provider.hotel.config;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;

/**
 * @ClassName: LettuceConnectionValidConfig
 * @Description:
 * @Author: shadow
 * @Date: 2024/3/10 16:19
 */
@Configuration
@Slf4j
public class LettuceConnectionValidConfig implements InitializingBean {
    @Resource
    private RedisConnectionFactory redisConnectionFactory;

    @Override
    public void afterPropertiesSet() {
        if (redisConnectionFactory instanceof LettuceConnectionFactory c) {
            c.setValidateConnection(true);
        }
    }
}
