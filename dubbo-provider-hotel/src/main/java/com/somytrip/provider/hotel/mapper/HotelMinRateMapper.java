package com.somytrip.provider.hotel.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.somytrip.entity.dto.hotel.QueryHotelRateListDto;
import com.somytrip.entity.hotel.HotelMinRateEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @projectName: hotel-service
 * @package: com.somytrip.provider.hotel.mapper
 * @interfaceName: HotelMinRateMapper
 * @author: shadow
 * @description: 酒店每日最低价Mapper
 * @date: 2024/4/1 16:13
 * @version: 1.0
 */
@Mapper
public interface HotelMinRateMapper extends BaseMapper<HotelMinRateEntity> {

    List<HotelMinRateEntity> queryListFromDto(@Param("dto") QueryHotelRateListDto dto);

    /**
     * 小奇AI-调用查询酒店数据
     *
     * @param dto
     * @return
     */
    List<HotelMinRateEntity> queryListFromDtoAi(@Param("dto") QueryHotelRateListDto dto);
}
