package com.somytrip.provider.hotel.config;

import com.somytrip.provider.hotel.interrupt.LocaleInterceptor;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 */
@Configuration
public class InterceptorConfig implements WebMvcConfigurer {

    @Resource
    private LocaleInterceptor localeInterceptor;

    // addPathPatterns      - 要拦截的
    // excludePathPatterns  - 要排除的(不拦截)
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 国际化(多语言)拦截器
        registry.addInterceptor(localeInterceptor);
    }
}
