package com.somytrip.provider.hotel.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.somytrip.entity.enums.hotel.HotelOrigin;
import com.somytrip.entity.hotel.HotelRateEntity;
import com.somytrip.entity.vo.hotel.HotelRateVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * @ClassName: HotelRateMapper
 * @Description: 酒店价格mapper
 * @Author: shadow
 * @Date: 2024/2/20 11:53
 */
@Mapper
public interface HotelRateMapper extends BaseMapper<HotelRateEntity> {

    @Select("SELECT t1.hotel_origin, " +
            "       t1.hotel_id, " +
            "       t1.rate_plan_id, " +
            "       t1.room_type_id, " +
            "       t1.hotel_code, " +
            "       t1.currency_code, " +
            "       t1.member, " +
            "       t1.weekend, " +
            "       t2.weekend_start, " +
            "       t2.weekend_end " +
            "FROM hotel_rate t1 " +
            "         JOIN hotel_suppliers t2 ON t1.hotel_id = t2.hotel_id AND t1.hotel_code = t2.hotel_code " +
            "WHERE t1.hotel_id = #{hotelId} " +
            "AND t1.hotel_origin = #{hotelOrigin} " +
            "ORDER BY t1.member " +
            "LIMIT 1")
    HotelRateVo queryMinPriceRate(@Param("hotelId") String hotelId,
                                  @Param("hotelOrigin") HotelOrigin hotelOrigin);
}
