package com.somytrip.provider.hotel.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.ResourceBundleMessageSource;

import java.util.Locale;

/**
 * @ClassName: LocaleConfig
 * @Description: 国际化 Config配置类
 * @Author: shadow
 * @Date: 2023/11/6 1:11
 */
@Configuration
public class LocaleConfig {

    @Bean
    public ResourceBundleMessageSource messageSource() {
        Locale.setDefault(Locale.CHINA);
        ResourceBundleMessageSource source = new ResourceBundleMessageSource();
        // 设置国际化文件存储路径和名称  i18n目录，messages文件名
        source.setBasenames("i18n/messages");
        // 设置根据key如果没有获取到对应的文本信息, 则返回key作为信息
        source.setUseCodeAsDefaultMessage(true);
        // 设置字符编码
        source.setDefaultEncoding("UTF-8");
        return source;
    }
}
