package com.somytrip.provider.hotel.utils;

import com.alibaba.fastjson2.JSONObject;
import com.somytrip.entity.hotel.NCNB.NCNBReqBody;
import com.somytrip.entity.hotel.NCNB.NCNBRespBody;
import okhttp3.MediaType;
import okhttp3.RequestBody;

/**
 * @ClassName: NCNBUtils
 * @Description: 捷旅工具类
 * @Author: shadow
 * @Date: 2024/2/18 10:28
 */
public class NCNBUtils {

    /**
     * 获取请求体
     *
     * @param bodyEntity com.somytrip.entity.hotel.NCNBReqBodyEntity
     * @return okhttp3.RequestBody
     */
    public static <T> RequestBody getRequestBody(NCNBReqBody<T> bodyEntity) {
        JSONObject body = getRequestBodyObject(bodyEntity);
        MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
        return RequestBody.create(body.toJSONString(), mediaType);
    }

    /**
     * 获取响应体
     *
     * @param responseObj com.alibaba.fastjson2.JSONObject
     * @return com.somytrip.entity.hotel.NCNB.NCNBRespBody
     */
    public static NCNBRespBody getResponseBody(JSONObject responseObj) {
        JSONObject cnResponse = responseObj.getJSONObject("CnResponse");
        return new NCNBRespBody(cnResponse);
    }

    /**
     * 获取捷旅请求体JSONObject
     *
     * @param bodyEntity 捷旅请求实体
     * @return JSONObject 请求实体JSONObject
     */
    public static <T> JSONObject getRequestBodyObject(NCNBReqBody<T> bodyEntity) {
        JSONObject body = new JSONObject();
        body.put("CNRequest", bodyEntity);
        return body;
    }
}
