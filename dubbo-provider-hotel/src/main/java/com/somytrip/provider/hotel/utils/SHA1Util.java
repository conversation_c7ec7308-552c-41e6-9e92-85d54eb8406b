package com.somytrip.provider.hotel.utils;

import java.security.MessageDigest;

/**
 * @ClassName: SHA1Util
 * @Description:
 * @Author: shadow
 * @Date: 2024/1/23 15:17
 */
public class SHA1Util {

    private static final char[] CHARS = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};

    /**
     * SHA1加密
     *
     * @param str 要加密的字符串
     * @return String 加密后字符串
     */
    public static String encode(String str) {
        if (str == null) {
            return null;
        }
        try {
            MessageDigest messageDigest = MessageDigest.getInstance("SHA1");
            messageDigest.update(str.getBytes());
            byte[] digest = messageDigest.digest();
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(CHARS[(b >> 4) & 15]);
                sb.append(CHARS[b & 15]);
            }
            return sb.toString();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
