package com.somytrip.provider.hotel.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.provider.hotel.config
 * @className: SwlConfig
 * @author: shadow
 * @description: 深文旅配置
 * @date: 2024/12/13 10:18
 * @version: 1.0
 */
@Data
@Configuration
@ConfigurationProperties("third-party.swl")
public class SwlConfig {

    private String secretId;

    private String secretKey;

    private String pushUrl;

    private String pushStatusChangeUrl;

    private String privateKey;
}
