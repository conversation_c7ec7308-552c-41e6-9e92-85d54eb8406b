package com.somytrip.provider.hotel.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.provider.hotel.config
 * @className: SZJLConfig
 * @author: shadow
 * @description: 深圳捷旅配置
 * @date: 2024/10/13 14:40
 * @version: 1.0
 */
@Data
@Configuration
@ConfigurationProperties("third-party.szjl")
public class SZJLConfig {

    private String hotelHost;

    private String orderHost;

    private String appKey;

    private String secretKey;

    private String version;

    private boolean useTest;

    private TestConfig testConfig;

    public String getHotelHost() {
        return !useTest ? hotelHost : testConfig.host;
    }

    public String getOrderHost() {
        return !useTest ? orderHost : testConfig.host;
    }

    public String getAppKey() {
        return !useTest ? appKey : testConfig.appKey;
    }

    public String getSecretKey() {
        return !useTest ? secretKey : testConfig.secretKey;
    }

    @Data
    public static class TestConfig {

        private String host;

        private String appKey;

        private String secretKey;
    }
}
