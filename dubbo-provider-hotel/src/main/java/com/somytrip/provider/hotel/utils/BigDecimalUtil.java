package com.somytrip.provider.hotel.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * @ClassName: BigDecimalUtil
 * @Description: 高精度工具类
 * @Author: shadow
 * @Date: 2024/1/2 11:40
 */
public class BigDecimalUtil {

    /**
     * 四舍五入
     *
     * @param bigDecimal 待处理高精度
     * @param num        保留几位小数
     * @return 四舍五入后字符串
     */
    public static String halfAdjust2Str(BigDecimal bigDecimal, Integer num) {
        if (bigDecimal == null) {
            return "";
        }
        return bigDecimal.setScale(num, RoundingMode.HALF_UP).toString();
    }

    public static BigDecimal halfAdjust(BigDecimal bigDecimal, Integer num) {
        if (bigDecimal == null) {
            return bigDecimal;
        }
        return bigDecimal.setScale(num, RoundingMode.HALF_UP);
    }
}
