package com.somytrip.provider.hotel.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * @ClassName: WebMvcConfig
 * @Description:
 * @Author: shadow
 * @Date: 2023/9/24 12:01
 */
@EnableWebMvc
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

//    @Bean
//    public UserInfoHandlerMethodArgumentResolver userInfoResolver() {
//        return new UserInfoHandlerMethodArgumentResolver();
//    }
//
//    @Override
//    public void addArgumentResolvers(Item<HandlerMethodArgumentResolver> resolvers) {
//        resolvers.add(userInfoResolver());
//    }
}