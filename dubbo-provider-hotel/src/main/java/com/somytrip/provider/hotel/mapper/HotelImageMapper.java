package com.somytrip.provider.hotel.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.somytrip.entity.hotel.HotelImageEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @ClassName: HotelImageMapper
 * @Description: 酒店图片Mapper
 * @Author: shadow
 * @Date: 2024/2/2 15:36
 */
@Mapper
public interface HotelImageMapper extends BaseMapper<HotelImageEntity> {

    @ResultMap("mybatis-plus_HotelImageEntity")
    @Select("select * from hotel_images_bak where hotel_id = #{hotelId}")
    List<HotelImageEntity> queryBakList(String hotelId);
}
