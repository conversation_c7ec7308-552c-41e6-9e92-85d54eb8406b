package com.somytrip.provider.hotel.config;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.support.WebClientAdapter;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;

import java.nio.charset.StandardCharsets;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-02-03 15:47
 */
@Configuration
@Slf4j
public class WebClientConfig {
    private final WebClient webClient = WebClient.builder()
            .baseUrl("https://www.baiduc.com")
            .build();

    private WebClient getNewWebClient(String baseUrl) {
        return this.webClient.mutate().baseUrl(baseUrl).build();
    }

    public <S> S createService(String baseUrl, Class<S> serviceType) {
        log.info("createService baseUrl: {}", baseUrl);
        HttpServiceProxyFactory factory = HttpServiceProxyFactory.builder()
                .exchangeAdapter(WebClientAdapter.create(getNewWebClient(baseUrl)))
                .build();
        return factory.createClient(serviceType);
    }

    public <S> S createService(Class<S> serviceType) {
        try {
            final String baseUrl = "BASE_URL";
            String url = StrUtil.str(serviceType.getField(baseUrl).get(serviceType), StandardCharsets.UTF_8);
            log.info("reflect get createService baseUrl: {}", url);
            HttpServiceProxyFactory factory = HttpServiceProxyFactory.builder()
                    .exchangeAdapter(WebClientAdapter.create(getNewWebClient(url)))
                    .build();
            return factory.createClient(serviceType);
        } catch (Exception e) {
            log.error("reflect get createService error: {}", serviceType.toString());
        }
        return null;
    }
}
