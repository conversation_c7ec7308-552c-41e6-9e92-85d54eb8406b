package com.somytrip.provider.community.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.somytrip.entity.community.UserLabel;
import com.somytrip.entity.dto.community.user.LabelTotalDTO;
import com.somytrip.entity.dto.community.user.UserLabelVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 用户标签信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Mapper
public interface UserLabelMapper extends BaseMapper<UserLabel> {

    /**
     * 获取用户标签
     *
     * @param userId
     * @return
     */
    List<UserLabelVO> getUserLabelList(@Param("userId") Long userId);

    /**
     * 批量更新标签百分比
     *
     * @param labelDTO
     * @return
     */
    Integer updateLabelPercentage(@Param("label") LabelTotalDTO labelDTO);
}
