package com.somytrip.provider.community.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.somytrip.entity.community.MessagesUser;
import com.somytrip.entity.dto.community.message.PrivateMessageListRes;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 消息信息用户表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-11
 */
@Mapper
public interface MessagesUserMapper extends BaseMapper<MessagesUser> {

    /**
     * 私信列表
     *
     * @param userId
     * @param pageSize
     * @param offset
     * @return
     */
    List<PrivateMessageListRes> dataList(@Param("userId") Long userId,
                                         @Param("pageSize") Integer pageSize,
                                         @Param("offset") Integer offset,
                                         @Param("readFlag") Integer readFlag);

    /**
     * 获取聊天对话框
     *
     * @param sendUserId
     * @param receiveUserId
     * @return
     */
    @Select("""
                SELECT
                    t.session_id
                FROM
                    (
                    SELECT
                        session_id
                    FROM
                        messages_user
                    WHERE
                        sender_id = #{sendUserId}
                        AND receiver_id = #{receiveUserId}
                        AND del_flag = 0
                UNION
                    SELECT
                        session_id
                    FROM
                        messages_user
                    WHERE
                        sender_id = #{receiveUserId}
                        AND receiver_id = #{sendUserId}
                    AND del_flag = 0
                    )
                t LIMIT 1
            """)
    String getChatSessionId(@Param("sendUserId") Long sendUserId,
                            @Param("receiveUserId") Long receiveUserId);

    /**
     * 发送消息，更新统计数
     *
     * @param sessionId
     * @param localDateTime
     * @param content
     */
    @Update("""
                update messages_user set update_time = #{localDateTime},last_content = #{content} where session_id = #{sessionId} 
            """)
    void modifyUserMessageSession(@Param("sessionId") String sessionId,
                                  @Param("localDateTime") LocalDateTime localDateTime,
                                  @Param("content") String content);

}
