package com.somytrip.provider.community.util;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

public class TimeDifferenceFormatterUtils {

    /**
     * 相隔大于60秒，显示xx分xx秒，相隔大于60分，显示xx小时xx分，相隔大于24小时，显示xx天xx小时，相隔大于30天，显示yyyy年MM月dd日，
     * 时间格式化：
     * - xx分xx秒
     * - xx小时xx分
     * - xx天xx小时
     * - yyyy年MM月dd日
     *
     * @param beginTime
     * @param endTime
     * @return
     */
    public static String formatTimeDifference(LocalDateTime beginTime, LocalDateTime endTime) {
        long secondsBetween = ChronoUnit.SECONDS.between(beginTime, endTime);
        long minutesBetween = ChronoUnit.MINUTES.between(beginTime, endTime);
        long hoursBetween = ChronoUnit.HOURS.between(beginTime, endTime);
        long daysBetween = ChronoUnit.DAYS.between(beginTime, endTime);
        if (daysBetween > 30) {
            return beginTime.toLocalDate().toString();
        } else if (daysBetween > 0) {
            return daysBetween + "天前";
        } else if (hoursBetween > 0) {
            return hoursBetween + "小时前";
        } else if (minutesBetween > 0) {
            return minutesBetween + "分前";
        } else if (secondsBetween > 15) {
            return secondsBetween + "秒前";
        } else {
            return "刚刚";
        }
    }

    /**
     * 格式化游记发布时间
     * - 最近5分钟，展示刚发布；
     * - 最近30分钟，展示30min内发布；
     * - 最近60分钟，展示1hour内发布；
     * - 最近4小时，展示4hour内发布；
     * - 最近8小时，展示8hour内发布；
     * - 同年发布，展示为MM-DD发布；
     * - 其它时间，展示YYYY-MM-DD发布
     */
    public static String formatPublishTime(LocalDateTime publishTime, LocalDateTime endTime) {

        if (ObjectUtil.hasNull(publishTime, endTime)) return null;

        long minutesBetween = ChronoUnit.MINUTES.between(publishTime, endTime);
        long hoursBetween = ChronoUnit.HOURS.between(publishTime, endTime);

        int publishTimeYear = publishTime.getYear();
        int endTimeYear = endTime.getYear();

        if (minutesBetween < 5) {
            return "刚刚发布";
        } else if (minutesBetween < 30) {
            return "30min内发布";
        } else if (minutesBetween < 60) {
            return "1hour内发布";
        } else if (hoursBetween < 4) {
            return "4hour内发布";
        } else if (hoursBetween < 8) {
            return "8hour内发布";
        } else if (publishTimeYear == endTimeYear) {
            return LocalDateTimeUtil.format(publishTime, "MM-dd") + "发布";
        } else {
            return LocalDateTimeUtil.format(publishTime, "yyyy-MM-dd") + "发布";
        }
    }
}
