package com.somytrip.provider.community.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.somytrip.entity.community.Follows;
import com.somytrip.entity.dto.community.interaction.MyFansResp;
import com.somytrip.entity.dto.community.interaction.MyFollowsResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 关注信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-30
 */
@Mapper
public interface FollowsMapper extends BaseMapper<Follows> {

    /**
     * 更新用户粉丝、关注数
     *
     * @param userId
     * @param modifyDateTime
     */
    @Update("update community_user_info set fans_num = (SELECT count(*) from follows where following_id=#{userId} and flag=1 and del_flag=0)," +
            "follows_num = (SELECT count(*) from follows where follower_id=#{userId} and flag=1 and del_flag=0)" +
            ",update_time=#{modifyDateTime} where uid =#{userId}")
    void modifyUserFansFollowsNum(@Param("userId") Long userId, @Param("modifyDateTime") LocalDateTime modifyDateTime);

    /**
     * 我的粉丝列表
     *
     * @param userId
     * @param pageSize
     * @param offset
     * @return
     */
    List<MyFansResp> myFansList(@Param("userId") Long userId,
                                @Param("pageSize") Integer pageSize,
                                @Param("offset") Integer offset);

    /**
     * 我的关注列表
     *
     * @param userId
     * @param pageSize
     * @param offset
     * @return
     */
    List<MyFollowsResp> myFollowsList(@Param("userId") Long userId,
                                      @Param("pageSize") Integer pageSize,
                                      @Param("offset") Integer offset);
}
