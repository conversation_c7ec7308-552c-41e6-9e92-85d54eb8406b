package com.somytrip.provider.community.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.somytrip.entity.community.TravelNote;
import com.somytrip.entity.dto.community.TravelNoteInfoDto;
import com.somytrip.entity.dto.community.TravelNoteListDto;
import com.somytrip.entity.dto.community.TravelNoteQueryListParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 游记信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-30
 */
@Mapper
public interface TravelNotesMapper extends BaseMapper<TravelNote> {

    /**
     * 查询游记列表
     *
     * @param page  分页
     * @param param 查询参数
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.somytrip.entity.dto.community.TravelNoteListDto>
     * <AUTHOR>
     * @date 2024/12/5 14:21
     */
    IPage<TravelNoteListDto> queryList(IPage<TravelNoteListDto> page, @Param("param") TravelNoteQueryListParam param);

    /**
     * 查询游记封面信息
     *
     * @param travelNoteSn 游记序列号
     * @return com.somytrip.entity.dto.community.TravelNoteInfoDto
     * <AUTHOR>
     * @date 2024/12/5 17:24
     */
    TravelNoteInfoDto queryNoteDetailInfo(@Param("travelNoteSn") String travelNoteSn, @Param("uid") String uid);
}
