package com.somytrip.provider.community.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.somytrip.entity.community.Likes;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;

/**
 * <p>
 * 点赞信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-30
 */
@Mapper
public interface LikesMapper extends BaseMapper<Likes> {

    /**
     * 更新游记点赞数
     *
     * @param businessId
     * @param modifyDateTime
     */
    @Update("update travel_note_review set likes_num = (SELECT count(*) from likes where business_id=#{businessId} and business_type=1 and flag=1 and del_flag=0),update_time=#{modifyDateTime} where travel_id =#{businessId}")
    void modifyTravelNotesLikesNum(@Param("businessId") Long businessId, @Param("modifyDateTime") LocalDateTime modifyDateTime);

    /**
     * 更新评论点赞数
     *
     * @param businessId
     * @param modifyDateTime
     */
    @Update("update comments set like_num = (SELECT count(*) from likes where business_id=#{businessId} and business_type=2 and flag=1 and del_flag=0),update_time=#{modifyDateTime} where id =#{businessId}")
    void modifyCommentLikesNum(@Param("businessId") Long businessId, @Param("modifyDateTime") LocalDateTime modifyDateTime);
}
