package com.somytrip.provider.community.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.somytrip.entity.community.Messages;
import com.somytrip.entity.dto.community.message.ChatMessageReq;
import com.somytrip.entity.dto.community.message.ChatMessageRes;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 消息信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-30
 */
@Mapper
public interface MessagesMapper extends BaseMapper<Messages> {

    /**
     * 私信聊天框
     *
     * @param req
     * @return
     */
    List<ChatMessageRes> chatData(ChatMessageReq req);

    /**
     * 已读操作，更新统计数
     *
     * @param userId
     * @param localDateTime
     */
    @Update("""
                update messages set read_time = #{localDateTime},is_read = 1 where receiver_id = #{userId} and type=1
            """)
    void modifyMessageReadFlag(@Param("userId") Long userId,
                               @Param("localDateTime") LocalDateTime localDateTime);

    /**
     * 已读操作，更新统计数
     *
     * @param receiveUserId
     * @param sendUserId
     * @param localDateTime
     */
    @Update("""
                update messages set read_time = #{localDateTime},is_read = 1 where receiver_id = #{receiveUserId} and sender_id = #{sendUserId} and type =1
            """)
    void modifyMessageReadReceiveFlag(@Param("sendUserId") Long sendUserId,
                                      @Param("receiveUserId") Long receiveUserId,
                                      @Param("localDateTime") LocalDateTime localDateTime);

    /**
     * 用户未读消息统计数
     *
     * @param userId
     * @return
     */
    @Select("""
                select count(*) from messages where receiver_id = #{userId}  and type =1 and is_read = 0
            """)
    Integer unReadMsgTotal(@Param("userId") Long userId);
}
