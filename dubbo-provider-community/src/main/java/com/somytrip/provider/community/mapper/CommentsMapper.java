package com.somytrip.provider.community.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.somytrip.entity.community.Comments;
import com.somytrip.entity.dto.community.comment.CommentData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 评论信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-30
 */
@Mapper
public interface CommentsMapper extends BaseMapper<Comments> {

    /**
     * 评论列表数据
     *
     * @param lastCommentId
     * @param pageSize
     * @param userId
     * @param parentCommentId
     * @param businessId
     * @return
     */
    List<CommentData> commentList(@Param("lastCommentId") Long lastCommentId,
                                  @Param("pageSize") Integer pageSize,
                                  @Param("userId") Long userId,
                                  @Param("parentCommentId") Long parentCommentId,
                                  @Param("businessId") Long businessId);

    /**
     * 更新游记评论数
     *
     * @param businessId
     * @param modifyDateTime
     */
    @Update("update travel_note_review set comments_num = (SELECT count(*) from comments where business_id=#{businessId} and business_type=1 and del_flag=0),update_time=#{modifyDateTime} where travel_id =#{businessId}")
    void modifyCommentsNum(@Param("businessId") Long businessId, @Param("modifyDateTime") LocalDateTime modifyDateTime);
}
