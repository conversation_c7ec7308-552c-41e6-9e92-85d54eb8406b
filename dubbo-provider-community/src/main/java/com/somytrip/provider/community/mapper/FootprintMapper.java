package com.somytrip.provider.community.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.somytrip.entity.community.FootprintEntity;
import com.somytrip.entity.dto.community.footprint.FootprintCityDto;
import com.somytrip.entity.dto.community.footprint.FootprintQueryCitiesParam;
import com.somytrip.entity.resp.community.FootprintCountryListResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.provider.community.mapper
 * @interfaceName: FootprintMapper
 * @author: shadow
 * @description: 足迹Mapper
 * @date: 2024/12/31 10:33
 * @version: 1.0
 */
@Mapper
public interface FootprintMapper extends BaseMapper<FootprintEntity> {

    /**
     * 查询国家记录
     *
     * @param page 分页
     * @param uid  用户ID
     * @return com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.somytrip.entity.resp.community.FootprintCountryListResp.FootprintCountryVo>
     * <AUTHOR>
     * @date 2025/1/2 10:58
     */
    @Select("SELECT s.country_id, " +
            "       s.country         AS country_name, " +
            "       s.min_create_time AS record_time " +
            "FROM (SELECT gc.country_id, " +
            "             gc.country, " +
            "             MIN(fp.id)          AS min_fp_id, " +
            "             MIN(fp.create_time) AS min_create_time " +
            "      FROM footprints fp " +
            "               LEFT JOIN " +
            "           `oct_iri_db_tourism-ai`.global_city gc " +
            "           ON gc.id = fp.city_id " +
            "      WHERE fp.uid = #{uid} " +
            "        AND NOT fp.is_del " +
            "      GROUP BY gc.country_id) AS s " +
            "ORDER BY s.min_fp_id DESC")
    Page<FootprintCountryListResp.FootprintCountryVo> queryCountries(
            IPage<FootprintCountryListResp.FootprintCountryVo> page,
            @Param("uid") String uid
    );

    /**
     * 查询历时天数
     *
     * @param uid 用户ID
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 2025/1/2 10:58
     */
    @Select("SELECT DATEDIFF(CURRENT_DATE, create_time) " +
            "FROM footprints_setting " +
            "WHERE uid = #{uid} " +
            "LIMIT 1")
    Integer queryDurationDays(@Param("uid") String uid);

    /**
     * 查询城市记录
     *
     * @param param 查询参数
     * @return java.util.List<com.somytrip.entity.dto.community.footprint.FootprintCityDto>
     * <AUTHOR>
     * @date 2025/1/2 15:49
     */
    List<FootprintCityDto> queryCities(@Param("param") FootprintQueryCitiesParam param);

    /**
     * 查询城市记录总数
     *
     * @param param 查询参数
     * @return java.lang.Long
     * <AUTHOR>
     * @date 2025/1/2 17:36
     */
    Long queryCityTotal(@Param("param") FootprintQueryCitiesParam param);
}
