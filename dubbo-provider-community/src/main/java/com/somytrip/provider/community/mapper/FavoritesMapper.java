package com.somytrip.provider.community.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.somytrip.entity.community.Favorites;
import com.somytrip.entity.dto.community.interaction.MyFavoritesResp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 收藏信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-30
 */
@Mapper
public interface FavoritesMapper extends BaseMapper<Favorites> {


    /**
     * 更新游记收藏数
     *
     * @param businessId
     * @param modifyDateTime
     */
    @Update("update travel_note_review set favorites_num = (SELECT count(*) from favorites where business_id=#{businessId} and business_type=1 and flag=1 and del_flag=0),update_time=#{modifyDateTime} where travel_id =#{businessId}")
    void modifyTravelNotesFavoritesNum(@Param("businessId") Long businessId, @Param("modifyDateTime") LocalDateTime modifyDateTime);

    /**
     * 更新用户收藏数
     *
     * @param userId
     * @param modifyDateTime
     */
    @Update("update community_user_info set favorites_num = (SELECT count(*) from favorites where user_id=#{userId} and business_type=1 and flag=1 and del_flag=0),update_time=#{modifyDateTime} where uid =#{userId}")
    void modifyUserFavoritesNum(@Param("userId") Long userId, @Param("modifyDateTime") LocalDateTime modifyDateTime);

    /**
     * 我的收藏列表
     *
     * @param userId
     * @param pageSize
     * @param offset
     * @return
     */
    List<MyFavoritesResp> myFavoritesList(@Param("userId") Long userId,
                                          @Param("pageSize") Integer pageSize,
                                          @Param("offset") Integer offset);

    @Select("""
                SELECT
                	count(*)
                FROM
                	favorites f
                	LEFT JOIN travel_notes n ON f.business_id = n.id\s
                	AND f.business_type = 1
                	where f.user_id = #{userId} and f.flag=1 and n.is_del=0
            """)
    long countFavorites(@Param("userId") Long userId);
}
