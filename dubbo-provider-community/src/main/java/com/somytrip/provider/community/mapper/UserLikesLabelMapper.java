package com.somytrip.provider.community.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.somytrip.entity.community.UserLikesLabel;
import com.somytrip.entity.dto.community.user.LabelTotalDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 用户喜欢标签点击记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Mapper
public interface UserLikesLabelMapper extends BaseMapper<UserLikesLabel> {

    /**
     * 获取用户标签统计数
     *
     * @param userId
     * @return
     */
    List<LabelTotalDTO> getLabelTotalNum(@Param("userId") Long userId);
}
