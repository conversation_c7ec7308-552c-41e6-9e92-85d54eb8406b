package com.somytrip.entity.community;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.community
 * @className: FootprintEntity
 * @author: shadow
 * @description: 足迹实体类
 * @date: 2024/12/31 10:31
 * @version: 1.0
 */
@Data
@TableName(value = "footprints")
@NoArgsConstructor
@AllArgsConstructor
public class FootprintEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = -3252303886121511786L;

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private String uid;

    /**
     * 城市ID
     */
    private Integer cityId;

    /**
     * 是否删除
     */
    @TableField(value = "is_del")
    private Boolean del;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
