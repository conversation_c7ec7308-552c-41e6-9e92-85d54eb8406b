package com.somytrip.entity.community;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 游记景点信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-30
 */
@Data
@ToString
@TableName("travel_notes_scenic_spots")
public class TravelNotesScenicSpot implements Serializable {

    @Serial
    private static final long serialVersionUID = -5723270890723544834L;

    /**
     * 主键，自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 景点名称
     */
    private String scenicName;

    /**
     * 景点id
     */
    private Long scenicId;

    /**
     * 城市code
     */
    private String cityCode;

    /**
     * 游记id
     */
    private Long travelId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 景点评分
     */
    private Float rating;

    /**
     * 描述
     */
    private String description;

    /**
     * 是否删除
     */
    @TableField(value = "is_del")
    private Boolean del;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    private LocalDateTime createTime;

}
