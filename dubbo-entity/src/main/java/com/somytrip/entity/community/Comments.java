package com.somytrip.entity.community;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 评论信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-30
 */
@Data
@ToString
@TableName("comments")
@Builder
public class Comments implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id，自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户头像地址
     */
    private String userHeadPic;

    /**
     * 用户昵称
     */
    private String userNickname;

    /**
     * 是否作者，默认0,0-不是作者，1-作者
     */
    private Boolean isAuthor;

    /**
     * 评论ip地址
     */
    private String ipAddress;

    /**
     * 评论所在地城市名称
     */
    private String ipCityName;

    /**
     * 评论所在省名称
     */
    private String ipProvinceName;

    /**
     * 评论业务类型：1-游记
     */
    private Integer businessType;

    /**
     * 评论所属的业务id
     */
    private Long businessId;

    /**
     * 父评论的 ID，如果为 0则表示顶级评论
     */
    private Long parentId;

    /**
     * 评论内容
     */
    private String content;

    /**
     * 回复用户id
     */
    private Long replyUserId;

    /**
     * 回复用户头像地址
     */
    private String replyUserHeadPic;

    /**
     * 回复用户昵称
     */
    private String replyUserNickname;

    /**
     * 回复评论id
     */
    private Long replyId;

    /**
     * 评论点赞数
     */
    private Integer likeNum;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除标记，0-未删除，1-删除
     */
    private Boolean delFlag;

}
