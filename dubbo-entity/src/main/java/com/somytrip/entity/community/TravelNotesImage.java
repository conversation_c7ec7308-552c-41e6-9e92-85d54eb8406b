package com.somytrip.entity.community;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.somytrip.entity.FileInfo;
import lombok.Data;
import lombok.ToString;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 游记景点附件信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-30
 */
@Data
@ToString
@TableName("travel_notes_images")
public class TravelNotesImage implements Serializable {

    @Serial
    private static final long serialVersionUID = 5295353838652307176L;

    /**
     * 主键，自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联类型
     * 1-游记, 2-景点
     */
    private Integer joinType;

    /**
     * 关联ID
     */
    private Long joinId;

    /**
     * 图片状态，1-审核中，2-审核成功，3-审核失败
     */
    private Integer imageStatus;

    /**
     * 失败原因
     */
    private String reason;

    /**
     * 文件信息
     */
    @TableField(typeHandler = Fastjson2TypeHandler.class)
    private FileInfo fileInfo;

    /**
     * 附件类型，1-图片，2-视频
     */
    private Integer type;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    private LocalDateTime createTime;

}
