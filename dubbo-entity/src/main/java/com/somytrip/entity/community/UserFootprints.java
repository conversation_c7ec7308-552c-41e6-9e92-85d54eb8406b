package com.somytrip.entity.community;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 用户浏览足迹表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-30
 */
@Data
@ToString
@TableName("user_footprints")
public class UserFootprints implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键，自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 游记id
     */
    private Long travelId;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}
