package com.somytrip.entity.community;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.community
 * @className: TravelNoteReview
 * @author: shadow
 * @description: 游记评价
 * @date: 2024/12/4 17:30
 * @version: 1.0
 */
@Data
public class TravelNoteReview implements Serializable {

    @Serial
    private static final long serialVersionUID = 6339503759815787662L;

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 游记ID
     */
    private Long travelId;

    /**
     * 点赞数
     */
    private Long likesNum;

    /**
     * 转发数
     */
    private Long forwardNum;

    /**
     * 收藏数
     */
    private Long favoritesNum;

    /**
     * 评论数
     */
    private Long commentsNum;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
