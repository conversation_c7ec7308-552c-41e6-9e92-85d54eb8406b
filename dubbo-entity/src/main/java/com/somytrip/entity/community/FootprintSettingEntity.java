package com.somytrip.entity.community;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.somytrip.entity.dto.community.footprint.FootprintSettingEditParam;
import com.somytrip.entity.enums.community.FootprintMapType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.community
 * @className: FootprintSettingEntity
 * @author: shadow
 * @description: 足迹设置Entity
 * @date: 2025/1/2 14:17
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("footprints_setting")
public class FootprintSettingEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 7774242843223733031L;

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private String uid;

    /**
     * 是否开启足迹功能
     */
    @TableField(value = "is_enable")
    private Boolean enable;

    /**
     * 是否可访问
     */
    @TableField(value = "is_visible")
    private Boolean visible;

    /**
     * 地图类型
     */
    private FootprintMapType mapType;


    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public FootprintSettingEntity(FootprintSettingEditParam param) {
        this.uid = param.getUid();
        this.enable = param.getEnable();
        this.visible = param.getVisible();
        this.mapType = FootprintMapType.fromValue(param.getMapType());
    }
}
