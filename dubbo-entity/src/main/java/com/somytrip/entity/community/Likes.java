package com.somytrip.entity.community;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 点赞信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-30
 */
@Data
@ToString
@Builder
@TableName("likes")
public class Likes implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id，自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 点赞业务类型：1-游记，2-评论
     */
    private Integer businessType;

    /**
     * 点赞所属的业务id
     */
    private Long businessId;

    /**
     * 点赞标记，1-点赞，2-取消点赞
     */
    private Integer flag;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除标记，0-未删除，1-删除
     */
    private Boolean delFlag;
}
