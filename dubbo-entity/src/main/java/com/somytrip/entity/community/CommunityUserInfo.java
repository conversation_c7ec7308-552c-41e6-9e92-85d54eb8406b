package com.somytrip.entity.community;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.community
 * @className: CommunityUserInfo
 * @author: shadow
 * @description: 社区用户信息
 * @date: 2024/12/4 17:32
 * @version: 1.0
 */

@ToString
@Data
@TableName("community_user_info")
public class CommunityUserInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long uid;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 头像存储桶名称
     */
    private String avatarBucketName;

    /**
     * 头像名称
     */
    private String avatarFileName;

    /**
     * 城市code
     */
    private String cityCode;

    /**
     * 搜旅号
     */
    private String smtNo;

    /**
     * -1 未知 0 女 1 男
     */
    private Integer gender;

    /**
     * 生日
     */
    private LocalDate birthday;

    /**
     * 职业
     */
    @TableField(typeHandler = Fastjson2TypeHandler.class)
    private List<String> careers;

    /**
     * 粉丝数
     */
    private Integer fansNum;


    /**
     * 足迹数
     */
    private Integer footprintsNum;

    /**
     * 关注数
     */
    private Integer followsNum;

    /**
     * 收藏数
     */
    private Integer favoritesNum;

    /**
     * 成团数
     */
    private Integer groupedNum;

    /**
     * 自我介绍
     */
    private String selfIntroduction;

    /**
     * 评分
     */
    private BigDecimal rating;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 年龄标签是否展示，true-展示，false-不展示
     */
    private Boolean ageDisplayFlag;
    /**
     * 地区标签是否展示，true-展示，false-不展示
     */
    private Boolean areaDisplayFlag;

    /**
     * 生日标签是否展示，true-展示，false-不展示
     */
    private Boolean birthdayDisplayFlag;

    /**
     * 职业标签是否展示，true-展示，false-不展示
     */
    private Boolean careersDisplayFlag;

    /**
     * 星座标签是否展示，true-展示，false-不展示
     */
    private Boolean constellationDisplayFlag;

    /**
     * 年龄信息是否展示标志，true-展示，false-不展示
     */
    private Boolean genderDisplayFlag;

    /**
     * 修改搜旅号时间
     */
    private LocalDateTime updateSmtTime;
}
