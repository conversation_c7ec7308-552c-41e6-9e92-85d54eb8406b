package com.somytrip.entity.community;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 用户标签信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@ToString
@Data
@TableName("user_label")
public class UserLabel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id，自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 新增标签操作用户id
     */
    private Long operationUserId;

    /**
     * 标签占比
     */
    private BigDecimal labelPercentage;

    /**
     * 用户喜欢数
     */
    private Integer likeNum;

    /**
     * 标签名称
     */
    private String labelName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除标记，0-未删除，1-删除
     */
    private Boolean delFlag;

}
