package com.somytrip.entity.community;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.somytrip.entity.FileInfo;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 举报信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-30
 */
@Builder
@Data
@ToString
@Accessors(chain = true)
@TableName(value = "message_reports", autoResultMap = true)
public class MessageReports implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键，自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 提交举报的用户ID
     */
    private Long reporterId;

    /**
     * 被举报的账号用户id
     */
    private Long reportedAccountId;

    /**
     * 举报理由原因
     */
    private String reportReason;

    /**
     * 举报状态：1-提交举报信息，2-已处理，3-驳回
     */
    private Integer reportHandlerStatus;

    /**
     * 聊天证据
     */
    private String chatEvidence;

    /**
     * 举报描述
     */
    private String description;

    private String reportBusinessId;

    private Integer reportType;

    /**
     * 图片材料提交，以JSON格式存储多个图片URL或其他相关信息
     */
    @TableField(typeHandler = Fastjson2TypeHandler.class)
    private List<FileInfo> imageMaterials;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    private LocalDateTime createTime;

    /**
     * 删除标记，0-未删除，1-删除
     */
    private Boolean delFlag;
}
