package com.somytrip.entity.community;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 用户喜欢标签点击记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-20
 */
@Data
@ToString
@TableName("user_likes_label")
public class UserLikesLabel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id，自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 标签id
     */
    private Long labelId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 操作用户id
     */
    private Long operationUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除标记，0-未删除，1-删除
     */
    private Boolean delFlag;

}
