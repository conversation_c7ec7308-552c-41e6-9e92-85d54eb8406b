package com.somytrip.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity
 * @className: FileInfo
 * @author: shadow
 * @description: 文件
 * @date: 2024/12/4 17:25
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FileInfo {

    /**
     * 桶
     */
    private String bucket;

    /**
     * 名称
     */
    private String name;

    /**
     * url
     */
    private String url;
}
