package com.somytrip.entity.hotel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.somytrip.entity.enums.hotel.HotelOrigin;
import lombok.Data;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.hotel
 * @className: HotelLocaleIntroEditorEntity
 * @author: shadow
 * @description: 酒店介绍本地化entity
 * @date: 2024/10/16 9:32
 * @version: 1.0
 */
@Data
@TableName("hotel_locale_intro_editor")
public class HotelLocaleIntroEditorEntity {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 酒店主键ID
     */
    private Long hotelPid;

    /**
     * 酒店ID
     */
    private String hotelId;

    /**
     * 酒店来源
     */
    private HotelOrigin hotelOrigin;

    /**
     * 语种
     */
    private String locale;

    /**
     * 当前语种值
     */
    private String value;
}
