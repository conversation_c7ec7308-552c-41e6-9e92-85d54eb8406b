package com.somytrip.entity.hotel.WEBBEDS;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName: WEBBEDSCitiesResp
 * @Description: WEBBEDS城市列表响应
 * @Author: shadow
 * @Date: 2024/3/19 2:20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WEBBEDSCitiesResp {

    /**
     * 城市列表
     */
    private List<City> cities;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class City {

        /**
         * 行政区列表
         */
        @JsonProperty("districts")
        List<District> districts;
        /**
         * 城市ID
         */
        @JsonProperty("id")
        private Integer id;
        /**
         * 城市名称
         */
        @JsonProperty("name")
        private String name;
        /**
         * 城市代码
         */
        @JsonProperty("code")
        private String code;
        /**
         * 省份ID
         */
        @JsonProperty("province_id")
        private Integer provinceId;
        /**
         * 省份名称
         */
        @JsonProperty("province_name")
        private String provinceName;

        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class District {

            /**
             * 行政区ID
             */
            private Integer id;

            /**
             * 行政区名称
             */
            private String name;
        }
    }
}
