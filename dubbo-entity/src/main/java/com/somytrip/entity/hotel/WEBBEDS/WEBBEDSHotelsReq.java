package com.somytrip.entity.hotel.WEBBEDS;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @ClassName: WEBBEDSHotelsReq
 * @Description: WEBBEDS酒店列表请求参数
 * @Author: shadow
 * @Date: 2024/3/19 2:44
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WEBBEDSHotelsReq extends WEBBEDSBaseReq {

    /**
     * 城市ID
     * 与hotel_ids二选一
     */
    @JsonProperty("city_id")
    private Integer cityId;

    /**
     * 酒店ID列表
     * 与city_id二选一
     */
    @JsonProperty("hotel_ids")
    private List<Integer> hotelIds;
}
