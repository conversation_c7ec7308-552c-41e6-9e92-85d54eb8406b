package com.somytrip.entity.hotel.ELong;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/**
 * @ClassName: ELongHotelOrderPayResp
 * @Description: 同程艺龙酒店订单支付响应
 * @Author: shadow
 * @Date: 2024/2/24 14:48
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ELongHotelOrderPayResp {

    /**
     * 支付请求是否接收成功
     * 该字段表示艺龙分销系统是否成功接收到了支付请求，不代表支付成功
     * 是否支付成功请查看：http://open.elong.com/faq/detail?plt=2&id=121
     */
    @NotNull
    @JsonProperty("IsSuccess")
    private Boolean isSuccess;

    /**
     * 备注(失败原因)
     */
    @Nullable
    @JsonProperty("notes")
    private String notes;
}
