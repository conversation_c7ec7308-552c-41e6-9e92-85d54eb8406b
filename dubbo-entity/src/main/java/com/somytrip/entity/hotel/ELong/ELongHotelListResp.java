package com.somytrip.entity.hotel.ELong;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.somytrip.entity.enums.hotel.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName: ELongHotelListResp
 * @Description: 同程艺龙酒店列表搜索响应
 * @Author: shadow
 * @Date: 2024/3/1 11:16
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ELongHotelListResp {

    /**
     * 查询到的酒店总数
     */
    @Nullable
    @JsonProperty("Count")
    private Integer count = 0;

    /**
     * 酒店结果集
     */
    @Nullable
    @JsonProperty("Hotels")
    private List<Hotel> hotels;

    /**
     * 汇率信息
     */
    @Nullable
    @JsonProperty("ExchangeRateList")
    private List<ExchangeRate> exchangeRates;

    /**
     * Hotel 节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Hotel {

        /**
         * 酒店编号
         */
        @NotNull
        @JsonProperty("HotelId")
        private String hotelId;

        /**
         * 最低价格
         * 返回的是该酒店中所有符合查询条件的产品的最低价
         */
        @NotNull
        @JsonProperty("LowRate")
        private BigDecimal lowRate;

        /**
         * 最低价格的货币
         * 参考Currency
         */
        @Nullable
        @JsonProperty("CurrencyCode")
        private String currencyCode;

        /**
         * 预定规则
         * 该酒店下所有的预订规则。应在订单填写页面提示给用户，也可以做到系统规则中约束用户的选择或输入。
         * 包含多个 BookingRule 节点
         */
        @Nullable
        @JsonProperty("BookingRules")
        private List<BookingRule> bookingRules;

        /**
         * 担保规则
         * 出现规则即表示需要担保。当 isTimeGuarantee 和 isAmountGuarantee 都等于 false 时表示无条件强制担保
         * 包含多个 GuaranteeRule 节点
         */
        @Nullable
        @JsonProperty("GuaranteeRules")
        private List<GuaranteeRule> guaranteeRules;

        /**
         * 预付规则
         * 包含多个 PrepayRule 节点
         */
        @Nullable
        @JsonProperty("PrepayRules")
        private List<PrepayRule> prepayRules;

        /**
         * 新担保规则
         * 包含多个 GuaranteeRuleExtend 节点
         */
        @Nullable
        @JsonProperty("GuaranteeRuleExtends")
        private List<GuaranteeRuleExtend> guaranteeRuleExtends;

        /**
         * 新预付规则
         * 包含多个 PrepayRuleExtend 节点
         */
        @Nullable
        @JsonProperty("PrepayRuleExtends")
        private List<PrepayRuleExtend> prepayRuleExtends;

        /**
         * 增值服务
         * 包含多个 ValueAdd 节点
         */
        @Nullable
        @JsonProperty("ValueAdds")
        private List<ValueAdd> valueAdds;

        /**
         * 促销规则
         * 搜索接口(list和detail)中的价格都经过了这些规则的计算，仅需将规则的描述提示用户即可。
         * 包含多个 DrrRule 节点
         */
        @Nullable
        @JsonProperty("DrrRules")
        private List<DrrRule> drrRules;

        /**
         * 酒店设置
         * 1 免费wifi
         * 2 收费wifi
         * 3 免费宽带
         * 4 收费宽带
         * 5 免费停车场
         * 6 收费停车场
         * 7 免费接机服务
         * 8 收费接机服务
         * 9 室内游泳池
         * 10 室外游泳池
         * 11 健身房
         * 12 商务中心
         * 13 会议室
         * 14 酒店餐厅
         * 15 叫醒服务
         * 16 行李寄存
         * 17 双床
         * 18 大床
         * 以上是本接口使用的简略设施信息，与静态信息中获取的设施id并不对应。
         */
        @Nullable
        @JsonProperty("Facilities")
        private String facilities;

        /**
         * 距离
         * 距离搜索的时候有值
         */
        @Nullable
        @JsonProperty("Distance")
        private BigDecimal distance;

        /**
         * 距离对应的参照物
         * V1.14新增。如果有值应该展示告诉用户Distance计算时候的参照点
         */
        @Nullable
        @JsonProperty("PoiName")
        private String poiName;

        /**
         * 房型列表
         * 包含多个 Room 节点
         */
        @Nullable
        @JsonProperty("Rooms")
        private List<Room> rooms;

        /**
         * 酒店信息
         * 参考 Detail 节点，ResultType 字段包含3时出现
         */
        @Nullable
        @JsonProperty("Detail")
        private Detail detail;

        /**
         * 送礼活动
         * 包含多个 Gift 节点
         */
        @Nullable
        @JsonProperty("Gifts")
        private List<Gift> gifts;

        /**
         * 礼包套餐
         * 包含多个 GiftPackage 节点
         */
        @Nullable
        @JsonProperty("GiftPackages")
        private List<GiftPackage> giftPackages;

        /**
         * 酒店特殊信息提示
         * 包含多个 HAvailPolicy 节点
         * V1.04新增。请把此信息展示给用户，以便用户预订
         */
        @Nullable
        @JsonProperty("HAvailPolicys")
        private List<HAvailPolicy> hAvailPolicys;

//        /**
//         * 简易产品信息
//         * V1.14新增。参见 Product
//         * 该字段目前不建议使用，产品信息请使用 Rooms 节点的 RatePlans 节点
//         */
//        @Nullable
//        @JsonProperty("Products")
//        private Item<Product> products;

        /**
         * 酒店特惠信息
         * V1.30新增，多个值以逗号分隔，可能出现以下值：
         * BENEFIT-优惠
         * CASHBACK-返现
         * SPECIAL_DISCOUNT-立减优惠
         * SALES_PROMOTION-促销
         * DAILY_SPECIAL-今夜甩卖
         */
        @Nullable
        @JsonProperty("HotelFlags")
        private List<String> hotelFlags;

        /**
         * 好评分数
         */
        @Nullable
        @JsonProperty("PraiseScore")
        private BigDecimal praiseScore;

        /**
         * BookingRule 节点
         */
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class BookingRule {

            /**
             * 规则类型
             * NeedNationality：务必提供客人国籍
             * PerRoomPerName：您预订了N间房，请您提供不少于N的入住客人姓名
             * ForeignerNeedEnName：此酒店要求外宾务必留英文拼写 ，港澳台酒店出现这个字段的时候，所有人都需要填写英文名或姓名拼音
             * RejectCheckinTime：几点到几点酒店不接受预订 , 此处校验的是下单时的当前时间
             * NeedPhoneNo：务必提供联系人手机号(请加在联系人结点Contact上)
             */
            @NotNull
            @JsonProperty("TypeCode")
            private String typeCode;

            /**
             * 预订规则编号
             * RatePlan.BookingRuleIds将与此关联
             */
            @Nullable
            @JsonProperty("BookingRuleId")
            private String bookingRuleId;

            /**
             * 描述
             */
            @NotNull
            @JsonProperty("Description")
            private String description;

            /**
             * 日期类型
             * BookDay –预订日期（订单的创建日期）
             */
            @Nullable
            @JsonProperty("DateType")
            private String dateType;

            /**
             * 开始日期
             */
            @Nullable
            @JsonProperty("StartDate")
            private String startDate;

            /**
             * 结束日期
             */
            @Nullable
            @JsonProperty("EndDate")
            private String endDate;

            /**
             * 每天开始时间
             * 针对日期段内每天生效, 当TypeCode 为RejectCheckinTime时表示当前预订时间在StartHour到EndHour区间内酒店不接受预订。
             * 当EndHour大于24点的时候是表示第二天的几点加上了24小时，如26:00表示第二天的2点。
             */
            @Nullable
            @JsonProperty("StartHour")
            private String startHour;

            /**
             * 每天结束时间
             */
            @Nullable
            @JsonProperty("EndHour")
            private String endHour;
        }

        /**
         * GuaranteeRule 节点
         */
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class GuaranteeRule {

            /**
             * 担保规则编号
             */
            @NotNull
            @JsonProperty("GuranteeRuleId")
            private Long guaranteeRuleId;

            /**
             * 描述
             */
            @NotNull
            @JsonProperty("Description")
            private String description;

            /**
             * 日期类型
             */
            @NotNull
            @JsonProperty("DateType")
            private DateType dateType;

            /**
             * 开始日期
             * 举例：DateType为CheckInDay：表示当前订单的入住日期落在StartDate和EndDate之间，
             * 并且入住日期符合周设置时才需要判断其它条件是否担保，否则不需要担保
             */
            @NotNull
            @JsonProperty("StartDate")
            private String startDate;

            /**
             * 结束日期
             * 举例：DateType为CheckInDay：表示当前订单的入住日期落在StartDate和EndDate之间，
             * 并且入住日期符合周设置时才需要判断其它条件是否担保，否则不需要担保
             */
            @NotNull
            @JsonProperty("EndDate")
            private String endDate;

            /**
             * 周有效天数， 一般为周一到周日都有效， 判断日期符合日期段同时也要满足周设置的有效
             * 周一对应为1，周二对应为2， 依次类推;逗号分隔
             * 为空时表示无周末设置
             * DateType为StayDay：表示当前订单的客人只要有住在店里面的日期（ArrivalDate,DepartureDate）落在StartDate和EndDate之间，
             * 并且入住日期符合周设置时才需要判断其它条件是否担保，否则不需要担保
             */
            @Nullable
            @JsonProperty("WeekSet")
            private String weekSet;

            /**
             * 是否到店时间担保
             * False:为不校验到店时间
             * True:为需要校验到店时间
             * 此字段与之后的IsAmountGuarantee字段用法比较特殊，请仔细阅读注意事项中关于这两个字段的说明。
             */
            @NotNull
            @JsonProperty("IsTimeGuarantee")
            private Boolean isTimeGuarantee;

            /**
             * 到店担保开始时间
             * 用于IsTimeGuarantee==true进行检查。
             */
            @Nullable
            @JsonProperty("StartTime")
            private String startTime;

            /**
             * 到店担保结束时间
             * 当EndTime小于StartTime的时候，默认从StartTime到次日6点都需要担保。
             */
            @Nullable
            @JsonProperty("EndTime")
            private String endTime;

            /**
             * 到店担保的结束时间是否为第二天
             * 0为当天，1为次日
             */
            @Nullable
            @JsonProperty("IsTomorrow")
            private Boolean isTomorrow;

            /**
             * 是否房量担保
             * False:为不校验房量条件
             * True:为校验房量条件
             */
            @NotNull
            @JsonProperty("IsAmountGuarantee")
            private Boolean isAmountGuarantee;

            /**
             * 担保的房间数,预定几间房以上要担保
             * 用于IsAmountGuarantee==true进行检查
             */
            @Nullable
            @JsonProperty("Amount")
            private Integer amount;

            /**
             * 担保类型
             * FirstNightCost为首晚房费担保
             * FullNightCost为全额房费担保
             */
            @Nullable
            @JsonProperty("GuaranteeType")
            private String guaranteeType;

            /**
             * 变更规则
             * 担保规则取消变更规则：
             * NoChange、不允许变更取消
             * NeedSomeDay、允许变更/取消,需在XX日YY时之前通知
             * NeedCheckinTime、允许变更/取消,需在最早到店时间之前几小时通知
             * NeedCheckin24hour、允许变更/取消,需在到店日期的24点之前几小时通知
             */
            @Nullable
            @JsonProperty("ChangeRule")
            private GuaranteeChangeRule changeRule;

            /**
             * 日期参数
             * ChangeRule=NeedSomeDay时，对应规则2描述中 “允许变更/取消,需在XX日YY时之前通知” 中的XX日，YY时
             */
            @Nullable
            @JsonProperty("Day")
            private String day;

            /**
             * 时间参数
             * ChangeRule=NeedSomeDay时，对应规则2描述中 “允许变更/取消,需在XX日YY时之前通知” 中的XX日，YY时
             */
            @Nullable
            @JsonProperty("Time")
            private String time;

            /**
             * 小时参数
             * ChangeRule=NeedCheckinTime时，对应规则3描述中 “ 允许变更/取消,需在最早到店时间之前几小时通知” 中的几小时
             * ChangeRule=NeedCheckin24hour时，对应规则4描述中“ 允许变更/取消,需在到店日期的24点之前几小时通知” 中的几小时
             */
            @Nullable
            @JsonProperty("Hour")
            private Integer hour;
        }

        /**
         * PrepayRule 节点
         */
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class PrepayRule {

            /**
             * 规则编号
             */
            @NotNull
            @JsonProperty("PrepayRuleId")
            private Long prepayRuleId;

            /**
             * 描述
             */
            @NotNull
            @JsonProperty("Description")
            private String description;

            /**
             * 日期类型
             * CheckInDay：入住日期（该字段后期下线，可以不用判断）
             */
            @NotNull
            @JsonProperty("DateType")
            private String dateType;

            /**
             * 开始日期
             * 使用离线数据模式需要判断
             */
            @Nullable
            @JsonProperty("StartDate")
            private String startDate;

            /**
             * 结束日期
             * 使用离线数据模式需要判断
             */
            @Nullable
            @JsonProperty("EndDate")
            private String endDate;

            /**
             * 周有效设置
             * 使用离线数据模式需要判断
             */
            @Nullable
            @JsonProperty("WeekSet")
            private String weekSet;

            /**
             * 变更规则
             */
            @NotNull
            @JsonProperty("ChangeRule")
            private PrepayChangeRule changeRule;

            /**
             * 第一阶段提前的几小时
             * 用于PrepayNeedSomeDay
             */
            @Nullable
            @JsonProperty("Hour")
            private Integer hour;

            /**
             * 第二阶段提前的几小时
             * 用于PrepayNeedSomeDay
             */
            @Nullable
            @JsonProperty("Hour2")
            private Integer hour2;

            /**
             * 具体取消时间日期部分
             * 用于PrepayNeedOneTime
             */
            @Nullable
            @JsonProperty("DateNum")
            private String dateNum;

            /**
             * 具体取消时间小时部分
             * 用于PrepayNeedOneTime
             */
            @Nullable
            @JsonProperty("Time")
            private String time;

            /**
             * 在变更时间点前是否扣费
             * 用于 PrepayNeedSomeDay的Hour前扣款类型（一般不收罚金）。DeductFeesBefore为1表示扣款，0表示不扣款。
             */
            @Nullable
            @JsonProperty("DeductFeesBefore")
            private Integer deductFeesBefore;

            /**
             * 时间点前扣费的金额或比例
             * 用于 PrepayNeedSomeDay的Hour前扣款类型（一般不收罚金）。DeductFeesBefore为1表示扣款，0表示不扣款。
             */
            @Nullable
            @JsonProperty("DeductNumBefore")
            private BigDecimal DeductNumBefore;

            /**
             * 时间点后扣款类型
             */
            @Nullable
            @JsonProperty("CashScaleFirstAfter")
            private PrepayCashType CashScaleFirstAfter;

            /**
             * 在变更时间点后是否扣费
             * 用于 PrepayNeedSomeDay的Hour到Hour2之间的扣款类型。DeductFeesAfter为1表示扣款，0表示不扣款。
             * 如果CashScaleFirstAfter为FristNight，则返回-1，没有意义
             */
            @Nullable
            @JsonProperty("DeductFeesAfter")
            private Integer deductFeesAfter;

            /**
             * 时间点后扣费的金额或比例
             * 用于 PrepayNeedSomeDay的Hour到Hour2之间的扣款类型。DeductFeesAfter为1表示扣款，0表示不扣款。
             * 如果CashScaleFirstAfter为FristNight，则返回-1，没有意义
             */
            @Nullable
            @JsonProperty("DeductNumAfter")
            private BigDecimal deductNumAfter;

            /**
             * 时间点前扣款类型
             */
            @Nullable
            @JsonProperty("CashScaleFirstBefore")
            private PrepayCashType cashScaleFirstBefore;
        }

        /**
         * PenaltyWindowType 节点
         */
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class PenaltyWindowType {

            /**
             * 扣款类型
             * 0:百分比 1:晚数  2:首晚百分比
             */
            @NotNull
            @JsonProperty("PenaltyType")
            private Integer penaltyType;

            /**
             * 罚金
             * PenaltyType 为 0,2时，此值为两位小数。
             * PenaltyType  为1是，此值使用时会取整，按整数处理。
             */
            @NotNull
            @JsonProperty("PenaltyValue")
            private BigDecimal penaltyValue;

            /**
             * 规则时间分割起始点
             * 单位分钟，第一个点为1439280
             */
            @NotNull
            @JsonProperty("Deadline")
            private String deadline;
        }

        /**
         * GuaranteeRuleExtend节点
         */
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class GuaranteeRuleExtend {

            /**
             * 规则ID
             */
            @Nullable
            @JsonProperty("GuaranteeRuleExtendId")
            private Integer guaranteeRuleExtendId;

            /**
             * 开始时间
             * 按入住日匹配，入住日在开始时间和结束时间之间，且符合周有效规则，即为命中此规则
             */
            @Nullable
            @JsonProperty("StartDate")
            private String startDate;

            /**
             * 结束时间
             */
            @Nullable
            @JsonProperty("EndDate")
            private String endDate;

            /**
             * 周有效设置
             */
            @Nullable
            @JsonProperty("WeekSet")
            private String weekSet;

            /**
             * 担保类型
             * 0:需担保 1:无需担保 2:超时担保
             */
            @Nullable
            @JsonProperty("GuaranteeType")
            private Integer guaranteeType;

            /**
             * 取消费用类型
             * 0:跟随取消费用 1:订单全额 计算担保金额用，0时取PenaltyRuleList计算出的罚金列表中罚金的最大值
             */
            @Nullable
            @JsonProperty("NoshowPenalty")
            private Integer noShowPenalty;

            /**
             * 超时担保时间
             * 单位分钟，相对入住日24点的小时偏移量, 范围[0,840]
             */
            @Nullable
            @JsonProperty("GrtLatestCheckTime")
            private Integer grtLatesCheckTime;

            /**
             * 取消规则列表
             * 解析示例: https://open.elong.com/doc/info/cn-api-meta-hotel_data_rp#RuleExtend%E8%A7%A3%E6%9E%90
             */
            @Nullable
            @JsonProperty("PenaltyRuleList")
            private List<PenaltyWindowType> penaltyRuleList;
        }

        /**
         * PrepayRuleExtend节点
         */
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class PrepayRuleExtend {

            /**
             * 规则ID
             */
            @Nullable
            @JsonProperty("PrepayRuleExtendId")
            private Integer prepayRuleExtendId;

            /**
             * 开始时间
             * 按入住日匹配，入住日在开始时间和结束时间之间，且符合周有效规则，即为命中此规则
             */
            @NotNull
            @JsonProperty("StartDate")
            private String startDate;

            /**
             * 结束时间
             */
            @NotNull
            @JsonProperty("EndDate")
            private String endDate;

            /**
             * 周有效设置
             */
            @NotNull
            @JsonProperty("WeekSet")
            private String weekSet;

            /**
             * 取消费用类型
             * 0:跟随取消费用 1:订单全额（目前只有0）
             */
            @Nullable
            @JsonProperty("NoshowPenalty")
            private Integer noShowPenalty;

            /**
             * 取消规则列表
             * 解析示例: https://open.elong.com/doc/info/cn-api-meta-hotel_data_rp#RuleExtend%E8%A7%A3%E6%9E%90
             */
            @NotNull
            @JsonProperty("PenaltyRuleList")
            private List<PenaltyWindowType> penaltyRuleList;
        }

        /**
         * ValueAdd 节点
         */
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class ValueAdd {

            /**
             * 业务代码
             * 01-早餐
             * 02-午餐
             * 03-晚餐
             * 04-宽带上网
             * 05-服务费
             * 06-政府税
             * 99-特殊早餐，有效日期内生效，优先级高于01早餐
             * 当99特殊早餐和01早餐同时存在时，需要根据特殊早餐的有效日期判断哪种早餐生效，
             * 即在特殊早餐有效日期内99特殊早餐生效，有效日期外01早餐生效。
             */
            @NotNull
            @JsonProperty("TypeCode")
            private String typeCode;

            /**
             * 描述
             * 附加服务描述，代理不想解析的话，可以直接显示该描述
             */
            @NotNull
            @JsonProperty("Description")
            private String description;

            /**
             * 是否包含在房费中
             * false-不包含 true-包含，例如业务代码为早餐时，false即为不含早，true为含早
             */
            @NotNull
            @JsonProperty("IsInclude")
            private Boolean isInclude;

            /**
             * 包含的份数
             */
            @Nullable
            @JsonProperty("Amount")
            private Integer amount;

            /**
             * 货币代码
             */
//                @NotNull
            @Nullable
            @JsonProperty("CurrencyCode")
            // TODO: 枚举
            private String currencyCode;

            /**
             * 单价默认选项
             */
            @Nullable
            @JsonProperty("PriceOption")
            private PriceOption priceOption;

            /**
             * 单价
             * 视PriceOption表示金额或比例，比例值保存的百分数，不是最终的小数，例如 20%，则该字段保存为20
             */
            @Nullable
            @JsonProperty("Price")
            private BigDecimal price;

            /**
             * 是否单加
             * 目前只有早餐服务该字段有意义
             */
            @Nullable
            @JsonProperty("IsExtAdd")
            private Boolean isExtAdd;

            /**
             * 单加单价默认选项
             */
            @NotNull
            @JsonProperty("ExtOption")
            private PriceOption extOption;

            /**
             * 单加单价
             * 视 extOption 不同表示金额或比例值, 比例值保存的百分数，不是最终的小数 例如 20%， 则该字段保存为20
             */
            @Nullable
            @JsonProperty("ExtPrice")
            private BigDecimal extPrice;

            /**
             * 开始日期
             * 特殊早餐有效日期
             */
            @Nullable
            @JsonProperty("StartDate")
            private String startDate;

            /**
             * 结束日期
             * 特殊早餐有效日期
             */
            @Nullable
            @JsonProperty("EndDate")
            private String endDate;

            /**
             * 周有效设置
             * 特殊早餐有效日期
             */
            @Nullable
            @JsonProperty("WeekSet")
            private String weekSet;
        }

        /**
         * DrrRule 节点
         */
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class DrrRule {

            /**
             * 促销规则编号
             */
            @NotNull
            @JsonProperty("DrrRuleId")
            private String drrRuleId;

            /**
             * 产品促销规则类型代码
             */
            @NotNull
            @JsonProperty("TypeCode")
            private DRRTypeCode typeCode;

            /**
             * 描述
             */
            @Nullable
            @JsonProperty("Description")
            private String description;

            /**
             * 日期类型
             */
            @NotNull
            @JsonProperty("DateType")
            private DateType dateType;

            /**
             * 促销生效开始日期
             */
            @Nullable
            @JsonProperty("StartDate")
            private String startDate;

            /**
             * 促销生效结束日期
             */
            @Nullable
            @JsonProperty("EndDate")
            private String EndDate;

            /**
             * 提前几天
             */
            @Nullable
            @JsonProperty("DayNum")
            private Integer dayNum;

            /**
             * 连住几天
             */
            @Nullable
            @JsonProperty("CheckInNum")
            private Integer checkInNum;

            /**
             * 每连住几晚
             */
            @Nullable
            @JsonProperty("EveryCheckInNum")
            private Integer everyCheckInNum;

            /**
             * 最后几天
             */
            @Nullable
            @JsonProperty("LastDayNum")
            private Integer lastDayNum;

            /**
             * 第几晚及以后优惠
             */
            @Nullable
            @JsonProperty("WhichDayNum")
            private Integer whichDayNum;

            /**
             * 按金额或按比例来优惠
             * Cash-金额 Scale-比例
             */
            @Nullable
            @JsonProperty("CashScale")
            private CashScale cashScale;

            /**
             * 按金额或比例优惠的数值
             * 当CashScale为Percent时，该值保存的为百分数，例如30%
             */
            @Nullable
            @JsonProperty("DeductNum")
            private BigDecimal deductNum;

            /**
             * 星期有效设置
             * 日期符合Weekset中的周设置，才享受 feetype所对应的价格
             * 仅DRRStayWeekDay和DRRCheckInWeekDay的时候使用
             */
            @Nullable
            @JsonProperty("WeekSet")
            private String weekSet;

            /**
             * 关联的房型
             */
            @Nullable
            @JsonProperty("FeeType")
            private FeeType feeType;
        }

        /**
         * Room 节点
         */
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class Room {

            /**
             * 展示房型编码
             * 仅用于关联静态信息
             */
            @NotNull
            @JsonProperty("RoomId")
            private String roomId;

            /**
             * 房型名称
             */
            @NotNull
            @JsonProperty("Name")
            private String name;

            /**
             * 房型英文名称
             */
            @NotNull
            @JsonProperty("NameEn")
            private String nameEn;

            /**
             * 产品信息
             */
            @Nullable
            @JsonProperty("RatePlans")
            private List<RatePlan> ratePlans;

//            /**
//             * RatePlan 节点
//             */
//            @Data
//            @NoArgsConstructor
//            @AllArgsConstructor
//            public static class RatePlan1 {
//
//                /**
//                 * 产品编号
//                 */
//                @NotNull
//                @JsonProperty("RatePlanId")
//                private Long ratePlanId;
//
//                /**
//                 * 产品名称
//                 */
//                @NotNull
//                @JsonProperty("RatePlanName")
//                private String ratePlanName;
//
//                /**
//                 * 销售状态
//                 * 该产品产品、价格是否有效的状态，这里为false就表示这个产品不能卖了。
//                 * 如果为true，那么还需要依次判断NightlyRate数组中每个节点的状态，只有这些全部都为true，这个产品才可售。
//                 * false--不可销售（可能是产品无效、部分日期缺少价格）
//                 * true--可销售
//                 */
//                @NotNull
//                @JsonProperty("Status")
//                private Boolean status;
//
//                /**
//                 * 销售房型编号
//                 * 用于hotel.order.create中的入参RoomTypeId
//                 */
//                @Nullable
//                @JsonProperty("RoomTypeId")
//                private String roomTypeId;
//
//                /**
//                 * 供应商房型附加名称
//                 * 房型信息的补充说明
//                 */
//                @Nullable
//                @JsonProperty("SuffixName")
//                private String suffixName;
//
//                /**
//                 * 供应商酒店编码
//                 */
//                @NotNull
//                @JsonProperty("HotelCode")
//                private String hotelCode;
//
//                /**
//                 * 客人类型
//                 * All=统一价；
//                 * Chinese =内宾价，需提示客人“须持大陆身份证入住”；
//                 * OtherForeign =外宾价，需提示客人“须持国外护照入住”；
//                 * HongKong   =港澳台客人价，需提示客人“须持港澳台身份证入住”；
//                 * ChinaGuest =中宾价，需提示客人“须持中国身份证、护照入住”；
//                 */
//                @Nullable
//                @JsonProperty("CustomerType")
//                private CustomerType customerType;
//
//                /**
//                 * 适用人群
//                 * 宾客类型的适用人群：
//                 * 0:不限
//                 * 1:持中国身份证的居民
//                 * 2:持回乡证的港澳人士
//                 * 3:持台胞证的台湾人士
//                 * 4:持中国护照的侨胞
//                 * 5:持大陆工作证/居留许可的外籍人士
//                 * 6:持非中国护照的外籍人士
//                 * 7:其他
//                 */
//                @Nullable
//                @JsonProperty("GuestType")
//                private String guestType;
//
//                /**
//                 * 其他适用人群内容
//                 */
//                @Nullable
//                @JsonProperty("GuestTypeExtendCh")
//                private String guestTypeExtendCh;
//
//                /**
//                 * 房量限额
//                 * 入住时间内不能超售的最小值。当大于0小于5时，表示目前仅剩的房量;0表示房量充足
//                 */
//                @Nullable
//                @JsonProperty("CurrentAlloment")
//                private Integer currentAlloment;
//
//                /**
//                 * 预计确认时长
//                 * 产品的预计确认时长，小于等于0代表未知确认时长
//                 */
//                @Nullable
//                @JsonProperty("ConfirmDurationTime")
//                private Integer confirmDurationTime;
//
//                /**
//                 * 是否支持即时确认
//                 * 表示这个产品是否支持即时确认。最终的订单是否是即时确认还需查看创建订单接口的返回值（推荐）或者调用即时确认接口（不推荐）
//                 */
//                @Nullable
//                @JsonProperty("InstantConfirmation")
//                private Boolean instantConfirmation;
//
//                /**
//                 * 付款类型
//                 * SelfPay-前台现付、Prepay-预付
//                 * 具体信息查看使用说明
//                 */
//                @NotNull
//                @JsonProperty("PaymentType")
//                private PaymentType paymentType;
//
//                /**
//                 * 对应的预订规则编号
//                 * 使用这些Id到上面对应的规则数组里查找对应的规则
//                 */
//                @Nullable
//                @JsonProperty("BookingRuleIds")
//                private String bookingRuleIds;
//
//                /**
//                 * 对应的担保规则编号
//                 */
//                @Nullable
//                @JsonProperty("GuaranteeRuleIds")
//                private String guaranteeRuleIds;
//
//                /**
//                 * 对应的预付规则编号
//                 */
//                @Nullable
//                @JsonProperty("PrepayRuleIds")
//                private String prepayRuleIds;
//
//                /**
//                 * 对应的新担保规则ID
//                 * 关联的新担保规则编号，多个编号时以逗号分隔。只有PaymentType为SelfPay时需要关注。
//                 */
//                @Nullable
//                @JsonProperty("GuaranteeRuleExtendIds")
//                private String guaranteeRuleExtendIds;
//
//                /**
//                 * 对应的新预付规则ID
//                 * 关联的新预付规则编号，多个编号时以逗号分隔。只有PaymentType为Prepay时需要关注。
//                 */
//                @Nullable
//                @JsonProperty("PrepayRuleExtendIds")
//                private String prepayRuleExtendIds;
//
//                /**
//                 * 对应的促销规则编号
//                 */
//                @Nullable
//                @JsonProperty("DrrRuleIds")
//                private String drrRuleIds;
//
//                /**
//                 * 对应的增值服务编号
//                 */
//                @Nullable
//                @JsonProperty("ValueAddIds")
//                private String valueAddIds;
//
//                /**
//                 * 礼品ID
//                 * 请查找Gifts
//                 */
//                @Nullable
//                @JsonProperty("GiftIds")
//                private String giftIds;
//
//                /**
//                 * 产品特性类型
//                 * 版本1.08新增。可逗号分隔，目前取值：
//                 * 3-限时抢购
//                 * 4-钟点房
//                 * 5-手机专享
//                 * 25-床位房（床位房类型此处已无效，判断床位房请解析RatePlanName或者Room节点的Name字段，其中只要有一个字段包含“床位”字样即为床位房）
//                 */
//                @Nullable
//                @JsonProperty("ProductTypes")
//                private String productTypes;
//
//                /**
//                 * 是否今日特价
//                 */
//                @Nullable
//                @JsonProperty("IsLastMinuteSale")
//                private Boolean isLastMinuteSale;
//
//                /**
//                 * 今日特价开始时间
//                 * 每天可以销售的开始时间
//                 */
//                @Nullable
//                @JsonProperty("StartTime")
//                private String startTime;
//
//                /**
//                 * 今日特价结束时间
//                 * 每天可以销售的结束时间
//                 */
//                @Nullable
//                @JsonProperty("EndTime")
//                private String endTime;
//
//                /**
//                 * 预定最少数量
//                 * 默认值：1
//                 */
//                @Nullable
//                @JsonProperty("MinAmount")
//                private Integer minAmount;
//
//                /**
//                 * 最少入住天数
//                 * 默认值：1
//                 */
//                @Nullable
//                @JsonProperty("MinDays")
//                private Integer minDays;
//
//                /**
//                 * 最多入住天数
//                 * 默认值：365
//                 */
//                @Nullable
//                @JsonProperty("MaxDays")
//                private Integer maxDays;
//
//                /**
//                 * 总价
//                 * 已经通过DRR的计算可以直接显示给客人。价格为-1表示不能销售
//                 */
//                @Nullable
//                @JsonProperty("TotalRate")
//                private BigDecimal totalRate;
//
//                /**
//                 * 日均价
//                 */
//                @Nullable
//                @JsonProperty("AverageRate")
//                private BigDecimal averageRate;
//
//                /**
//                 * 促销前的日均价
//                 * 版本1.08新增
//                 */
//                @Nullable
//                @JsonProperty("AverageBaseRate")
//                private BigDecimal averageBaseRate;
//
//                /**
//                 * 货币
//                 * 参考Currency
//                 */
//                @Nullable
//                @JsonProperty("CurrencyCode")
//                private String currencyCode;
//
//                /**
//                 * 优惠券
//                 */
//                @Nullable
//                @JsonProperty("Coupon")
//                private BigDecimal coupon;
//
//                /**
//                 * 每天价格数组
//                 * 包含多个 NightlyRate节点
//                 */
//                @Nullable
//                @JsonProperty("NightlyRates")
//                private Item<NightlyRate> nightlyRates;
//
//                /**
//                 * 是否需要提供身份证号
//                 * 指该RatePlan在下单的时候，是否需要传入入住人的身份证号信息，如果该字段不为空且为true，则在成单时必须传入身份证号（将来废弃，新字段请参考身份信息验证类型【Identification】）
//                 */
//                @Nullable
//                @JsonProperty("NeedIdNo")
//                private Boolean needIdNo;
//
//                /**
//                 * 身份信息验证类型
//                 * 0-无特殊验证要求（默认值）
//                 * 1-整个订单至少传一个身份证
//                 * 2-订单中每个房间至少传一个证件
//                 * 3-订单中每个房间至少传一个身份证
//                 * 4-每个客人传一个身份证
//                 * 5-整个订单至少传一个身份证且需预订本人入住
//                 * （后续可能会继续增加其他验证类型，建议接入时将非文档中给出类型的产品做过滤处理）
//                 */
//                @Nullable
//                @JsonProperty("Identification")
//                private Integer identification;
//
//                /**
//                 * 预付产品发票模式
//                 * 仅用于预付产品的发票开具模式。 版本V1.11新增
//                 * Hotel -- 酒店开具
//                 * Elong -- 艺龙开具
//                 */
//                @Nullable
//                @JsonProperty("InvoiceMode")
//                private String invoiceMode;
//
//                /**
//                 * 产品可以展示销售的渠道
//                 * v1.23新增，逗号分隔的数字列表(如果仅出现3表示仅在手机端销售)：
//                 * 1---线上(普通的PC访问的Web)
//                 * 2---线下(呼叫中心、门店)
//                 * 3---手机(Mobile App、H5)
//                 */
//                @Nullable
//                @JsonProperty("BookingChannels")
//                private String bookingChannels;
//
//                /**
//                 * 是否为限价产品
//                 * 表示本RatePlan是否为限价产品，限价产品必须按照艺龙给出的售价进行售卖，即按照TotalRate指定的价格卖给客人
//                 * false --- 非限价
//                 * true --- 限价
//                 */
//                @Nullable
//                @JsonProperty("isPriceLimittedProduct")
//                private Boolean isPriceLimittedProduct;
//
//                /**
//                 * 酒店签约类型
//                 * v1.27 1为直签，2为非直签，0为未知
//                 */
//                @Nullable
//                @JsonProperty("CooperationType")
//                private Integer cooperationType;
//
//                /**
//                 * 可住开始时间
//                 * V1.32新增。
//                 * 1、钟点房产品特有字段。
//                 * 2、可住时长stayTime为常量，实际客人的可住时长需要代理根据可住结束时间减去当前时间进行计算
//                 * 3、建议实际可住时长不足一小时时不要再展示给客人
//                 */
//                @Nullable
//                @JsonProperty("earliestToliveTime")
//                private String earliestToliveTime;
//
//                /**
//                 * 可住结束时间
//                 */
//                @Nullable
//                @JsonProperty("latestToliveTime")
//                private String latestToliveTime;
//
//                /**
//                 * 可住时长
//                 */
//                @Nullable
//                @JsonProperty("stayTime")
//                private String stayTime;
//
//                /**
//                 * 可入住人数
//                 * 房型补充说明
//                 */
//                @Nullable
//                @JsonProperty("xStayPeopleNum")
//                private String xStayPeopleNum;
//
//                /**
//                 * 可入住性别
//                 * 房型补充说明
//                 */
//                @Nullable
//                @JsonProperty("xStaySex")
//                private String xStaySex;
//
//                /**
//                 * 床型
//                 * 房型补充说明
//                 */
//                @Nullable
//                @JsonProperty("xBedType")
//                private String xBedType;
//
//                /**
//                 * 楼层
//                 * 房型补充说明
//                 */
//                @Nullable
//                @JsonProperty("xFloor")
//                private String xFloor;
//
//                /**
//                 * 朝向
//                 * 房型补充说明
//                 */
//                @Nullable
//                @JsonProperty("xOrientation")
//                private String xOrientation;
//
//                /**
//                 * 自定义说明
//                 * 房型补充说明
//                 */
//                @Nullable
//                @JsonProperty("xUserDefined")
//                private String xUserDefined;
//
//                /**
//                 * NightlyRate 节点
//                 */
//                @Data
//                @NoArgsConstructor
//                @AllArgsConstructor
//                public static class NightlyRate {
//                    /**
//                     * 当天日期
//                     */
//                    @NotNull
//                    @JsonProperty("Date")
//                    private String date;
//
//                    /**
//                     * 会员价
//                     * 已经通过DRR的计算可以直接显示给客人。价格为-1表示不能销售。
//                     */
//                    @NotNull
//                    @JsonProperty("Member")
//                    private BigDecimal member;
//
//                    /**
//                     * 结算价
//                     * 仅用于结算价模式下的预付产品可用，非结算价模式下返回-1
//                     */
//                    @NotNull
//                    @JsonProperty("Cost")
//                    private BigDecimal cost;
//
//                    /**
//                     * 库存状态
//                     * 表示当天库存是否可用
//                     */
//                    @NotNull
//                    @JsonProperty("Status")
//                    private Boolean status;
//
//                    /**
//                     * 加床价
//                     * -1表示不能加床
//                     */
//                    @Nullable
//                    @JsonProperty("AddBed")
//                    private BigDecimal addBed;
//
//                    /**
//                     * 早餐份数
//                     * v1.25新增
//                     */
//                    @Nullable
//                    @JsonProperty("BreakfastCount")
//                    private Integer breakfastCount;
//
//                    /**
//                     * 每日优惠
//                     * v1.29（前台现付是返现，预付是返现或立减）
//                     */
//                    @Nullable
//                    @JsonProperty("Coupon")
//                    private BigDecimal coupon;
//                }
//            }

            @Data
            @NoArgsConstructor
            @AllArgsConstructor
            public static class RatePlan {

                /**
                 * 产品编号
                 */
                @NotNull
                @JsonProperty("RatePlanId")
                private Long ratePlanId;

                /**
                 * 产品名称
                 */
                @NotNull
                @JsonProperty("RatePlanName")
                private String ratePlanName;

                /**
                 * 销售状态
                 * false--不可销售（可能是产品无效、部分日期缺少价格）
                 * true--可销售
                 */
                @NotNull
                @JsonProperty("Status")
                private Boolean status;

                /**
                 * 销售房型编号
                 */
                @NotNull
                @JsonProperty("RoomTypeId")
                private String roomTypeId;

                /**
                 * 供应商房型附加名称
                 */
                @Nullable
                @JsonProperty("SuffixName")
                private String suffixName;

                /**
                 * 供应商酒店编码
                 */
                @Nullable
                @JsonProperty("HotelCode")
                private String hotelCode;

                /**
                 * 是否支持专票
                 */
                @Nullable
                @JsonProperty("SupportSpecialInvoice")
                private Boolean supportSpecialInvoice;

                /**
                 * 客人类型
                 * All=统一价；
                 * Chinese=内宾价，需提示客人“须持大陆身份证入住”；
                 * OtherForeign=外宾价，需提示客人“须持国外护照入住”；
                 * HongKong=港澳台客人价，需提示客人“须持港澳台身份证入住”；
                 * ChinaGuest=中宾价，需提示客人“须持中国身份证、护照入住”；
                 */
                @Nullable
                @JsonProperty("CustomerType")
                private String customerType;

                /**
                 * 适用人群
                 */
                @Nullable
                @JsonProperty("guestType")
                private String guestType;

                /**
                 * 适用人群中其他的内容
                 */
                @Nullable
                @JsonProperty("guestTypeExtendCh")
                private String guestTypeExtendCh;

                /**
                 * 房量限额
                 */
                @Nullable
                @JsonProperty("CurrentAlloment")
                private Integer currentAlloment;

                /**
                 * 预计确认时长
                 */
                @Nullable
                @JsonProperty("ConfirmDurationTime")
                private Integer confirmDurationTime;

                /**
                 * 是否支持即时确认
                 */
                @Nullable
                @JsonProperty("InstantConfirmation")
                private Boolean instantConfirmation;

                /**
                 * 付款类型
                 * SelfPay-前台现付、Prepay-预付
                 */
                @Nullable
                @JsonProperty("PaymentType")
                private String paymentType;

                /**
                 * 对应的预订规则编号
                 */
                @Nullable
                @JsonProperty("BookingRuleIds")
                private String bookingRuleIds;

                /**
                 * 是否为限价产品
                 */
                @NotNull
                @JsonProperty("isPriceLimittedProduct")
                private Boolean isPriceLimittedProduct;

                /**
                 * 限价类型
                 */
                @NotNull
                @JsonProperty("PriceLimitedType")
                private Integer priceLimitedType;

                /**
                 * 对应的担保规则编号
                 */
                @Nullable
                @JsonProperty("GuaranteeRuleIds")
                private String guaranteeRuleIds;

                /**
                 * 对应的预付规则编号
                 */
                @Nullable
                @JsonProperty("PrepayRuleIds")
                private String prepayRuleIds;

                /**
                 * 对应的新担保规则ID
                 */
                @Nullable
                @JsonProperty("GuaranteeRuleExtendIds")
                private String guaranteeRuleExtendIds;

                /**
                 * 对应的新预付规则ID
                 */
                @Nullable
                @JsonProperty("PrepayRuleExtendIds")
                private String prepayRuleExtendIds;

                /**
                 * 对应的促销规则编号
                 */
                @Nullable
                @JsonProperty("DrrRuleIds")
                private String drrRuleIds;

                /**
                 * 对应的增值服务编号
                 */
                @Nullable
                @JsonProperty("ValueAddIds")
                private String valueAddIds;

                /**
                 * 礼品ID
                 */
                @Nullable
                @JsonProperty("GiftIds")
                private String giftIds;

                /**
                 * 礼包套餐ID
                 */
                @Nullable
                @JsonProperty("PkgProductIds")
                private String pkgProductIds;

                /**
                 * 新餐食节点
                 */
//                @NotNull
                @JsonProperty("Meals")
                private Meals meals;

                /**
                 * 酒店特殊信息提示的编号
                 */
                @Nullable
                @JsonProperty("HAvailPolicyIds")
                private String hAvailPolicyIds;

                /**
                 * 产品特性类型
                 */
                @Nullable
                @JsonProperty("ProductTypes")
                private String productTypes;

                /**
                 * 分销渠道
                 */
                @Nullable
                @JsonProperty("sellChannels")
                private String sellChannels;

                /**
                 * 是否今日特价（即尾房）
                 */
                @Nullable
                @JsonProperty("IsLastMinuteSale")
                private Boolean isLastMinuteSale;

                /**
                 * 是否需要提供身份证号
                 */
                @Nullable
                @JsonProperty("NeedIdNo")
                private Boolean needIdNo;

                /**
                 * 身份信息验证类型
                 */
                @NotNull
                @JsonProperty("Identification")
                private Integer identification;

                /**
                 * 尾房开始时间
                 */
                @Nullable
                @JsonProperty("StartTime")
                private String startTime;

                /**
                 * 尾房结束时间
                 */
                @Nullable
                @JsonProperty("EndTime")
                private String endTime;

                /**
                 * 预定最少数量
                 */
                @Nullable
                @JsonProperty("MinAmount")
                private Integer minAmount;

                /**
                 * 最少入住天数
                 */
                @Nullable
                @JsonProperty("MinDays")
                private Integer minDays;

                /**
                 * 最多入住天数
                 */
                @Nullable
                @JsonProperty("MaxDays")
                private Integer maxDays;

                /**
                 * 最多预订间数
                 */
                @Nullable
                @JsonProperty("MaxCheckinRooms")
                private Integer maxCheckinRooms;

                /**
                 * 最少提前预订小时数
                 */
                @NotNull
                @JsonProperty("MinAdvHours")
                private Integer minAdvHours;

                /**
                 * 最多提前预订小时数
                 */
                @NotNull
                @JsonProperty("MaxAdvHours")
                private Integer maxAdvHours;

                /**
                 * 总价
                 */
                @NotNull
                @JsonProperty("TotalRate")
                private BigDecimal totalRate;

                /**
                 * 日均价
                 */
                @NotNull
                @JsonProperty("AverageRate")
                private BigDecimal averageRate;

                /**
                 * 日均价（促销前）
                 */
                @Nullable
                @JsonProperty("AverageBaseRate")
                private BigDecimal averageBaseRate;

                /**
                 * 货币
                 */
                @Nullable
                @JsonProperty("CurrencyCode")
                private String currencyCode;

                /**
                 * 同程促销金额
                 */
                @Nullable
                @JsonProperty("Coupon")
                private BigDecimal coupon;

                /**
                 * 每天价格数组
                 */
                @Nullable
                @JsonProperty("NightlyRates")
                private List<NightlyRate> nightlyRates;

                /**
                 * 预付产品发票模式
                 */
                @NotNull
                @JsonProperty("InvoiceMode")
                private String invoiceMode;

                /**
                 * 酒店签约类型
                 */
                @Nullable
                @JsonProperty("CooperationType")
                private Integer cooperationType;

                /**
                 * 产品可以展示销售的渠道
                 */
                @Nullable
                @JsonProperty("BookingChannels")
                private String bookingChannels;

                /**
                 * 可住开始时间
                 */
                @Nullable
                @JsonProperty("earliestToliveTime")
                private String earliestToliveTime;

                /**
                 * 可住结束时间
                 */
                @Nullable
                @JsonProperty("latestToliveTime")
                private String latestToliveTime;

                /**
                 * 可住时长
                 */
                @Nullable
                @JsonProperty("stayTime")
                private String stayTime;

                /**
                 * 可入住人数
                 */
                @Nullable
                @JsonProperty("xStayPeopleNum")
                private String xStayPeopleNum;

                /**
                 * 可入住性别
                 */
                @Nullable
                @JsonProperty("xStaySex")
                private String xStaySex;

                /**
                 * 床型
                 */
                @Nullable
                @JsonProperty("xBedType")
                private String xBedType;

                /**
                 * 楼层
                 */
                @Nullable
                @JsonProperty("xFloor")
                private String xFloor;

                /**
                 * 朝向
                 */
                @Nullable
                @JsonProperty("xOrientation")
                private String xOrientation;

                /**
                 * 自定义说明
                 */
                @Nullable
                @JsonProperty("xUserDefined")
                private String xUserDefined;

                /**
                 * 酒店等级
                 */
                @Nullable
                @JsonProperty("HotelLevel")
                private String hotelLevel;

                /**
                 * 产品唯一标识
                 */
                @Nullable
                @JsonProperty("majia_id")
                private String majiaId;

                /**
                 * 产品唯一标识（API成单使用）
                 */
                @Nullable
                @JsonProperty("Littlemajiaid")
                private String littleMajiaId;

                /**
                 * 商品唯一标识
                 */
                @Nullable
                @JsonProperty("GoodsUniqId")
                private String goodsUniqId;

                /**
                 * 每日促销明细集合
                 */
                @Nullable
                @JsonProperty("DayPromotions")
                private List<DayPromotion> dayPromotions;

                /**
                 * 促销活动集合
                 */
                @Nullable
                @JsonProperty("PromotionFlags")
                private List<PromotionFlag> promotionFlags;

                /**
                 * 是否促销
                 */
                @Nullable
                @JsonProperty("IsPromotion")
                private Boolean isPromotion;

                /**
                 * 是需要回传马甲
                 */
                @Nullable
                @JsonProperty("NeedMajiaId")
                private Boolean needMajiaId;

                /**
                 * 使用的同程促销类型
                 */
                @Nullable
                @JsonProperty("UsedPromotionValues")
                private List<UsedPromotionValue> usedPromotionValues;

                /**
                 * 网络标准化
                 */
                @Nullable
                @JsonProperty("InternetFilter")
                private Integer internetFilter;

                /**
                 * 早餐标准化
                 */
                @Nullable
                @JsonProperty("BoardFilter")
                private Integer boardFilter;

                /**
                 * 预付结果节点
                 */
                @Nullable
                @JsonProperty("PrepayResult")
                private PrepayResult prepayResult;

                /**
                 * 现付结果节点
                 */
                @NotNull
                @JsonProperty("GuaranteeResult")
                private GuaranteeResult guaranteeResult;

                /**
                 * 房间面积
                 */
                @Nullable
                @JsonProperty("RoomSquareMetres")
                private String roomSquareMetres;

                /**
                 * 税和服务费原币种
                 */
                @Nullable
                @JsonProperty("TaxAndServiceFee")
                private BigDecimal taxAndServiceFee;

                /**
                 * 税和服务费人民币币种
                 */
                @Nullable
                @JsonProperty("TaxAndServiceFeeRMB")
                private BigDecimal taxAndServiceFeeRMB;

                /**
                 * 床型描述
                 */
                @Nullable
                @JsonProperty("BedDescription")
                private String bedDescription;

                /**
                 * 床型标准化描述
                 */
                @Nullable
                @JsonProperty("BedTypeAssociationalFilter")
                private String bedTypeAssociationalFilter;

                /**
                 * 是否有窗
                 */
                @Nullable
                @JsonProperty("HasWindow")
                private Boolean hasWindow;

                /**
                 * 吸烟偏好描述
                 */
                @Nullable
                @JsonProperty("SmokingDesc")
                private String smokingDesc;

                /**
                 * 膳食
                 */
                @Nullable
                @JsonProperty("Board")
                private Board board;

                /**
                 * 房间可住儿童年龄
                 */
                @Nullable
                @JsonProperty("RoomChildAge")
                private Integer roomChildAge;

                /**
                 * 房间最大入住人数
                 */
                @Nullable
                @JsonProperty("RoomMaxPax")
                private Integer roomMaxPax;

                /**
                 * 房间最大可住成人数
                 */
                @Nullable
                @JsonProperty("AdultOccupancyPerRoom")
                private Integer adultOccupancyPerRoom;

                /**
                 * 房间最大可住儿童数
                 */
                @Nullable
                @JsonProperty("ChildrenOccupancyPerRoom")
                private Integer childrenOccupancyPerRoom;

                /**
                 * 入住需知
                 */
                @Nullable
                @JsonProperty("CheckInInstructions")
                private String checkInInstructions;

                /**
                 * 额外人员费用(附加费)
                 */
                @Nullable
                @JsonProperty("ExtraPersonFee")
                private BigDecimal extraPersonFee;

                /**
                 * 额外人员费用(附加费人民币)
                 */
                @Nullable
                @JsonProperty("ExtraPersonFeeRMB")
                private BigDecimal extraPersonFeeRMB;

                /**
                 * 床型信息
                 */
                @Nullable
                @JsonProperty("BedGroups")
                private List<InterBedGroup> bedGroups;

                /**
                 * 网络描述
                 */
                @Nullable
                @JsonProperty("InternetDesc")
                private String internetDesc;

                /**
                 * 供应商id
                 */
                @Nullable
                @JsonProperty("SupplierId")
                private String supplierId;

                /**
                 * 二级供应商id
                 */
                @Nullable
                @JsonProperty("SubSupplierId")
                private String subSupplierId;

                @JsonProperty("SupplyId")
                private String supplyId;

                /**
                 * 商品库shopperid
                 */
                @Nullable
                @JsonProperty("ShopperProductId")
                private String shopperProductId;

                /**
                 * 另付税和服务费
                 */
                @Nullable
                @JsonProperty("AdditionalTax")
                private AdditionalTax additionalTax;

                /**
                 * Meals节点
                 */
                @Data
                @NoArgsConstructor
                @AllArgsConstructor
                public static class Meals {

                    /**
                     * 是否存在餐食表格
                     * true 代表取 dayMealTable 餐食表格字段，查看每天的餐食情况
                     */
                    @NotNull
                    @JsonProperty("hasMealTable")
                    private Boolean hasMealTable;

                    /**
                     * 餐食文案描述
                     */
                    @NotNull
                    @JsonProperty("mealCopyWriting")
                    private String mealCopyWriting;

                    /**
                     * 每日餐食表格
                     */
                    @NotNull
                    @JsonProperty("dayMealTable")
                    private List<DayMeal> dayMealTable;

                    /**
                     * DayMeal 节点
                     */
                    @Data
                    @NoArgsConstructor
                    @AllArgsConstructor
                    public static class DayMeal {
                        /**
                         * 餐食的日期
                         * 格式为 yyyy-MM-dd，例如 2021-08-12
                         */
                        @NotNull
                        @JsonProperty("date")
                        private String date;

                        /**
                         * 是否使用动态餐食
                         * true 取 dynamicMealDesc
                         * false 取 breakfastDesc、lunchDesc、dinnerDesc
                         */
                        @NotNull
                        @JsonProperty("useDynamicMeal")
                        private Boolean useDynamicMeal;

                        /**
                         * 动态餐食描述
                         */
                        @Nullable
                        @JsonProperty("dynamicMealDesc")
                        private String dynamicMealDesc;

                        /**
                         * 早餐数量
                         */
                        @NotNull
                        @JsonProperty("breakfastShare")
                        private Integer breakfastShare;

                        /**
                         * 早餐描述
                         */
                        @Nullable
                        @JsonProperty("breakfastDesc")
                        private String breakfastDesc;

                        /**
                         * 午餐数量
                         */
                        @NotNull
                        @JsonProperty("lunchShare")
                        private Integer lunchShare;

                        /**
                         * 午餐描述
                         */
                        @Nullable
                        @JsonProperty("lunchDesc")
                        private String lunchDesc;

                        /**
                         * 晚餐数量
                         */
                        @NotNull
                        @JsonProperty("dinnerShare")
                        private Integer dinnerShare;

                        /**
                         * 晚餐描述
                         */
                        @Nullable
                        @JsonProperty("dinnerDesc")
                        private String dinnerDesc;

                        /**
                         * 到天餐食描述
                         */
                        @Nullable
                        @JsonProperty("dayMealCopyWriting")
                        private String dayMealCopyWriting;
                    }
                }

                /**
                 * NightlyRate 节点
                 */
                @Data
                @NoArgsConstructor
                @AllArgsConstructor
                public static class NightlyRate {
                    /**
                     * 当天日期
                     */
                    @NotNull
                    @JsonProperty("Date")
                    private String date;

                    /**
                     * 会员价
                     * 已经通过DRR的计算可以直接显示给客人。价格为-1表示不能销售。
                     */
                    @NotNull
                    @JsonProperty("Member")
                    private BigDecimal member;

                    /**
                     * 结算价
                     * 仅用于结算价模式下的预付产品可用，非结算价模式下返回-1
                     */
                    @NotNull
                    @JsonProperty("Cost")
                    private BigDecimal cost;

                    /**
                     * 库存状态
                     * 表示当天库存是否可用
                     */
                    @NotNull
                    @JsonProperty("Status")
                    private Boolean status;

                    /**
                     * 加床价
                     * -1表示不能加床
                     */
                    @Nullable
                    @JsonProperty("AddBed")
                    private BigDecimal addBed;

                    /**
                     * 早餐份数
                     * v1.25新增
                     */
                    @Nullable
                    @JsonProperty("BreakfastCount")
                    private Integer breakfastCount;

                    /**
                     * 每日优惠
                     * v1.29（前台现付是返现，预付是返现或立减）
                     */
                    @Nullable
                    @JsonProperty("coupon")
                    private BigDecimal coupon;

                    @Nullable
                    @JsonProperty("MinRate")
                    private BigDecimal minRate;

                    @Nullable
                    @JsonProperty("Rate")
                    private BigDecimal rate;
                }

                /**
                 * DayPromotion 节点
                 */
                @Data
                @NoArgsConstructor
                @AllArgsConstructor
                public static class DayPromotion {
                    /**
                     * 日期
                     */
                    @NotNull
                    @JsonProperty("Date")
                    private String date;

                    /**
                     * 促销信息
                     */
                    @NotNull
                    @JsonProperty("Promotions")
                    private List<Promotion> promotions;

                    /**
                     * Promotion节点
                     */
                    @Data
                    @NoArgsConstructor
                    @AllArgsConstructor
                    public static class Promotion {

                        /**
                         * 促销类型
                         * 0:未定义
                         * 1:天天特价
                         * 2:门店新客
                         * 3:优享会
                         * 4:其他促销
                         * 5:权益云
                         */
                        @Nullable
                        @JsonProperty("PromotionType")
                        private Integer promotionType;

                        /**
                         * 卖价优惠的金额
                         */
                        @Nullable
                        @JsonProperty("PriceDiscountValue")
                        private BigDecimal priceDiscountValue;

                        /**
                         * ID
                         */
                        @Nullable
                        @JsonProperty("PromotionId")
                        private Long promotionId;
                    }
                }

                /**
                 * PromotionFlag 节点
                 */
                @Data
                @NoArgsConstructor
                @AllArgsConstructor
                public static class PromotionFlag {

                    /**
                     * 促销活动Id
                     */
                    @NotNull
                    @JsonProperty("PromotionId")
                    private Long promotionId;

                    /**
                     * 促销活动标签
                     */
                    @Nullable
                    @JsonProperty("PromotionTag")
                    private String promotionTag;
                }

                /**
                 * UsedPromotionValue 节点
                 */
                @Data
                @NoArgsConstructor
                @AllArgsConstructor
                public static class UsedPromotionValue {

                    /**
                     * 促销类型Id
                     * 1:返现; 9:立减; 10:普通红包返现; 11:普通红包立减; 62:满返红包;63:满减红包
                     */
                    @NotNull
                    @JsonProperty("PromotionTypeId")
                    private Integer promotionTypeId;

                    /**
                     * 优惠金额
                     */
                    @NotNull
                    @JsonProperty("PromotionValue")
                    private BigDecimal PromotionValue;
                }

                /**
                 * PrepayResult 节点
                 */
                @Data
                @NoArgsConstructor
                @AllArgsConstructor
                public static class PrepayResult {

                    /**
                     * 取消规则
                     */
                    @NotNull
                    @JsonProperty("CancelDescription")
                    private String cancelDescription;

                    /**
                     * 取消类型
                     * 1：免费取消
                     * 2：收费取消
                     * 3：限时取消
                     * 4：不可取消
                     */
                    @NotNull
                    @JsonProperty("CancelType")
                    private Integer cancelType;

                    /**
                     * 是否使用阶梯担保规则
                     */
                    @Nullable
                    @JsonProperty("LadderVouch")
                    private Boolean ladderVouch;

                    /**
                     * 取消规则明细
                     */
                    @NotNull
                    @JsonProperty("LadderParseList")
                    private List<LadderParse> ladderParseList;

                    /**
                     * 取消规则标签
                     * 如果规则是任意取消和不可取消的没有这个字段和对应值，
                     * 限时取消和付费取消则会返回该字段
                     */
                    @Nullable
                    @JsonProperty("CancelTag")
                    private String cancelTag;
                }

                /**
                 * GuaranteeResult节点
                 */
                @Data
                @NoArgsConstructor
                @AllArgsConstructor
                public static class GuaranteeResult {

                    /**
                     * 到店时间触发时的担保金额
                     * 货币类型为原币种，即所在 RatePlan 节点下的 CurrencyCode
                     * （废弃）
                     */
                    @NotNull
                    @JsonProperty("MoneyArrivalTime")
                    private Double moneyArrivalTime;

                    /**
                     * 需要担保的到店时间（格式：hh:mm）
                     * 用户填写的最晚到店早于此时间，不需要关注 MoneyArrivalTime，否则需要担保 MoneyArrivalTime
                     */
                    @NotNull
                    @JsonProperty("ArrivalTime")
                    private String arrivalTime;

                    /**
                     * 房量担保分割点
                     * 用户预订间数小于此值不需要担保，否则需要担保
                     */
                    @NotNull
                    @JsonProperty("RoomCount")
                    private Integer roomCount;

                    /**
                     * 担保类型
                     * 二进制位表示:
                     * 1: 到店时间担保
                     * 2: 房量担保
                     * 3: 预订即需担保
                     * 4: 免担保
                     * 0：免担保；2：到店时间担保；4：房量担保；8：预定即需担保
                     */
                    @NotNull
                    @JsonProperty("GuaranteeType")
                    private Integer guaranteeType;

                    /**
                     * 当前条件下需要担保的金额
                     * 具体见取消规则明细
                     */
                    @NotNull
                    @JsonProperty("GuaranteeMoney")
                    private Double guaranteeMoney;

                    /**
                     * 当前条件下是否需要担保
                     */
                    @NotNull
                    @JsonProperty("NeedGuarantee")
                    private Boolean needGuarantee;

                    /**
                     * 可以取消的时间点，单位秒
                     * （废弃）
                     * 之前可以取消，之后不可取消，不可取消时为 -28800
                     * 免费取消时为 Long.MAX_VALUE
                     * 其他情况下为北京时间的时间戳
                     */
                    @NotNull
                    @JsonProperty("CancelTime")
                    private Long cancelTime;

                    /**
                     * 取消规则详细描述
                     */
                    @Nullable
                    @JsonProperty("CancelDescription")
                    private String cancelDescription;

                    /**
                     * 取消规则标签
                     * 如果规则是任意取消和不可取消的没有这个字段和对应值
                     * 限时取消和付费取消则会返回该字段
                     */
                    @Nullable
                    @JsonProperty("CancelTag")
                    private String cancelTag;

                    /**
                     * 取消类型
                     * 1: 免费取消
                     * 2: 付费取消
                     * 3: 可取消
                     * 4: 不可取消
                     */
                    @NotNull
                    @JsonProperty("CancelType")
                    private Integer cancelType;

                    /**
                     * 取消规则明细
                     * 参考 LadderParse 节点
                     */
                    @NotNull
                    @JsonProperty("LadderParseList")
                    private List<LadderParse> ladderParseList;
                }

                /**
                 * LadderParse 节点
                 */
                @Data
                @NoArgsConstructor
                @AllArgsConstructor
                public static class LadderParse {

                    /**
                     * 开始时间
                     */
                    @NotNull
                    @JsonProperty("BeginTime")
                    private Long beginTime;

                    /**
                     * 结束时间
                     */
                    @NotNull
                    @JsonProperty("EndTime")
                    private Long endTime;

                    /**
                     * 扣费类型
                     * 0: 不扣费
                     * 1: 金额
                     * 2: 比例
                     * 3: 首晚房费
                     * 请注意：hotel.detail、hotel.data.booking、hotel.order.detail接口中CutType、CutValue存在不一致的情况，
                     * 对客展示建议直接使用具体扣费金额：AmountRmb（兜底当扣费金额AmountRmb大于订单金额时，展示不可取消）
                     */
                    @NotNull
                    @JsonProperty("CutType")
                    private Integer cutType;

                    /**
                     * 扣费值（原始币种）
                     */
                    @NotNull
                    @JsonProperty("CutValue")
                    private BigDecimal cutValue;

                    /**
                     * 扣费值（国际现付的是原币，预付对客的是人民币，预付对酒店的是原币）
                     */
                    @NotNull
                    @JsonProperty("Amount")
                    private BigDecimal amount;

                    /**
                     * 短文案
                     */
                    @Nullable
                    @JsonProperty("ShortDesc")
                    private String shortDesc;

                    /**
                     * 扣费值（人民币）
                     */
                    @NotNull
                    @JsonProperty("AmountRmb")
                    private BigDecimal amountRmb;

                    /**
                     * 汇率
                     */
                    @Nullable
                    @JsonProperty("ExchangeRate")
                    private BigDecimal exchangeRate;
                }

                /**
                 * Board 节点
                 */
                @Data
                @NoArgsConstructor
                @AllArgsConstructor
                public static class Board {

                    /**
                     * 是否含早
                     * 国际特有字段
                     */
                    @Nullable
                    @JsonProperty("BreakfastIncluded")
                    private Boolean breakfastIncluded;

                    /**
                     * 是否半膳
                     * 国际特有字段
                     */
                    @Nullable
                    @JsonProperty("HalfboardIncluded")
                    private Boolean halfboardIncluded;

                    /**
                     * 是否全膳
                     * 国际特有字段
                     */
                    @Nullable
                    @JsonProperty("FullboardIncluded")
                    private Boolean fullboardIncluded;

                    /**
                     * 膳食描述
                     * 国际特有字段
                     */
                    @Nullable
                    @JsonProperty("BoardDesc")
                    private String boardDesc;

                    /**
                     * 膳食明细
                     * 参考 BoardDetail 节点
                     */
                    @Nullable
                    @JsonProperty("Boards")
                    private List<BoardDetail> boards;

                    /**
                     * BoardDetail 节点
                     */
                    @Data
                    @NoArgsConstructor
                    @AllArgsConstructor
                    public static class BoardDetail {

                        /**
                         * 描述
                         * 国际特有字段
                         */
                        @Nullable
                        @JsonProperty("Description")
                        private String description;

                        /**
                         * 膳食数量
                         * 国际特有字段
                         */
                        @Nullable
                        @JsonProperty("Count")
                        private Integer count;

                        /**
                         * 膳食类型
                         * 1：早餐；2：午餐；3：晚餐；0：未知餐型
                         * 国际特有字段
                         */
                        @Nullable
                        @JsonProperty("type")
                        private Integer type;

//                        /**
//                         * 膳食描述
//                         * 国际特有字段
//                         */
//                        @Nullable
//                        @JsonProperty("BoardDesc")
//                        private String boardDesc;
                    }
                }

                /**
                 * InterBedGroup节点
                 */
                @Data
                @NoArgsConstructor
                @AllArgsConstructor
                public static class InterBedGroup {

                    /**
                     * 床型信息id
                     * 国际特有字段
                     */
                    @Nullable
                    @JsonProperty("BedGroupId")
                    private String bedGroupId;

                    /**
                     * 床型信息描述
                     * 国际特有字段
                     */
                    @Nullable
                    @JsonProperty("BedGroupDesc")
                    private String bedGroupDesc;

                    /**
                     * 床类型集合
                     * 国际特有字段
                     */
                    @Nullable
                    @JsonProperty("BedTypes")
                    private List<BedType> bedTypes;

                    /**
                     * BedType节点
                     */
                    @Data
                    @NoArgsConstructor
                    @AllArgsConstructor
                    public static class BedType {

                        /**
                         * 床类型id
                         * 国际特有字段
                         */
                        @Nullable
                        @JsonProperty("BedTypeId")
                        private String bedTypeId;

                        /**
                         * 床类型名称
                         * 国际特有字段
                         */
                        @Nullable
                        @JsonProperty("BedTypeName")
                        private String bedTypeName;

                        /**
                         * 床类型
                         * 国际特有字段
                         */
                        @Nullable
                        @JsonProperty("BedType")
                        private String bedType;

                        /**
                         * 床数
                         * 国际特有字段
                         */
                        @Nullable
                        @JsonProperty("Count")
                        private Integer count;

                        /**
                         * 床大小
                         * 国际特有字段
                         */
                        @Nullable
                        @JsonProperty("Size")
                        private String size;
                    }
                }

                /**
                 * AdditionalTax节点
                 */
                @Data
                @NoArgsConstructor
                @AllArgsConstructor
                public static class AdditionalTax {

                    /**
                     * 另付税和服务费总额
                     */
                    @Nullable
                    @JsonProperty("TotalAmountRmb")
                    private BigDecimal totalAmountRmb;

                    /**
                     * 另付税和服务费明细
                     */
                    @Nullable
                    @JsonProperty("AdditionalTaxItems")
                    private List<AdditionalTaxItem> additionalTaxItems;

                    /**
                     * AdditionalTaxItem节点
                     */
                    @Data
                    @NoArgsConstructor
                    @AllArgsConstructor
                    public static class AdditionalTaxItem {

                        /**
                         * 另付税和服务费明细描述
                         */
                        @Nullable
                        @JsonProperty("Description")
                        private String description;

                        /**
                         * 另付税和服务费明细金额
                         */
                        @Nullable
                        @JsonProperty("Amount")
                        private BigDecimal amount;
                    }
                }
            }
        }

        /**
         * Detail 节点
         */
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class Detail {

            /**
             * 酒店名称
             */
            @NotNull
            @JsonProperty("HotelName")
            private String hotelName;

            /**
             * 挂牌星级
             * 此为酒店对外的挂牌星级（国家旅游局规定），0-无星级；1-一星级；2-二星级；3-三星级；4-四星级；5-五星级。
             * 当为0时对外显示可用Category的值，但请进行图标区分。
             */
            @NotNull
            @JsonProperty("StarRate")
            private Integer starRate;

            /**
             * 艺龙推荐级别
             * 艺龙推荐星级，而非酒店挂牌星级。对应值含义为：0,1,2：客栈；3：舒适；4：高档；5：豪华；A：公寓
             */
            @NotNull
            @JsonProperty("Category")
            private Integer category;

            /**
             * 纬度
             * 默认返回Google坐标系。当ResultType中加入8后，输出为百度坐标。其他坐标系请联系对应的厂商获取转换方法。
             */
            @Nullable
            @JsonProperty("Latitude")
            private String latitude;

            /**
             * 经度
             * 默认返回Google坐标系。当ResultType中加入8后，输出为百度坐标。其他坐标系请联系对应的厂商获取转换方法。
             */
            @Nullable
            @JsonProperty("Longitude")
            private String longitude;

            /**
             * 地址
             */
            @Nullable
            @JsonProperty("Address")
            private String address;

            /**
             * 前台电话
             */
            @Nullable
            @JsonProperty("Phone")
            private String phone;

            /**
             * 酒店图片
             * 小图(120x120, png), 入参ResultType传入3时返回，可以将该返回的链接上120_120替换为70_70获取更小的图片，也可以替换为350_350获取更大的图片
             */
            @Nullable
            @JsonProperty("ThumbNailUrl")
            private String thumbNailUrl;

            /**
             * 城市ID
             */
            @Nullable
            @JsonProperty("City")
            private String city;

            /**
             * 城市名称
             * V1.16新增。为空时使用ID和GEO数据进行关联。
             */
            @Nullable
            @JsonProperty("CityName")
            private String cityName;

            /**
             * 行政区ID
             */
            @Nullable
            @JsonProperty("District")
            private String district;

            /**
             * 行政区名称
             * V1.16新增。为空时使用ID和GEO数据进行关联。
             */
            @Nullable
            @JsonProperty("DistrictName")
            private String districtName;

            /**
             * 商业区ID
             */
            @Nullable
            @JsonProperty("BusinessZone")
            private String businessZone;

            /**
             * 商业区名称
             * V1.16新增。为空时使用ID和GEO数据进行关联。
             */
            @Nullable
            @JsonProperty("BusinessZoneName")
            private String businessZoneName;

            /**
             * 评价
             * 参照 Review 节点
             */
            @Nullable
            @JsonProperty("Review")
            private Review review;

            /**
             * 特色介绍
             */
            @Nullable
            @JsonProperty("Features")
            private String features;

            /**
             * 设施服务
             */
            @Nullable
            @JsonProperty("GeneralAmenities")
            private String generalAmenities;

            /**
             * 交通状况
             */
            @Nullable
            @JsonProperty("Traffic")
            private String traffic;

            /**
             * Review 节点
             */
            @Data
            @NoArgsConstructor
            @AllArgsConstructor
            public static class Review {

                /**
                 * 好评数
                 */
                @NotNull
                @JsonProperty("Good")
                private Integer good;

                /**
                 * 差评数
                 */
                @NotNull
                @JsonProperty("Poor")
                private Integer poor;

                /**
                 * 评论总数
                 */
                @NotNull
                @JsonProperty("Count")
                private Integer count;

                /**
                 * 评论分数
                 * 列表接口此字段为评论分数，【0-5】小数，一般是4.5 4.8 5.0之类的，与详情接口同名字段意义不同
                 */
                @Nullable
                @JsonProperty("Score")
                private String score;
            }
        }

        /**
         * Gift 节点
         */
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class Gift {

            /**
             * 送礼编号
             * 关联 RatePlan.GiftId
             */
            @NotNull
            @JsonProperty("GiftId")
            private Long giftId;

            /**
             * 礼包副标题
             * 关于礼品副标题的描述
             */
            @Nullable
            @JsonProperty("GiftDescription")
            private String giftDescription;

            /**
             * 描述
             * 关于礼品的描述
             */
            @NotNull
            @JsonProperty("Description")
            private String description;

            /**
             * 关联的产品列表
             * RelatedProducts 节点
             */
            @Nullable
            @JsonProperty("RelatedProducts")
            private List<RelatedProducts> relatedProducts;

            /**
             * 礼品有效开始时间
             * yyyy-MM-dd'T'HH:mm:ss+08:00 格式，例如：2018-11-02T00:00:00+08:00
             */
            @Nullable
            @JsonProperty("StartDate")
            private String startDate;

            /**
             * 礼品有效结束时间
             * yyyy-MM-dd'T'HH:mm:ss+08:00 格式，例如：2018-11-02T00:00:00+08:00
             */
            @Nullable
            @JsonProperty("EndDate")
            private String endDate;

            /**
             * 日期类型
             * CheckinDate: 入住日
             * BookingDate: 预订日
             * StayDate: 在店日
             */
            @NotNull
            @JsonProperty("DateType")
            private DateType dateType;

            /**
             * 星期设置
             */
            @Nullable
            @JsonProperty("WeekSet")
            private String weekSet;

            /**
             * 活动内容
             */
            @Nullable
            @JsonProperty("GiftContent")
            private String giftContent;

            /**
             * 送礼类型
             * 1. 礼品-含果盘
             * 3. 礼品-含水果
             * 4. 礼品-含饮品
             * 5. 礼品-含精美艺品
             * 6. 礼品-其他
             * 7. 延迟退房-延迟至13点退房
             * 8. 延迟退房-延迟至14点退房
             * 9. 延迟退房-延迟至15点退房
             * 10. 延迟退房-其他
             * 11. 餐饮-含午餐
             * 12. 餐饮-含晚餐
             * 13. 餐饮-含下午茶
             * 14. 餐饮-含餐券
             * 15. 餐饮-其他
             * 16. 旅游门票-含景点门票
             * 17. 旅游门票-含演出门票
             * 18. 旅游门票-其他
             * 19. 折扣/折扣券-含店内折扣/折扣券
             * 20. 折扣/折扣券-含外部折扣/折扣券
             * 21. 折扣/折扣券-其他
             * 22. 交通-含接站
             * 23. 交通-含接机
             * 24. 交通-含送站
             * 25. 交通-含送机
             * 26. 交通-含景区直通车
             * 27. 交通-其他
             * 28. 其他-其他
             * 注意：1.52 版本及以后该字段废弃，请使用 GiftInfos 字段，1.52 之前的版本仍使用该字段。
             */
            @Nullable
            @JsonProperty("GiftTypes")
            private String giftTypes;

            /**
             * 新的送礼类型
             * GiftInfo
             */
            @Nullable
            @JsonProperty("GiftInfos")
            private List<GiftInfo> giftInfos;

            /**
             * 小时数
             */
            @Nullable
            @JsonProperty("HourNumber")
            private Integer hourNumber;

            /**
             * 小时数的类型
             * Hours24: 全天24小时都可以送礼品
             * XhourBefore: 几点之前送礼品
             * XHourAfter: 几点之后送礼品
             */
            @Nullable
            @JsonProperty("HourType")
            private HourType hourType;

            /**
             * 送礼方式
             * EveryRoom: 每间房送一回礼品
             * EveryRoomPerDay: 每间房每个晚上送一回礼品
             * Other: 其他，参考下面的描述
             */
            @Nullable
            @JsonProperty("WayOfGiving")
            private String wayOfGiving;

            /**
             * 其他的送礼具体方式
             * 送礼方式为其他的时候，送礼活动的名称
             */
            @Nullable
            @JsonProperty("WayOfGivingOther")
            private String wayOfGivingOther;

            /**
             * 礼包价值
             * 礼包、套餐的预估总价值
             */
            @Nullable
            @JsonProperty("GiftValue")
            private BigDecimal giftValue;

            /**
             * RelatedProducts 节点
             */
            @Data
            @NoArgsConstructor
            @AllArgsConstructor
            public static class RelatedProducts {

                /**
                 * 供应商房型id列表
                 * 以逗号分隔的供应商房型id列表
                 */
                @NotNull
                @JsonProperty("RoomTypeIds")
                private String roomTypeIds;

                /**
                 * 价格计划ID
                 */
                @NotNull
                @JsonProperty("RatePlanId")
                private Integer ratePlanId;
            }

            /**
             * GiftInfo节点
             */
            @Data
            @NoArgsConstructor
            @AllArgsConstructor
            public static class GiftInfo {

                /**
                 * 礼包一级编号
                 * 1.含礼品
                 * 2.延迟退房
                 * 3.含餐饮
                 * 4.含旅游门票
                 * 5.含折扣/抵扣券
                 * 6.含交通
                 * 7.其他
                 */
                @NotNull
                @JsonProperty("GiftInfo")
                private Integer giftInfo;

                /**
                 * 二级礼包内容
                 */
                @NotNull
                @JsonProperty("GiftSubInfos")
                private List<GiftSubInfo> giftSubInfos;

                /**
                 * GiftSubInfo 节点
                 */
                @Data
                @NoArgsConstructor
                @AllArgsConstructor
                public static class GiftSubInfo {

                    /**
                     * 礼包二级编号
                     * 1.含果盘
                     * 3.含水果
                     * 4.含饮品
                     * 5.含精美艺品
                     * 6.其他
                     * 7.延迟至13点退房
                     * 8.延迟至14点退房
                     * 9.延迟至15点退房
                     * 10.其他
                     * 11.含午餐
                     * 12.含晚餐
                     * 13.含下午茶
                     * 14.含餐券
                     * 15.其他
                     * 16.含景点门票
                     * 17.含演出门票
                     * 18.其他
                     * 19.含店内折扣/抵扣券
                     * 20.含外部折扣/抵扣券
                     * 21.其他
                     * 22.含接站
                     * 23.含接机
                     * 24.含送站
                     * 25.含送机
                     * 26.含景区直通车
                     * 27.其他
                     * 28.其他
                     * 注意：1-6对应一级编号的1，7-10对应一级编号的2，11-15对应一级编号的3，16-18对应一级编号的4，19-21对应一级编号的5，22-27对应一级编号的6，28对应一级编号的7
                     */
                    @NotNull
                    @JsonProperty("SubInfo")
                    private Integer subInfo;
                }
            }
        }

        /**
         * GiftPackage 节点
         */
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class GiftPackage {

            /**
             * 礼包套餐ID
             * 关联RatePlan.PkgProductids
             */
            @NotNull
            @JsonProperty("PkgProductId")
            private String pkgProductId;

            /**
             * 礼包套餐类型
             * 0：礼包，1：套餐
             */
            @NotNull
            @JsonProperty("PkgType")
            private Integer pkgType;

            /**
             * 礼包套餐名字
             */
            @NotNull
            @JsonProperty("PkgProductName")
            private String pkgProductName;

            /**
             * 礼包套餐特别说明
             */
            @Nullable
            @JsonProperty("RuleDescriptionAdditional")
            private String ruleDescriptionAdditional;

            /**
             * 礼包套餐图片
             */
            @Nullable
            @JsonProperty("Pictures")
            private List<Picture> pictures;

            /**
             * X产品列表
             */
            @Nullable
            @JsonProperty("XProducts")
            private List<XProduct> xProducts;

            /**
             * Picture节点
             */
            @Data
            @NoArgsConstructor
            @AllArgsConstructor
            public static class Picture {

                /**
                 * 礼包图片顺序
                 */
                @NotNull
                @JsonProperty("ImgIndex")
                private Integer imgIndex;

                /**
                 * 礼包图片链接
                 */
                @NotNull
                @JsonProperty("ImgUrl")
                private String imgUrl;
            }

            /**
             * XProduct 节点
             */
            @Data
            @NoArgsConstructor
            @AllArgsConstructor
            public static class XProduct {

                /**
                 * X产品ID
                 * X产品即一个具体的礼包
                 */
                @NotNull
                @JsonProperty("XProductId")
                private String xProductId;

                /**
                 * X产品名字
                 */
                @NotNull
                @JsonProperty("XProductName")
                private String xProductName;

                /**
                 * X产品状态
                 * 0：无效，1：有效
                 */
                @NotNull
                @JsonProperty("Status")
                private Integer status;

                /**
                 * X产品类型
                 */
                @Nullable
                @JsonProperty("TypeName")
                private String typeName;

                /**
                 * X产品数量
                 */
                @Nullable
                @JsonProperty("Quantity")
                private String quantity;

                /**
                 * X产品接待时间
                 */
                @Nullable
                @JsonProperty("ReceptionTimes")
                private String receptionTimes;

                /**
                 * X产品适用人数
                 */
                @Nullable
                @JsonProperty("Capacity")
                private String capacity;

                /**
                 * X产品预订电话
                 */
                @Nullable
                @JsonProperty("BookingPhone")
                private String bookingPhone;

                /**
                 * X产品预订规则
                 */
                @Nullable
                @JsonProperty("AppointPolicy")
                private String appointPolicy;
            }

            /**
             * RelatedProduct 节点
             */
            @Data
            @NoArgsConstructor
            @AllArgsConstructor
            public static class RelatedProduct {

                /**
                 * 供应商房型id
                 */
                @NotNull
                @JsonProperty("RoomTypeId")
                private String roomTypeId;

                /**
                 * 价格计划ID
                 */
                @NotNull
                @JsonProperty("RatePlanId")
                private Integer ratePlanId;
            }
        }

        /**
         * HAvailPolicy 节点
         */
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class HAvailPolicy {

            /**
             * 提示编号
             * 关联 RatePlan.HAvailPolicyIds
             */
            @NotNull
            @JsonProperty("Id")
            private String id;

            /**
             * 提示内容
             */
            @Nullable
            @JsonProperty("AvailPolicyText")
            private String availPolicyText;

            /**
             * 有效开始时间
             */
            @Nullable
            @JsonProperty("AvailPolicyStart")
            private String availPolicyStart;

            /**
             * 有效结束时间
             */
            @Nullable
            @JsonProperty("AvailPolicyEnd")
            private String availPolicyEnd;
        }
    }

    /**
     * ExchangeRate 节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExchangeRate {

        /**
         * 货币编码
         * 参考 Currency 节点
         */
        @NotNull
        @JsonProperty("CurrencyCode")
        private String currencyCode;

        /**
         * 汇率值
         * 对应货币转换成人民币的汇率
         */
        @NotNull
        @JsonProperty("Rate")
        private BigDecimal rate;
    }
}
