package com.somytrip.entity.hotel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.somytrip.entity.enums.hotel.HotelOrigin;
import com.somytrip.entity.hotel.ELong.offline.ELongStaticHotelInfoResp;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * 酒店服务指数表(HotelServiceRank)实体类
 *
 * <AUTHOR>
 * @since 2024-01-31 16:49:23
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "hotel_service_rank")
public class HotelServiceRankEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 692167350030997104L;

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 酒店来源
     */
    private HotelOrigin hotelOrigin;
    /**
     * 酒店ID
     */
    private String hotelId;
    /**
     * 酒店主键ID
     */
    private Long hotelPid;
    /**
     * 酒店服务总评分
     */
    private String summaryScore;

    public HotelServiceRankEntity(ELongStaticHotelInfoResp.Detail.ServiceRank serviceRank, String hotelId) {
        this.hotelOrigin = HotelOrigin.ELong;
        this.hotelId = hotelId;
        this.summaryScore = serviceRank.getSummaryScore();
    }
}