package com.somytrip.entity.hotel.ELong.offline;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/**
 * @ClassName: ELongHotelIncrShardingInv
 * @Description: 同程艺龙酒店分片增量请求参数
 * @Author: shadow
 * @Date: 2024/2/22 16:15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ELongHotelIncrShardingReq {

    /**
     * 最后的更新ID
     * 第一次时需配合hotel.incr.sharding.id使用
     */
    @NotNull
    @JsonProperty("LastId")
    private Long lastId;

    /**
     * 分片键
     * 1-16，用于查询指定分片数据
     */
    @NotNull
    @JsonProperty("ShardingKey")
    private Integer shardingKey;

    /**
     * 抓取的数量
     * 不传，默认：1000；最大不能超过5000
     */
    @Nullable
    @JsonProperty("Count")
    private Integer count;
}
