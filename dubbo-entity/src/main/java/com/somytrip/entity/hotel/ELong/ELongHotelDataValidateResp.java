package com.somytrip.entity.hotel.ELong;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.somytrip.entity.enums.hotel.HotelValidateResultCode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName: ELongHotelDataValidateResp
 * @Description: 同程艺龙酒店数据验证(下单前校验)响应
 * @Author: shadow
 * @Date: 2024/2/22 17:45
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ELongHotelDataValidateResp {

    /**
     * 验证结果
     * OK: 正常可预订
     * Product：产品无效或关房
     * Inventory：房量不够
     * Rate: 价格不符
     */
    @NotNull
    @JsonProperty("ResultCode")
    private HotelValidateResultCode resultCode;

    /**
     * 具体结果信息
     */
    @Nullable
    @JsonProperty("ErrorMessage")
    private String errorMessage;

    /**
     * 担保金额
     * 如果是担保订单才有这个值
     */
    @Nullable
    @JsonProperty("GuaranteeRate")
    private BigDecimal guaranteeRate;

    /**
     * 担保金额的货币类型
     */
    @Nullable
    @JsonProperty("CurrencyCode")
    private String currencyCode;

    /**
     * 最晚取消时间
     * 担保订单可取消的时间，如果返回的时间小于当前时间，则代表此订单不可变更取消
     */
    @Nullable
    @JsonProperty("CancelTime")
    private LocalDateTime cancelTime;

    /**
     * 免费取消时间
     */
    @Nullable
    @JsonProperty("FreeCancelTime")
    private LocalDateTime freeCancelTime;

    /**
     * 罚金金额
     * 货币类型为人民币
     * 只代表取消时间处于免费取消时间跟最晚取消时间之间产生的罚金金额，早于免费取消时间不收罚金，晚于最晚取消100%罚金
     */
    @Nullable
    @JsonProperty("PenaltyAmount")
    private BigDecimal penaltyAmount;

    /**
     * 国际验证详情
     * 国际特有字段
     */
    @Nullable
    @JsonProperty("interValidateInfo")
    private InterValidateInfo interValidateInfo;

    /**
     * InterValidateInfo节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class InterValidateInfo {

        /**
         * 政策列表信息
         * 国际特有字段
         */
        @Nullable
        @JsonProperty("ratePlanInfo")
        private RatePlanInfo ratePlanInfo;

        /**
         * 酒店信息
         * 国际特有字段
         */
        @Nullable
        @JsonProperty("orderHotel")
        private OrderHotel orderHotel;

        /**
         * RatePlanInfo节点
         */
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class RatePlanInfo {

            /**
             * 确认类型
             * 确认类型，0延迟确认，1立即确认；
             * 国际特有字段
             */
            @NotNull
            @JsonProperty("ConfirmType")
            private Integer confirmType;

            /**
             * 取消名称
             * 取消名称：限时取消、不可取消、随时取消；
             * 国际特有字段
             */
            @Nullable
            @JsonProperty("CancelName")
            private String cancelName;

            /**
             * 取消政策说明文案
             * 国际特有字段
             */
            @Nullable
            @JsonProperty("CancelDescription")
            private String cancelDescription;

            /**
             * 每晚房价
             * 国际特有字段
             */
            @Nullable
            @JsonProperty("RateNightlyRateList")
            private List<RoomRateNightlyRate> rateNightInRateList;

            /**
             * 取消政策集合
             * 国际特有字段
             */
            @Nullable
            @JsonProperty("CancelPolicyList")
            private List<CancelPolicy> cancelPolicyList;

            /**
             * 房间最大入住人数
             * 国际特有字段
             */
            @Nullable
            @JsonProperty("OccupancyPerRoom")
            private Integer occupancyPerRoom;

            /**
             * 成人数
             * 国际特有字段
             */
            @Nullable
            @JsonProperty("AdultPerRoom")
            private Integer adultPerRoom;

            /**
             * 儿童数
             * 国际特有字段
             */
            @Nullable
            @JsonProperty("ChildPerRoom")
            private Integer childPerRoom;

            /**
             * 儿童年龄
             * 国际特有字段
             */
            @Nullable
            @JsonProperty("RoomChildAge")
            private Integer roomChildAge;

            /**
             * 房间描述
             * 国际特有字段
             */
            @Nullable
            @JsonProperty("RoomDescription")
            private String roomDescription;

            /**
             * 入住需知
             * 国际特有字段
             */
            @Nullable
            @JsonProperty("CheckInInstructions")
            private String checkInstructions;

            /**
             * 客房的可用吸烟偏好
             * 国际特有字段
             */
            @Nullable
            @JsonProperty("SmokingPreferences")
            private String smokingPreferences;

            /**
             * 剩余房间数
             * 国际特有字段
             */
            @Nullable
            @JsonProperty("RestInventoryCount")
            private String restInventoryCount;

            /**
             * 额外人员费用
             * 国际特有字段
             */
            @Nullable
            @JsonProperty("ExtraPersonFee")
            private BigDecimal extraPersonFee;

            /**
             * 额外人员费用(人民币)
             * 国际特有字段
             */
            @Nullable
            @JsonProperty("ExtraPersonFeeRMB")
            private BigDecimal extraPersonFeeRMB;

            /**
             * 床型信息
             * 国际特有字段
             */
            @Nullable
            @JsonProperty("InterBedGroups")
            private List<InterBedGroup> interBedGroups;

            /**
             * 另付税和服务费
             * 国际特有字段
             */
            @Nullable
            @JsonProperty("AdditionalTax")
            private AdditionalTax additionalTax;

            /**
             * RateNightlyRate节点
             */
            @Data
            @NoArgsConstructor
            @AllArgsConstructor
            public static class RoomRateNightlyRate {

                /**
                 * 每晚每间房价(含税费)
                 * 国际特有字段
                 */
                @NotNull
                @JsonProperty("Rate")
                private BigDecimal rate;

                /**
                 * 最小价(不含税费)
                 * 国际特有字段
                 */
                @NotNull
                @JsonProperty("MinRate")
                private BigDecimal minRate;

                /**
                 * 日期
                 * 国际特有字段
                 */
                @NotNull
                @JsonProperty("Date")
                private String date;
            }

            /**
             * CancelPolicy节点
             */
            @Data
            @NoArgsConstructor
            @AllArgsConstructor
            public static class CancelPolicy {

                /**
                 * 罚金
                 * 国际特有字段
                 */
                @NotNull
                @JsonProperty("Penalty")
                private BigDecimal penalty;

                /**
                 * 罚金(人民币)
                 * 国际特有字段
                 */
                @NotNull
                @JsonProperty("PenaltyRMB")
                private BigDecimal penaltyRMB;

                /**
                 * 取消开始时间
                 * 国际特有字段
                 */
                @NotNull
                @JsonProperty("DateFrom")
                private String dateFrom;

                /**
                 * 取消结束时间
                 * 国际特有字段
                 */
                @NotNull
                @JsonProperty("DateTo")
                private String dateTo;
            }

            /**
             * InterBedGroup节点
             */
            @Data
            @NoArgsConstructor
            @AllArgsConstructor
            public static class InterBedGroup {

                /**
                 * 床型信息id
                 * 国际特有字段
                 */
                @Nullable
                @JsonProperty("BedGroupId")
                private String bedGroupId;

                /**
                 * 床型信息描述
                 * 国际特有字段
                 */
                @Nullable
                @JsonProperty("BedGroupDesc")
                private String bedGroupDesc;

                /**
                 * 床类型集合
                 * 国际特有字段
                 */
                @Nullable
                @JsonProperty("BedTypes")
                private List<BedType> bedTypes;

                /**
                 * BedType节点
                 */
                @Data
                @NoArgsConstructor
                @AllArgsConstructor
                public static class BedType {

                    /**
                     * 床类型id
                     * 国际特有字段
                     */
                    @Nullable
                    @JsonProperty("BedTypeId")
                    private String bedTypeId;

                    /**
                     * 床类型名称
                     * 国际特有字段
                     */
                    @Nullable
                    @JsonProperty("BedTypeName")
                    private String bedTypeName;

                    /**
                     * 床类型
                     * 国际特有字段
                     */
                    @Nullable
                    @JsonProperty("BedType")
                    private String bedType;

                    /**
                     * 床数
                     * 国际特有字段
                     */
                    @Nullable
                    @JsonProperty("Count")
                    private Integer count;

                    /**
                     * 床大小
                     * 国际特有字段
                     */
                    @Nullable
                    @JsonProperty("Size")
                    private String size;
                }
            }

            /**
             * AdditionalTax节点
             */
            @Data
            @NoArgsConstructor
            @AllArgsConstructor
            public static class AdditionalTax {

                /**
                 * 另付税和服务费总额
                 */
                @Nullable
                @JsonProperty("TotalAmountRmb")
                private BigDecimal totalAmountRmb;

                /**
                 * 另付税和服务费明细
                 */
                @Nullable
                @JsonProperty("AdditionalTaxItems")
                private List<AdditionalTaxItem> additionalTaxItems;

                /**
                 * AdditionalTaxItem节点
                 */
                @Data
                @NoArgsConstructor
                @AllArgsConstructor
                public static class AdditionalTaxItem {

                    /**
                     * 另付税和服务费明细描述
                     */
                    @Nullable
                    @JsonProperty("Description")
                    private String description;

                    /**
                     * 另付税和服务费明细金额
                     */
                    @Nullable
                    @JsonProperty("Amount")
                    private BigDecimal Amount;
                }
            }
        }

        /**
         * OrderHotel节点
         */
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class OrderHotel {

            /**
             * 物理酒店ID
             * 国际特有字段
             */
            @NotNull
            @JsonProperty("HotelId")
            private String hotelId;

            /**
             * 酒店名称
             * 国际特有字段
             */
            @NotNull
            @JsonProperty("Name")
            private String name;

            /**
             * 酒店英文名称
             * 国际特有字段
             */
            @Nullable
            @JsonProperty("HotelEnglishName")
            private String hotelEnglishName;

            /**
             * 地址
             * 国际特有字段
             */
            @Nullable
            @JsonProperty("Address")
            private String address;

            /**
             * 英文地址
             * 国际特有字段
             */
            @Nullable
            @JsonProperty("AddressEn")
            private String addressEn;

            /**
             * 手机号
             * 国际特有字段
             */
            @Nullable
            @JsonProperty("Phone")
            private String phone;

            /**
             * 城市名称
             * 国际特有字段
             */
            @NotNull
            @JsonProperty("CityName")
            private String cityName;

            /**
             * 国家名称
             * 国际特有字段
             */
            @NotNull
            @JsonProperty("HotelCountryName")
            private String hotelCountryName;

            /**
             * 国家ID
             * 国际特有字段
             */
            @NotNull
            @JsonProperty("HotelCountryId")
            private String hotelCountryId;

            /**
             * 纬度
             * 国际特有字段
             */
            @Nullable
            @JsonProperty("HotelLatitude")
            private String hotelLatitude;

            /**
             * 经度
             * 国际特有字段
             */
            @Nullable
            @JsonProperty("HotelLongitude")
            private String hotelLongitude;

            /**
             * 城市ID
             * 国际特有字段
             */
            @NotNull
            @JsonProperty("CityId")
            private String cityId;

            /**
             * 星级
             * 国际特有字段
             */
            @Nullable
            @JsonProperty("Star")
            private Integer star;
        }
    }
}
