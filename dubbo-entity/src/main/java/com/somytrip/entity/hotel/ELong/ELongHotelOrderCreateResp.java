package com.somytrip.entity.hotel.ELong;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @ClassName: ELongHotelOrderCreateResp
 * @Description: 同程艺龙酒店创建订单响应
 * @Author: shadow
 * @Date: 2024/2/23 17:53
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ELongHotelOrderCreateResp {

    /**
     * 订单编号
     */
    @NotNull
    @JsonProperty("OrderId")
    private Long orderId;

    /**
     * 最晚取消时间
     * 如果日期为9999-12-30 23:00:00等条件代表不限制取消时间，不限制取消时间的订单
     */
    @NotNull
    @JsonProperty("CancelTime")
    private LocalDateTime cancelTime;

    /**
     * 担保金额
     * 如果此订单是担保订单，则在此列出担保金额，币种是人民币(如果提交订单时候的是港币，这里也会被换算成对应金额的人民币)。
     */
    @Nullable
    @JsonProperty("GuaranteeAmount")
    private BigDecimal guaranteeAmount;

    /**
     * 货币类型
     * 如果此订单是担保订单，则在此列出担保金额，币种是人民币(如果提交订单时候的是港币，这里也会被换算成对应金额的人民币)。
     */
    @Nullable
    @JsonProperty("CurrencyCode")
    private String currencyCode;

    /**
     * 是否是即时确认
     * 采用这个属性，就不需要再请求hotel.order.instant接口了。
     * 即时确认只说明这个库存确认方式，最终能否确认给客人还需要考虑订单是否为担保订单，担保订单需要担保成功后才能确认，预付订单同样需要等待客人支付后才能确认。
     * 注：订单即时确认并不代表最终一定被确认，很小概率酒店会拒绝订单，所以请实时同步订单状态，一旦发现订单变为已确认外的状态，请及时通知用户。
     */
    @Nullable
    @JsonProperty("IsInstantConfirm")
    private Boolean isInstantConfirm;

    /**
     * 支付最后期限
     * 如果担保预付订单，提交的信用卡因某种原因支付失败，系统可以保留一段时间，继续支付。
     * 如果这个时间点还没有成功支付，系统将自动取消订单。继续支付请使用 hotel.order.pay接口
     */
    @Nullable
    @JsonProperty("PaymentDeadlineTime")
    private LocalDateTime paymentDeadlineTime;

    /**
     * 支付错误信息
     */
    @Nullable
    @JsonProperty("PaymentMessage")
    private String paymentMessage;
}
