package com.somytrip.entity.hotel.WEBBEDS;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * @ClassName: WEBBEDSRequestBody
 * @Description: WEBBEDS请求体
 * @Author: shadow
 * @Date: 2024/3/18 18:58
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WEBBEDSRequestBody<T> {

    /**
     * 请求参数
     */
    private T requestParam;

    /**
     * 请求头
     */
    private Map<String, String> headers;
}
