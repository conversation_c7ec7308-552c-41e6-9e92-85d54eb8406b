package com.somytrip.entity.hotel.WEBBEDS;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @ClassName: WEBBEDSBookReq
 * @Description: WEBBEDS创建订单请求参数
 * @Author: shadow
 * @Date: 2024/3/19 4:14
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WEBBEDSBookReq extends WEBBEDSBaseReq {

    /**
     * 验证码ID
     */
    @JsonProperty("prebook_id")
    private String prebookId;

    /**
     * 外部订单号
     */
    @JsonProperty("outer_order_no")
    private String outerOrderNo;

    /**
     * 成人名列表
     */
    @JsonProperty("adult_first_names")
    private List<String> adultFirstNames;

    /**
     * 成人姓列表
     */
    @JsonProperty("adult_last_names")
    private List<String> adultLastNames;

    /**
     * 儿童名列表
     */
    @JsonProperty("children_first_names")
    private List<String> childrenFirstName;

    /**
     * 儿童姓列表
     */
    @JsonProperty("children_last_names")
    private List<String> childrenLastName;

    /**
     * 到达时间
     */
    private String arrivalTime;

    /**
     * 评论
     */
    private String comment;

    /**
     * 联系人名称
     */
    @JsonProperty("contact_name")
    private String contactName;

    /**
     * 联系人电话
     */
    @JsonProperty("contact_phone")
    private String contactPhone;

    /**
     * 联系人Email
     */
    @JsonProperty("contact_email")
    private String contactEmail;

    /**
     * 电话国际编码
     */
    @JsonProperty("inter_code")
    private String interCode;
}
