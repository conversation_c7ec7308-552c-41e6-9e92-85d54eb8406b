package com.somytrip.entity.hotel.ELong;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/**
 * @ClassName: ELongHotelOrderDetailReq
 * @Description: 同程艺龙酒店订单详情请求参数
 * @Author: shadow
 * @Date: 2024/2/24 11:25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ELongHotelOrderDetailReq {

    /**
     * 订单编号
     * 如果OrderId不为0，以OrderId为主
     */
    @NotNull
    @JsonProperty("OrderId")
    private Long orderId;

    /**
     * 联盟的订单编号
     * 当OrderId=0的时候，则按AffiliateConfirmationId查询
     */
    @Nullable
    @JsonProperty("AffiliateConfirmationId")
    private String affiliateConfirmationId;

    /**
     * 其他条件
     * 多个英文逗号分隔
     * 1、返回SpecialCancelApply
     */
    @Nullable
    @JsonProperty("Options")
    private String options;
}
