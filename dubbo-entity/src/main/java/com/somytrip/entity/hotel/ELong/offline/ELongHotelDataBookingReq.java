package com.somytrip.entity.hotel.ELong.offline;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.somytrip.entity.enums.hotel.PaymentType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @ClassName: ELongHotelDataBookingReq
 * @Description: 同程艺龙酒店预定数据请求参数
 * @Author: shadow
 * @Date: 2024/2/29 15:51
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ELongHotelDataBookingReq {

    /**
     * 入住日期
     */
    @NotNull
    @JsonProperty("ArrivalDate")
    private LocalDate arrivalDate;

    /**
     * 离店日期
     */
    @NotNull
    @JsonProperty("DepartureDate")
    private LocalDate departureDate;

    /**
     * 酒店编号
     */
    @NotNull
    @JsonProperty("HotelId")
    private String hotelId;

    /**
     * 酒店编码
     */
    @NotNull
    @JsonProperty("HotelCode")
    private String hotelCode;

    /**
     * 展示房型编号
     * 允许为空，当传入时会校验房型编号绑定关系
     */
    @Nullable
    @JsonProperty("RoomId")
    private String roomId;

    /**
     * 销售房型编码
     */
    @NotNull
    @JsonProperty("RoomTypeID")
    private String roomTypeId;

    /**
     * 产品编号
     */
    @NotNull
    @JsonProperty("RatePlanId")
    private Integer ratePlanId;

    /**
     * 支付类型
     * SelfPay--前台自付
     * Prepay--预付
     */
    @NotNull
    @JsonProperty("PaymentType")
    private PaymentType paymentType;

    /**
     * 返回的价格是否通过DRR计算
     * 默认为false；如果为true则返回的Rates节点里面的价格为DRR计算后的，false则为原始价格。
     * 促销产品调用时，需要透传该字段。
     */
    @Nullable
    @JsonProperty("IsRatesWithDRR")
    private Boolean isRatesWithDRR;

    /**
     * 调用监控
     */
    @Nullable
    @JsonProperty("MethodType")
    private Integer methodType;

    /**
     * 马甲Id
     * 从hotel.detail接口获取
     * 国内酒店马甲Id不再有30分钟限制
     * 搜索模式所有产品必传
     */
    @Nullable
    @JsonProperty("LittleMajiaId")
    private String littleMajiaId;

    /**
     * 商品唯一标示
     * 从hotel.detail接口获取
     * 搜索模式所有产品必传
     */
    @Nullable
    @JsonProperty("GoodsUniqId")
    private String goodsUniqId;

    /**
     * 房间数量
     * 当促销产品调用时，需要传如具体房间数量
     */
    @Nullable
    @JsonProperty("NumberOfRooms")
    private Integer numberOfRooms;

    /**
     * 最早到店时间
     * 请传入最早到店时间和最晚到店时间（不传时默认逻辑是：当天预订时，当前时间>=23点，最早最晚到店时间赋值为当天的23点59分，当前时间<23点时，最早到店时间为当前时间+30分钟，最晚到店时间为当前时间+60分钟；
     * 非当天预订时，最早到店时间为入住日的14点，最晚到店时间为入住日的15点）
     */
    @Nullable
    @JsonProperty("EarliestArrivalTime")
    private LocalDateTime earliestArrivalTime;

    /**
     * 最晚到店时间
     * 请传入最早到店时间和最晚到店时间，请注意保证搜索时传入此参数与试单和成单时一致，否则对应担保规则结果会不尽相同，到店时间更改，担保类型为到店时间担保的订单取消规则和担保规则均可能发生变化
     */
    @Nullable
    @JsonProperty("LatestArrivalTime")
    private LocalDateTime latestArrivalTime;
}
