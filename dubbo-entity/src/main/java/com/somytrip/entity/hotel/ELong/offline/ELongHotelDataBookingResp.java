package com.somytrip.entity.hotel.ELong.offline;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.somytrip.entity.hotel.ELong.ELongHotelListResp;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * @ClassName: ELongHotelDataBookingResp
 * @Description: 同程艺龙酒店预订数据响应
 * @Author: shadow
 * @Date: 2024/2/29 15:54
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ELongHotelDataBookingResp {

    /**
     * 产品信息
     */
    @Nullable
    @JsonProperty("RatePlan")
    private ELongHotelDataRpResp.Hotel.RatePlan ratePlan;

    /**
     * 周末价格起始日
     * 为0表示周末设置从周一开始
     * 为0表示到周日结束，但是两个都为0表示无周末设置；
     * 如果开始为3，结束为1，表示从周三到下周1都是周末设置
     * 1代表周一，7代表周日
     */
    @Nullable
    @JsonProperty("WeekendStart")
    private Integer weekendStart;

    /**
     * 周末价格结束日
     */
    @Nullable
    @JsonProperty("WeekendEnd")
    private Integer weekendEnd;

    /**
     * 预订规则
     */
    @Nullable
    @JsonProperty("BookingRules")
    private ELongHotelDataRpResp.Hotel.Supplier.BookingRule[] bookingRules;

    /**
     * 库存
     */
    @Nullable
    @JsonProperty("Inventories")
    private ELongHotelDataInventoryResp.Inventory[] inventories;

    /**
     * 价格
     */
    @Nullable
    @JsonProperty("Rates")
    private Rate[] rates;

    /**
     * 对象状态
     */
    @Nullable
    @JsonProperty("ObjectEffectiveStatus")
    private ObjectEffectiveStatus objectEffectiveStatus;

    /**
     * 到天餐食结果表格
     */
    @Nullable
    @JsonProperty("meals")
    private List<Meal> meals;

    /**
     * 预付规则结果
     */
    @Nullable
    @JsonProperty("PrepayResult")
    private PrepayResult prepayResult;

    /**
     * 现付规则结果
     */
    @Nullable
    @JsonProperty("GuaranteeResult")
    private GuaranteeResult guaranteeResult;

    /**
     * 数据来源
     * DB-数据库
     * DC-直连
     */
    @Nullable
    @JsonProperty("DataSource")
    private String dataSource;

    /**
     * 总卖价
     */
    @Nullable
    @JsonProperty("TotalRate")
    private BigDecimal totalRate;

    /**
     * Rate节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Rate {

        /**
         * 酒店编号
         */
        @NotNull
        @JsonProperty("HotelId")
        private String hotelId;

        /**
         * 房型编号
         */
        @NotNull
        @JsonProperty("RoomTypeId")
        private String roomTypeId;

        /**
         * 产品编号
         */
        @NotNull
        @JsonProperty("RatePlanId")
        private Integer ratePlanId;

        /**
         * 开始时间
         */
        @NotNull
        @JsonProperty("StartDate")
        private LocalDate startDate;

        /**
         * 结束时间
         */
        @NotNull
        @JsonProperty("EndDate")
        private LocalDate endDate;

        /**
         * 酒店编码
         */
        @Nullable
        @JsonProperty("HotelCode")
        private String hotelCode;

        /**
         * 状态
         * false--无效、true--有效
         */
        @NotNull
        @JsonProperty("Status")
        private Boolean status;

        /**
         * 平日卖价
         * -1代表此房无价，无价和满房都不能进行预订
         */
        @NotNull
        @JsonProperty("Member")
        private BigDecimal member;

        /**
         * 周末卖价
         * 同上，在周末时使用此价格，周末设置参考hotel.data.rp接口
         */
        @NotNull
        @JsonProperty("Weekend")
        private BigDecimal weekend;

        /**
         * 平日结算价
         * 同上，开通了结算价模式的接入方才可以使用
         */
        @NotNull
        @JsonProperty("MemberCost")
        private BigDecimal memberCost;

        /**
         * 周末结算价
         * 同上，开通了结算价模式的接入方才可以使用
         */
        @NotNull
        @JsonProperty("WeekendCost")
        private BigDecimal weekendCost;

        /**
         * 加床价
         * -1代表不能加床，0-免费加床，大于0表示加床的费用
         */
        @Nullable
        @JsonProperty("AddBed")
        private BigDecimal addBed;

        /**
         * 价格ID
         */
        @Nullable
        @JsonProperty("PriceID")
        private Long priceID;

        /**
         * 货币类型
         */
        @Nullable
        @JsonProperty("CurrencyCode")
        private String currencyCode;

        /**
         * 每间的同程促销明细
         */
        @Nullable
        @JsonProperty("UsedPromotionDayRoomValues")
        private UsedPromotionDayRoomValue[] usedPromotionDayRoomValues;

        /**
         * UsedPromotionDayRoomValue节点
         */
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class UsedPromotionDayRoomValue {

            /**
             * 房间号
             */
            @Nullable
            @JsonProperty("RoomNumber")
            private String roomNumber;

            /**
             * 促销明细
             * 此间促销明细
             */
            @Nullable
            @JsonProperty("PromotionDetailList")
            private List<PromotionDetail> promotionDetailList;

            /**
             * PromotionDetail节点
             */
            @Data
            @NoArgsConstructor
            @AllArgsConstructor
            public static class PromotionDetail {

                /**
                 * 促销金额
                 */
                @Nullable
                @JsonProperty("Amount")
                private BigDecimal amount;

                /**
                 * 促销类型
                 * 促销类型 9:立减
                 */
                @Nullable
                @JsonProperty("PromotionType")
                private Integer promotionType;
            }
        }
    }

    /**
     * ObjectEffectiveStatus节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ObjectEffectiveStatus {

        /**
         * HotelId 对应对象的状态
         * true -- 有效，false -- 无效
         */
        @NotNull
        @JsonProperty("HotelIdStatus")
        private Boolean hotelIdStatus;

        /**
         * RoomId 对应对象的状态
         */
        @NotNull
        @JsonProperty("RoomStatus")
        private Boolean roomStatus;

        /**
         * RatePlanId 对应对象的状态
         */
        @NotNull
        @JsonProperty("RatePlanStatus")
        private Boolean ratePlanStatus;

        /**
         * HotelCode 对应对象的状态
         */
        @NotNull
        @JsonProperty("HotelCodeStatus")
        private Boolean hotelCodeStatus;

        /**
         * RoomTypeId 对应对象的状态
         */
        @NotNull
        @JsonProperty("RoomTypeStatus")
        private Boolean roomTypeStatus;

        /**
         * RoomTypeId 和 RatePlanId 是否存在绑定关系
         */
        @NotNull
        @JsonProperty("ProductRelation")
        private Boolean productRelation;
    }

    /**
     * Meals节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Meal {

        /**
         * 是否存在餐食表格
         * 为 true 代表取 "dayMealTable" 餐食表格字段，查看每天的餐食情况
         */
        @NotNull
        @JsonProperty("hasMealTable")
        private Boolean hasMealTable;

        /**
         * 餐食文案描述
         */
        @NotNull
        @JsonProperty("mealCopyWriting")
        private String mealCopyWriting;

        /**
         * 每日餐食表格
         * 包含多个 DayMeal，见 DayMeal 节点
         */
        @NotNull
        @JsonProperty("dayMealTable")
        private List<DayMeal> dayMealTable;

        /**
         * DayMeal节点
         */
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class DayMeal {

            /**
             * 餐食的日期，格式为 yyyy-MM-dd，例如 2021-08-12
             */
            @NotNull
            @JsonProperty("date")
            private String date;

            /**
             * 是否使用动态餐食
             * 为 true 取 dynamicMealDesc
             * 为 false 取 breakfastDesc、lunchDesc、dinnerDesc
             */
            @NotNull
            @JsonProperty("useDynamicMeal")
            private Boolean useDynamicMeal;

            /**
             * 动态餐食描述
             */
            @Nullable
            @JsonProperty("dynamicMealDesc")
            private String dynamicMealDesc;

            /**
             * 早餐数量
             */
            @NotNull
            @JsonProperty("breakfastShare")
            private Integer breakfastShare;

            /**
             * 早餐描述
             */
            @Nullable
            @JsonProperty("breakfastDesc")
            private String breakfastDesc;

            /**
             * 午餐数量
             */
            @NotNull
            @JsonProperty("lunchShare")
            private Integer lunchShare;

            /**
             * 午餐描述
             */
            @Nullable
            @JsonProperty("lunchDesc")
            private String lunchDesc;

            /**
             * 晚餐数量
             */
            @NotNull
            @JsonProperty("dinnerShare")
            private Integer dinnerShare;

            /**
             * 晚餐描述
             */
            @Nullable
            @JsonProperty("dinnerDesc")
            private String dinnerDesc;

            /**
             * 到天餐食描述
             */
            @Nullable
            @JsonProperty("dayMealCopyWriting")
            private String dayMealCopyWriting;
        }
    }

    /**
     * PrepayResult节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PrepayResult {

        /**
         * 取消规则
         */
        @NotNull
        @JsonProperty("CancelDescription")
        private String cancelDescription;

        /**
         * 取消类型
         * 1：免费取消
         * 2：收费取消
         * 3：限时取消
         * 4：不可取消
         */
        @NotNull
        @JsonProperty("CancelType")
        private Integer cancelType;

        /**
         * 是否使用阶梯担保规则
         */
        @Nullable
        @JsonProperty("LadderVouch")
        private Boolean ladderVouch;

        /**
         * 取消规则明细
         * 参考 LadderParse 节点
         */
        @NotNull
        @JsonProperty("LadderParseList")
        private ELongHotelListResp.Hotel.Room.RatePlan.LadderParse[] ladderParseList;

        /**
         * 取消规则标签
         * 如果规则是任意取消和不可取消的没有这个字段和对应值
         * 限时取消和付费取消则会返回该字段
         */
        @Nullable
        @JsonProperty("CancelTag")
        private String cancelTag;
    }

    /**
     * GuaranteeResult节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GuaranteeResult {

        /**
         * 到店时间触发时的担保金额
         * 货币类型为原币种，即所在 RatePlan 节点下的 CurrencyCode
         * （废弃）
         */
        @NotNull
        @JsonProperty("MoneyArrivalTime")
        private Double moneyArrivalTime;

        /**
         * 需要担保的到店时间（格式：hh:mm）
         * 用户填写的最晚到店早于此时间，不需要关注 MoneyArrivalTime，否则需要担保 MoneyArrivalTime
         */
        @NotNull
        @JsonProperty("ArrivalTime")
        private String arrivalTime;

        /**
         * 房量担保分割点
         * 用户预订间数小于此值不需要担保，否则需要担保
         */
        @NotNull
        @JsonProperty("RoomCount")
        private Integer roomCount;

        /**
         * 担保类型
         * 二进制位表示:
         * 1: 到店时间担保
         * 2: 房量担保
         * 3: 预订即需担保
         * 4: 免担保
         * 0：免担保；2：到店时间担保；4：房量担保；8：预定即需担保
         */
        @NotNull
        @JsonProperty("GuaranteeType")
        private Integer guaranteeType;

        /**
         * 当前条件下需要担保的金额
         * 具体见取消规则明细
         */
        @NotNull
        @JsonProperty("GuaranteeMoney")
        private Double guaranteeMoney;

        /**
         * 当前条件下是否需要担保
         */
        @NotNull
        @JsonProperty("NeedGuarantee")
        private Boolean needGuarantee;

        /**
         * 可以取消的时间点，单位秒
         * （废弃）
         * 之前可以取消，之后不可取消，不可取消时为 -28800
         * 免费取消时为 Long.MAX_VALUE
         * 其他情况下为北京时间的时间戳
         */
        @NotNull
        @JsonProperty("CancelTime")
        private Long cancelTime;

        /**
         * 取消规则详细描述
         */
        @Nullable
        @JsonProperty("CancelDescription")
        private String cancelDescription;

        /**
         * 取消规则标签
         * 如果规则是任意取消和不可取消的没有这个字段和对应值
         * 限时取消和付费取消则会返回该字段
         */
        @Nullable
        @JsonProperty("CancelTag")
        private String cancelTag;

        /**
         * 取消类型
         * 1: 免费取消
         * 2: 付费取消
         * 3: 可取消
         * 4: 不可取消
         */
        @NotNull
        @JsonProperty("CancelType")
        private Integer cancelType;

        /**
         * 取消规则明细
         * 参考 LadderParse 节点
         */
        @NotNull
        @JsonProperty("LadderParseList")
        private ELongHotelListResp.Hotel.Room.RatePlan.LadderParse[] ladderParseList;
    }
}
