package com.somytrip.entity.hotel.WEBBEDS;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @ClassName: WEBBEDSPricesReq
 * @Description: WEBBEDS酒店价格请求参数
 * @Author: shadow
 * @Date: 2024/3/19 3:41
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WEBBEDSPricesReq extends WEBBEDSBaseReq {

    /**
     * 酒店ID列表
     * 与城市ID必须传一个  最大支持20个
     */
    @JsonProperty("hotel_ids")
    private List<Integer> hotelIds;

    /**
     * 城市ID
     * 与国家ID必须传一个
     */
    @JsonProperty("cityId")
    private Integer cityId;

    /**
     * 入住日期
     */
    @JsonProperty("check_in_date")
    private String checkInDate;

    /**
     * 离店日期
     */
    @JsonProperty("check_out_date")
    private String checkOutDate;

    /**
     * 房间数量
     */
    @JsonProperty("number_of_rooms")
    private Integer numberOfRooms = 1;

    /**
     * 入住成人数
     */
    @JsonProperty("number_of_adults")
    private Integer numberOfAdults;

    /**
     * 入住儿童数
     */
    @JsonProperty("number_of_children")
    private Integer numberOfChildren;

    /**
     * 儿童年龄数组
     */
    @JsonProperty("children_ages")
    private List<Integer> childrenAges;

    /**
     * ISO货币代码
     * 默认 CNY
     */
    private String currency;

    /**
     * ISO国籍代码 客人国籍
     */
    @JsonProperty("client_nationality")
    private String clientNationality;

    /**
     * 按城市查询上次请求返回的酒店ID
     */
    @JsonProperty("last_hotel_id")
    private Long lastHotelId;
}
