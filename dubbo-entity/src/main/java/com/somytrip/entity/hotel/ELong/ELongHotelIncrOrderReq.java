package com.somytrip.entity.hotel.ELong;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/**
 * @ClassName: ELongHotelIncrOrderReq
 * @Description: 同程艺龙酒店订单增量请求参数
 * @Author: shadow
 * @Date: 2024/2/24 14:55
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ELongHotelIncrOrderReq {

    /**
     * 最后的更新ID
     */
    @NotNull
    @JsonProperty("LastId")
    private Long lastId;

    /**
     * 抓取的数量
     * 不传，默认：1000；最大不能超过5000
     */
    @Nullable
    @JsonProperty("Count")
    private Integer count;
}
