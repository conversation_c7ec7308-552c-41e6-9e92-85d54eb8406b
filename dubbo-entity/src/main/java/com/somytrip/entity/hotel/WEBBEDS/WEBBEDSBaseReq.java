package com.somytrip.entity.hotel.WEBBEDS;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName: WEBBEDSBaseReq
 * @Description: WEBBEDS基本请求参数
 * @Author: shadow
 * @Date: 2024/3/18 19:30
 */
@Data
public class WEBBEDSBaseReq {

    /**
     * 语言
     */
    @JsonProperty("language")
    private String language = "zh-Hans";

    /**
     * 页码
     * 从0开始
     */
    @JsonProperty("page_index")
    private Integer pageIndex = 0;

    /**
     * 页容量
     */
    @JsonProperty("page_size")
    private Integer pageSize = 10;
}
