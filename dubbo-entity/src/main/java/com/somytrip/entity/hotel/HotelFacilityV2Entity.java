package com.somytrip.entity.hotel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.somytrip.entity.enums.hotel.*;
import com.somytrip.entity.hotel.ELong.offline.ELongStaticHotelInfoResp;
import com.somytrip.entity.hotel.NCNB.NCNBBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 酒店设施v2表(HotelFacilitiesV2)实体类
 *
 * <AUTHOR>
 * @since 2024-02-02 11:04:36
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "hotel_facilities_v2", autoResultMap = true)
public class HotelFacilityV2Entity implements Serializable {

    @Serial
    private static final long serialVersionUID = 147349964721511060L;

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 酒店来源
     */
    private HotelOrigin hotelOrigin;
    /**
     * 设施分类ID
     */
    private Long facilityTypeId;
    /**
     * 设施ID
     */
    private Long facilityId;
    /**
     * 设施名称
     */
    private String facilityName;
    /**
     * 图标ID
     */
    private String iconId;
    /**
     * 设施收费信息
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<FeeInfoEntity> feeInfo;
    /**
     * 设施营业时间
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<BusinessHourInfoEntity> businessHourInfos;
    /**
     * 年龄限制信息
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private AgeLimitInfoEntity ageLimitInfo;
    /**
     * 设施预约信息
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private ReservationInfoEntity reservationInfo;
    /**
     * 国际化字典
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Map<String, String>> localeDict;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public HotelFacilityV2Entity(ELongStaticHotelInfoResp.FacilityType.FacilityInfo elongFacilityInfo, Long facilityTypeId) {
        this.hotelOrigin = HotelOrigin.ELong;
        this.facilityTypeId = facilityTypeId;
        this.facilityId = elongFacilityInfo.getFacilityId();
        this.facilityName = elongFacilityInfo.getFacilityName();
        if (elongFacilityInfo.getFeeInfo() != null) {
            List<FeeInfoEntity> feeInfoEntities = new ArrayList<>();
            for (ELongStaticHotelInfoResp.FacilityType.FacilityInfo.FeeInfo feeInfo : elongFacilityInfo.getFeeInfo()) {
                feeInfoEntities.add(new FeeInfoEntity(feeInfo));
            }
            this.feeInfo = feeInfoEntities;
        }
        if (elongFacilityInfo.getBusinessHourInfos() != null) {
            List<BusinessHourInfoEntity> businessHourInfoEntities = new ArrayList<>();
            for (ELongStaticHotelInfoResp.FacilityType.FacilityInfo.BusinessHourInfo businessHourInfo
                    : elongFacilityInfo.getBusinessHourInfos()) {
                businessHourInfoEntities.add(new BusinessHourInfoEntity(businessHourInfo));
            }
            this.businessHourInfos = businessHourInfoEntities;
        }
        if (elongFacilityInfo.getAgeLimitInfo() != null) {
            this.ageLimitInfo = new AgeLimitInfoEntity(elongFacilityInfo.getAgeLimitInfo());
        }
        if (elongFacilityInfo.getReservationInfo() != null) {
            this.reservationInfo = new ReservationInfoEntity(elongFacilityInfo.getReservationInfo());
        }
    }

    public HotelFacilityV2Entity(ELongStaticHotelInfoResp.Facility bookingNoticeFacility) {
        this.hotelOrigin = HotelOrigin.ELong;
        this.facilityId = Long.valueOf(bookingNoticeFacility.getFacilityId());
        this.facilityName = bookingNoticeFacility.getFacilityName();
    }

    public HotelFacilityV2Entity(NCNBBaseEntity.FacilityInfos.Service ncnbService) {
        this.hotelOrigin = HotelOrigin.NCNB;
        this.facilityTypeId = Long.valueOf(ncnbService.getGroupId());
        this.facilityName = ncnbService.getServiceName();
    }

    /**
     * 设施收费信息节点
     */
    @Data
    public static class FeeInfoEntity {

        /**
         * 设施收费类型(Paid：收费；Free：免费；None：未知)
         */
//        @EnumValue
        private FeeChargeType feeChargeType;

        /**
         * 设施收费明细
         */
        private List<FeeDetailEntity> feeDetail;

        public FeeInfoEntity(ELongStaticHotelInfoResp.FacilityType.FacilityInfo.FeeInfo feeInfo) {
            if (feeInfo == null) {
                return;
            }
            this.feeChargeType = feeInfo.getFeeChargeType();
            List<ELongStaticHotelInfoResp.FacilityType.FacilityInfo.FeeInfo.FeeDetail> feeDetail = feeInfo.getFeeDetail();
            if (feeDetail == null) {
                return;
            }
            this.feeDetail = feeDetail.stream().map(FeeDetailEntity::new).collect(Collectors.toList());
        }

        /**
         * 设施收费明细节点
         */
        @Data
        public static class FeeDetailEntity {

            /**
             * 设施费用金额
             */
            private BigDecimal amount;

            /**
             * 设施费用币种
             */
            private String currency;

            /**
             * 设施费用类型
             * Time：次数；Minute：分钟；Quarter：一刻钟；HalfHour：半小时；Hour：小时；Day：天；
             * Week：周；Person：人；Bed：床；Car：车；Bottle：瓶
             */
            private FeeTimeType feeTimeType;

            public FeeDetailEntity(ELongStaticHotelInfoResp.FacilityType.FacilityInfo.FeeInfo.FeeDetail feeDetail) {
                this.amount = feeDetail.getAmount();
                this.currency = feeDetail.getCurrency();
                this.feeTimeType = feeDetail.getFeeTimeType();
            }
        }
    }

    /**
     * 设施营业时间节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BusinessHourInfoEntity {

        /**
         * 设施营业时间类型(OpenDay：开放时间；CloseDay:关闭时间)
         */
        private OpenDayType openDayType;

        /**
         * 设施营业时间开始时间(HH:MM)
         */
        private String startTime;

        /**
         * 设施营业时间结束时间(HH:MM)
         */
        private String endTime;

        /**
         * 设施营业时间周有效
         * 适用星期几，从周一到周日 （如1110110表示周四、周日无效）
         */
        private String weeklyIndex;

        public BusinessHourInfoEntity(
                ELongStaticHotelInfoResp.FacilityType.FacilityInfo.BusinessHourInfo businessHourInfo
        ) {
            this.openDayType = businessHourInfo.getOpenDayType();
            this.startTime = businessHourInfo.getStartTime();
            this.endTime = businessHourInfo.getEndTime();
            this.weeklyIndex = businessHourInfo.getWeeklyIndex();
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AgeLimitInfoEntity {

        /**
         * 设施使用最小年龄
         */
        private String minAge;

        /**
         * 设施使用最大年龄
         */
        private String maxAge;

        public AgeLimitInfoEntity(ELongStaticHotelInfoResp.FacilityType.FacilityInfo.AgeLimitInfo ageLimitInfo) {
            if (ageLimitInfo == null) {
                return;
            }
            this.minAge = ageLimitInfo.getMinAge();
            this.maxAge = ageLimitInfo.getMaxAge();
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ReservationInfoEntity {

        /**
         * 设施预约限制(T：需要预约；F：无需预约)
         */
        private Reserve reserve;

        /**
         * 提前预约时间
         */
        private String time;

        /**
         * 提前预约时间单位(Dia：天；Hora：小时；Minuto：分钟)
         */
        private TimeUnit timeUnit;

        public ReservationInfoEntity(
                ELongStaticHotelInfoResp.FacilityType.FacilityInfo.ReservationInfo reservationInfo
        ) {
            if (reservationInfo == null) {
                return;
            }
            this.reserve = reservationInfo.getReserve();
            this.time = reservationInfo.getTime();
            this.timeUnit = reservationInfo.getTimeUnit();
        }
    }
}

