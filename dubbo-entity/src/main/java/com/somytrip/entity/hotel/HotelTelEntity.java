package com.somytrip.entity.hotel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.somytrip.entity.enums.hotel.HotelOrigin;
import com.somytrip.entity.hotel.ELong.offline.ELongStaticHotelInfoResp;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * (HotelTel)实体类
 *
 * <AUTHOR>
 * @since 2024-01-31 17:10:37
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "hotel_tel")
public class HotelTelEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 541739158253406260L;

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 酒店来源
     */
    private HotelOrigin hotelOrigin;
    /**
     * 酒店ID
     */
    private String hotelId;
    /**
     * 酒店主键ID
     */
    private Long hotelPid;
    /**
     * 类型
     */
    private String type;
    /**
     * 国家码
     */
    private String nationCode;
    /**
     * 区域码
     */
    private String areaCode;
    /**
     * 主机号
     */
    private String mainCode;
    /**
     * 分机号
     */
    private String extCode;

    public HotelTelEntity(ELongStaticHotelInfoResp.Detail.Tel elongTel, String hotelId) {
        this.hotelOrigin = HotelOrigin.ELong;
        this.hotelId = hotelId;
        this.type = elongTel.getType();
        this.nationCode = elongTel.getNationCode();
        this.areaCode = elongTel.getAreaCode();
        this.mainCode = elongTel.getMainCode();
        this.extCode = elongTel.getExtCode();
    }
}

