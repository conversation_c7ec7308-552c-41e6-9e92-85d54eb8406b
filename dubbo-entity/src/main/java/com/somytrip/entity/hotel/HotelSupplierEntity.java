package com.somytrip.entity.hotel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.somytrip.entity.enums.hotel.HotelOrigin;
import com.somytrip.entity.hotel.ELong.offline.ELongHotelDataRpResp;
import com.somytrip.entity.hotel.ELong.offline.ELongStaticHotelInfoResp;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 酒店供应商(HotelSuppliers)实体类
 *
 * <AUTHOR>
 * @since 2024-02-02 15:18:26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "hotel_suppliers", autoResultMap = true)
public class HotelSupplierEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 476325889780578966L;
    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 酒店主键ID
     */
    private Long hotelPid;
    /**
     * 酒店ID
     */
    private String hotelId;
    /**
     * 酒店来源
     */
    private HotelOrigin hotelOrigin;
    /**
     * 供应商ID
     */
    private String supplierId;
    /**
     * 对应供应商编码
     */
    private String hotelCode;
    /**
     * 星期开始设置，用于房价的周末价计算。为0表示周末设置从周一开始。
     */
    private Integer weekendStart;
    /**
     * 星期结束设置，为0表示到周日结束，但是两个都为0表示无周末设置； 如果开始为3，结束为1，表示从周三到下周1都是周末设置。1代表周一，7代表周日。
     */
    private Integer weekendEnd;
    /**
     * 预定规则
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<BookingRuleEntity> bookingRules;
    /**
     * 房间映射关系
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<RoomEntity> rooms;
    /**
     * 预付产品发票模式
     */
    private String invoiceMode;
    /**
     * 酒店等级
     */
    private String hotelLevel;
    /**
     * 直连供应商
     */
    private String dcSupplierId;
    /**
     * 供应商类型
     */
    private String supplierType;
    /**
     * 即时确认的销售房型，多个房型以逗号分隔
     */
    private String instantRoomTypes;
    /**
     * 供应商有效状态，是否有效；无效的供应商关联的产品和库存不能销售
     */
    private Integer status;
    /**
     * 酒店使用库存和价格的方式
     */
    private String invokeType;
    /**
     * 特殊政策
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private AvailPolicyEntity availPolicy;
    /**
     * 温馨提示
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private HelpfulTipEntity helpfulTip;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public HotelSupplierEntity(ELongHotelDataRpResp.Hotel.Supplier elongRpSupplier, String hotelId) {
        this.hotelOrigin = HotelOrigin.ELong;
        this.hotelId = hotelId;
        this.hotelCode = elongRpSupplier.getHotelCode();
        this.supplierId = elongRpSupplier.getSupplierId();
        this.weekendStart = elongRpSupplier.getWeekendStart();
        this.weekendEnd = elongRpSupplier.getWeekendEnd();
        if (elongRpSupplier.getBookingRules() != null) {
            List<BookingRuleEntity> bookingRuleEntityList = new ArrayList<>();
            for (ELongHotelDataRpResp.Hotel.Supplier.BookingRule bookingRule : elongRpSupplier.getBookingRules()) {
                bookingRuleEntityList.add(new BookingRuleEntity(bookingRule));
            }
            this.bookingRules = bookingRuleEntityList;
        }
        if (elongRpSupplier.getRooms() != null) {
            List<RoomEntity> roomEntityList = new ArrayList<>();
            for (ELongHotelDataRpResp.Hotel.Supplier.Room room : elongRpSupplier.getRooms()) {
                roomEntityList.add(new RoomEntity(room));
            }
            this.rooms = roomEntityList;
        }
        this.invoiceMode = elongRpSupplier.getInvoiceMode();
        this.hotelLevel = elongRpSupplier.getHotelLevel();
        this.dcSupplierId = elongRpSupplier.getDCSupplierId();
        this.supplierType = elongRpSupplier.getSupplierType();
    }

    public HotelSupplierEntity(ELongStaticHotelInfoResp.Supplier elongInfoSupplier, String hotelId) {
        this.hotelOrigin = HotelOrigin.ELong;
        this.hotelId = hotelId;
        this.hotelCode = elongInfoSupplier.getHotelCode();
        this.supplierId = elongInfoSupplier.getSupplierId();
        this.weekendStart = elongInfoSupplier.getWeekendStart();
        this.weekendEnd = elongInfoSupplier.getWeekendEnd();
        this.instantRoomTypes = elongInfoSupplier.getInstantRoomTypes();
        this.status = elongInfoSupplier.getStatus();
        this.invokeType = elongInfoSupplier.getInvokeType();
        if (elongInfoSupplier.getAvailPolicy() != null) {
            this.availPolicy = new AvailPolicyEntity(elongInfoSupplier.getAvailPolicy());
        }
        if (elongInfoSupplier.getHelpfulTip() != null) {
            this.helpfulTip = new HelpfulTipEntity(elongInfoSupplier.getHelpfulTip());
        }
    }

    /**
     * BookingRule节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BookingRuleEntity {

        /**
         * 规则类型
         * NeedNationality：务必提供客人国籍
         * PerRoomPerName：您预订了N间房，请您提供不少于N的入住客人姓名
         * ForeignerNeedEnName：此酒店要求外宾务必留英文拼写 ，港澳台酒店出现这个字段的时候，所有人都需要填写英文名或姓名拼音
         * RejectCheckinTime：几点到几点酒店不接受预订 , 此处校验的是下单时的当前时间
         * NeedPhoneNo：务必提供联系人手机号(请加在联系人结点Contact上)
         */
        private String typeCode;

        /**
         * 关联的销售房型Id
         * all 表示所有房型
         */
        private String roomTypeIds;

        /**
         * 描述
         */
        private String description;

        /**
         * 日期类型
         * BookDay –预订日期（订单的创建日期）
         */
        private String dateType;

        /**
         * 开始日期
         */
        private String startDate;

        /**
         * 结束日期
         */
        private String endDate;

        /**
         * 每天开始时间
         * 针对日期段内每天生效, 当TypeCode 为RejectCheckinTime时表示当前预订时间在StartHour到EndHour区间内酒店不接受预订。
         * 当EndHour大于24点的时候是表示第二天的几点加上了24小时，如26:00表示第二天的2点。
         */
        private String startHour;

        /**
         * 每天结束时间
         */
        private String endHour;

        public BookingRuleEntity(ELongHotelDataRpResp.Hotel.Supplier.BookingRule elongBookingRule) {
            this.typeCode = elongBookingRule.getTypeCode();
            this.roomTypeIds = elongBookingRule.getRoomTypeIds();
            this.description = elongBookingRule.getDescription();
            this.dateType = elongBookingRule.getDateType();
            this.startDate = elongBookingRule.getStartDate();
            this.endDate = elongBookingRule.getEndDate();
            this.startHour = elongBookingRule.getStartHour();
            this.endHour = elongBookingRule.getEndHour();
        }
    }

    /**
     * Room节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RoomEntity {

        /**
         * 销售房型编号
         * 关联RatePlan.RoomTypeIds
         */
        private String roomTypeId;

        /**
         * 展示房型编号
         */
        private String roomId;

        /**
         * 销售房型可用状态
         * true---该销售房型可销售，false-该销售房型不能销售
         */
        private Boolean status;

        public RoomEntity(ELongHotelDataRpResp.Hotel.Supplier.Room elongRpRoom) {
            this.roomTypeId = elongRpRoom.getRoomTypeId();
            this.roomId = elongRpRoom.getRoomId();
            this.status = elongRpRoom.getStatus();
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AvailPolicyEntity {

        /**
         * 特殊政策开始日期
         * 格式：yyyy-MM-dd'T'HH:mm:ss'+08:00'；例如：2021-09-15T00:00:00+08:00
         */
        private String startDate;

        /**
         * 特殊政策结束日期
         * 格式：yyyy-MM-dd'T'HH:mm:ss'+08:00'；例如：2021-09-15T00:00:00+08:00
         */
        private String endDate;

        /**
         * 特殊政策中文描述
         * 例如：此酒店不能接待外宾；
         * 例如：1、客人延住请发延住单，不要发新订单;2、此酒店无停车场。
         */
        private String description;

        /**
         * 特殊政策英文描述
         */
        private String descriptionEn;

        public AvailPolicyEntity(ELongStaticHotelInfoResp.Supplier.AvailPolicy availPolicy) {
            if (availPolicy == null) {
                return;
            }
            this.startDate = availPolicy.getStartDate();
            this.endDate = availPolicy.getEndDate();
            this.description = availPolicy.getDescription();
            this.descriptionEn = availPolicy.getDescriptionEn();
        }
    }

    /**
     * 温馨提示节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HelpfulTipEntity {

        /**
         * 温馨提示开始日期
         */
        private String startDate;

        /**
         * 温馨提示结束日期
         */
        private String endDate;

        /**
         * 温馨提示中文描述
         */
        private String description;

        /**
         * 温馨提示英文描述
         */
        private String descriptionEn;

        public HelpfulTipEntity(ELongStaticHotelInfoResp.Supplier.HelpfulTip helpfulTip) {
            if (helpfulTip == null) {
                return;
            }
            this.startDate = helpfulTip.getStartDate();
            this.endDate = helpfulTip.getEndDate();
            this.description = helpfulTip.getDescription();
            this.descriptionEn = helpfulTip.getDescriptionEn();
        }
    }
}

