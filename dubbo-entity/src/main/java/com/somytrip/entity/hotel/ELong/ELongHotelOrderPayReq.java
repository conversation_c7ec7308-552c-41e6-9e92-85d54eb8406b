package com.somytrip.entity.hotel.ELong;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.math.BigDecimal;

/**
 * @ClassName: ELongHotelOrderPayReq
 * @Description: 同程艺龙酒店订单支付请求参数
 * @Author: shadow
 * @Date: 2024/2/24 14:48
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ELongHotelOrderPayReq {

    /**
     * 订单号
     */
    @NotNull
    @JsonProperty("OrderId")
    private Long orderId;

    /**
     * 是否已担保或已付款
     * 开通了公司担保业务的合作伙伴才能使用该属性
     * 有关支付逻辑的详细说明请参考相关文档
     */
    @NotNull
    @JsonProperty("IsGuaranteeOrCharged")
    private Boolean isGuaranteeOrCharged;

    /**
     * 信用卡
     * 担保订单和预付订单才须传信用卡
     */
    @Nullable
    @JsonProperty("CreditCard")
    private ELongHotelOrderCreateReq.CreditCard creditCard;

    /**
     * 第三方支付
     * 担保订单和预付订单才须传第三方支付信息
     * 如果IsGuaranteeOrCharged为false，则一定不能传第三方支付信息
     */
    @Nullable
    @JsonProperty("DoveCorpCard")
    private ELongHotelOrderCreateReq.DoveCorpCard doveCorpCard;

    /**
     * 支付金额
     * 人民币价格
     */
    @NotNull
    @JsonProperty("Amount")
    private BigDecimal amount;
}
