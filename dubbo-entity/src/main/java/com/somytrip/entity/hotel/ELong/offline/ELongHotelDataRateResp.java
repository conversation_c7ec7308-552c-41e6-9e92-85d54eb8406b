package com.somytrip.entity.hotel.ELong.offline;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName: ELongHotelDataRateResp
 * @Description: 同程艺龙静态酒店价格信息响应
 * @Author: shadow
 * @Date: 2024/2/20 10:22
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ELongHotelDataRateResp {

    /**
     * 价格集合
     */
    @Nullable
    @JsonProperty("Rates")
    private List<Rate> rates;

    /**
     * Rate节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Rate {

        /**
         * 酒店ID
         */
        @NotNull
        @JsonProperty("HotelID")
        private String hotelId;

        /**
         * 房型ID
         */
        @NotNull
        @JsonProperty("RoomTypeId")
        private String roomTypeId;

        /**
         * 产品 ID
         */
        @NotNull
        @JsonProperty("RateplanId")
        private Integer ratePlanId;

        /**
         * 付款类型
         */
        @Nullable
        @JsonProperty("PaymentType")
        private String paymentType;

        /**
         * 开始时间
         */
        @NotNull
        @JsonProperty("StartDate")
        private String startDate;

        /**
         * 结束时间
         */
        @NotNull
        @JsonProperty("EndDate")
        private String endDate;

        /**
         * 供应商ID
         */
        @Nullable
        @JsonProperty("HotelCode")
        private String hotelCode;

        /**
         * 状态
         */
        @NotNull
        @JsonProperty("Status")
        private Boolean status;

        /**
         * 平日卖价
         * -1代表此房无价，无价和满房都不能进行预订
         */
        @NotNull
        @JsonProperty("Member")
        private BigDecimal member;

        /**
         * 周末卖价
         * -1代表此房无价，无价和满房都不能进行预订
         */
        @NotNull
        @JsonProperty("Weekend")
        private BigDecimal weekend;

        /**
         * 平日结算价
         * -1代表此房无价，无价和满房都不能进行预订
         * 开通了结算价模式的接入方才可以使用
         */
        @NotNull
        @JsonProperty("MemberCost")
        private BigDecimal memberCost;

        /**
         * 周末结算价
         * -1代表此房无价，无价和满房都不能进行预订
         * 开通了结算价模式的接入方才可以使用
         */
        @NotNull
        @JsonProperty("WeekendCost")
        private BigDecimal weekendCost;

        /**
         * 加床价
         * -1代表不能加床，0-免费加床，大于0表示加床的费用
         */
        @Nullable
        @JsonProperty("AddBed")
        private BigDecimal addBed;

        /**
         * 价格ID
         */
        @Nullable
        @JsonProperty("PriceID")
        private String priceId;

        /**
         * 货币类型
         */
        @Nullable
        @JsonProperty("CurrencyCode")
        private String currencyCode;

        /**
         * 发票模式
         * NoSense --全部
         * Elong --艺龙开票
         * Hotel --酒店开票
         */
        @Nullable
        @JsonProperty("InvoiceMode")
        private String invoiceMode;

        /**
         * 是否限价
         * 多天连住时，有一天为true，则所有天限价。
         * 判断限价时与hotel.data.rp接口中IsPriceLimitProduct字段为或关系，两者有其一为true，均为限价。
         * false：非限价
         * true：限价
         */
        @NotNull
        @JsonProperty("IsPriceLimit")
        private Boolean isPriceLimit;

        /**
         * 限价类型
         * 二进制bit位分别表示各个限价条件
         * 从右往左从一开始的三位分别是：不可抬价、不可立减、不可返现
         * 0为非限价
         * PriceLimitedType&1==1时限价条件“需要展示卖价 不可抬价”成立
         * PriceLimitedType&2==2时限价条件“不可立减”成立
         * PriceLimitedType&4==4时限价条件“不可返现”成立
         * 如果是限价产品，但是不符合当前已定义限价条件时，建议过滤处理。
         */
        @NotNull
        @JsonProperty("PriceLimitedType")
        private Integer priceLimitedType;
    }
}
