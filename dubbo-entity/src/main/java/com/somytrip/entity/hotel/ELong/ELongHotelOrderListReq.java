package com.somytrip.entity.hotel.ELong;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @ClassName: ELongHotelOrderListReq
 * @Description: 同程艺龙酒店订单列表请求参数
 * @Author: shadow
 * @Date: 2024/2/23 17:56
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ELongHotelOrderListReq {

    /**
     * 预定时间开始点
     */
    @Nullable
    @JsonProperty("CreationTimeFrom")
    private LocalDateTime creationTimeFrom;

    /**
     * 预定时间结束点
     * 预定时间范围尽量控制在一个月范围内，否则会超时
     */
    @Nullable
    @JsonProperty("CreationTimeTo")
    private LocalDateTime creationTimeTo;

    /**
     * 酒店编号
     */
    @Nullable
    @JsonProperty("HotelId")
    private String hotelId;

    /**
     * 房型编号
     */
    @Nullable
    @JsonProperty("RoomTypeId")
    private String roomTypeId;

    /**
     * 产品编号
     */
    @Nullable
    @JsonProperty("RatePlanId")
    private Integer ratePlanId;

    /**
     * 入住日期开始点
     */
    @Nullable
    @JsonProperty("ArrivalDateFrom")
    private LocalDate arrivalDateFrom;

    /**
     * 入住日期结束点
     */
    @Nullable
    @JsonProperty("ArrivalDateTo")
    private LocalDate arrivalDateTo;

    /**
     * 离店日期开始点
     */
    @Nullable
    @JsonProperty("DepartureDateFrom")
    private LocalDate departureDateFrom;

    /**
     * 离店日期结束点
     */
    @Nullable
    @JsonProperty("DepartureDateTo")
    private LocalDate departureDateTo;

    /**
     * 最后更新时间开始点
     */
    @Nullable
    @JsonProperty("MinUpdateTime")
    private LocalDateTime minUpdateTime;

    /**
     * 最后更新时间结束点
     */
    @Nullable
    @JsonProperty("MaxUpdateTime")
    private LocalDateTime maxUpdateTime;

    /**
     * 联系人手机
     */
    @Nullable
    @JsonProperty("Mobile")
    private String mobile;

    /**
     * 入住人姓名
     */
    @Nullable
    @JsonProperty("CustomerName")
    private String customerName;

    /**
     * 订单状态
     */
    @Nullable
    @JsonProperty("Status")
    private String status;

    /**
     * 分页页码
     * 分页大小为10
     */
    @NotNull
    @JsonProperty("PageIndex")
    private Integer pageIndex;
}
