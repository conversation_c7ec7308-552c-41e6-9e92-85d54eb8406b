package com.somytrip.entity.hotel;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.somytrip.entity.enums.hotel.HotelOrigin;
import com.somytrip.entity.hotel.ELong.offline.ELongStaticHotelGradeResp;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 酒店点评表(HotelGrades)实体类
 *
 * <AUTHOR>
 * @since 2024-02-05 11:23:40
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "hotel_grades", autoResultMap = true)
public class HotelGradeEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = -68270035734212837L;

    /**
     * 自增主键ID
     */
    private Long id;
    /**
     * 酒店来源
     */
    private HotelOrigin hotelOrigin;
    /**
     * 酒店ID
     */
    private String hotelId;
    /**
     * 酒店主键ID
     */
    private Long hotelPid;
    /**
     * 点评总数
     */
    private Integer reviewCount;
    /**
     * 好评数
     */
    private Integer reviewGoodsCount;
    /**
     * 差评数
     */
    private Integer reviewPoorCount;
    /**
     * 评分(已经计算为百分比。周边酒店的评价评分可以自行通过酒店经纬度计算周边酒店，再计算平均评分)
     */
    private String reviewScore;
    /**
     * 订单30分钟内确认率
     */
    @TableField("confirm_rate_of_30_minute")
    private Integer confirmRateOf30Minute;
    /**
     * 分销过去90天产量得分
     */
    @TableField("grade_of_order_count_b90")
    private Integer gradeOfOrderCountB90;
    /**
     * C端过去90天产量得分
     */
    @TableField("grade_of_order_count_c90")
    private Integer gradeOfOrderCountC90;
    /**
     * (产品过去30天可卖得分
     */
    @TableField("grade_of_prod_30")
    private Integer gradeOfProd30;
    /**
     * 分销过去90天可定成功率
     */
    @TableField("val_rate_of_90")
    private Integer valRateOf90;
    /**
     * 点评标签
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<CommentTagEntity> commentTags;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public HotelGradeEntity(ELongStaticHotelGradeResp elongGradeResp, String hotelId) {

        this.hotelOrigin = HotelOrigin.ELong;
        this.hotelId = hotelId;

        ELongStaticHotelGradeResp.Grade grade = elongGradeResp.getGrade();
        ELongStaticHotelGradeResp.Review review = elongGradeResp.getReview();
        List<ELongStaticHotelGradeResp.CommentTag> commentTags = elongGradeResp.getCommentTags();

        if (review != null) {
            this.reviewCount = review.getReviewCount();
            this.reviewGoodsCount = review.getReviewGoodsCount();
            this.reviewPoorCount = review.getReviewPoorCount();
            this.reviewScore = review.getReviewScore();
        }
        if (grade != null) {
            this.confirmRateOf30Minute = grade.getConfirmRateOf30Minute();
            this.gradeOfOrderCountB90 = grade.getGradeOfOrderCountB90();
            this.gradeOfOrderCountC90 = grade.getGradeOfOrderCountC90();
            this.gradeOfProd30 = grade.getGradeOfProd30();
            this.valRateOf90 = grade.getValRateOf90();
        }
        if (commentTags != null) {
            List<CommentTagEntity> commentTagEntityList = new ArrayList<>();
            for (ELongStaticHotelGradeResp.CommentTag commentTag : commentTags) {
                commentTagEntityList.add(new CommentTagEntity(commentTag));
            }
            this.commentTags = commentTagEntityList;
        }
    }

    /**
     * 点评标签节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CommentTagEntity {

        /**
         * 标签名称
         */
        @JsonProperty("TagName")
        private String tagName;

        /**
         * 标签热度
         */
        @JsonProperty("HeatValue")
        private Integer heatValue;

        public CommentTagEntity(ELongStaticHotelGradeResp.CommentTag elongCommentTag) {
            this.tagName = elongCommentTag.getTagName();
            this.heatValue = elongCommentTag.getHeatValue();
        }
    }
}

