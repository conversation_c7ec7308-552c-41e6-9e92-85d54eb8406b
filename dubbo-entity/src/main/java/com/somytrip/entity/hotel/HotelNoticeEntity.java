package com.somytrip.entity.hotel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.somytrip.entity.enums.hotel.HotelOrigin;
import com.somytrip.entity.hotel.ELong.offline.ELongStaticHotelInfoResp;
import com.somytrip.entity.hotel.WEBBEDS.WEBBEDSHotelsResp;
import com.somytrip.utils.LocalDateTimeUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * (HotelNotice)实体类
 *
 * <AUTHOR>
 * @since 2024-02-02 09:33:19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "hotel_notice")
public class HotelNoticeEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = -38907013510072050L;

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 酒店来源
     */
    private HotelOrigin hotelOrigin;
    /**
     * 酒店ID
     */
    private String hotelId;
    /**
     * 酒店主键ID
     */
    private Long hotelPid;
    /**
     * 分类
     */
    private String category;
    /**
     * 开始日期
     */
    private LocalDate start;
    /**
     * 结束日期
     */
    private LocalDate end;
    /**
     * 中文文本
     */
    private String text;
    /**
     * 英文文本
     */
    private String textEn;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public HotelNoticeEntity(ELongStaticHotelInfoResp.Detail.Notice elongNotice, String hotelId) {
        this.hotelOrigin = HotelOrigin.ELong;
        this.hotelId = hotelId;
        this.category = elongNotice.getCategory();
        this.start = LocalDateTimeUtil.convertISOStr2LocalDate(elongNotice.getStart());
        this.end = LocalDateTimeUtil.convertISOStr2LocalDate(elongNotice.getEnd());
        this.text = elongNotice.getText();
        this.textEn = elongNotice.getTextEn();
    }

    public HotelNoticeEntity(WEBBEDSHotelsResp.Hotel.Notice.NoticeItem webbedsNoticeItem, String hotelId) {
        this.hotelOrigin = HotelOrigin.WEBBEDS;
        this.hotelId = hotelId;
        this.category = "Hotel";
        if (StringUtils.isNotBlank(webbedsNoticeItem.getStartDate())) {
            this.start = LocalDate.parse(webbedsNoticeItem.getStartDate());
        }
        if (StringUtils.isNotBlank(webbedsNoticeItem.getEndDate())) {
            this.end = LocalDate.parse(webbedsNoticeItem.getEndDate());
        }
        this.text = webbedsNoticeItem.getText();
    }

    public static void main(String[] args) {
        System.out.println(LocalDate.parse(""));
    }

    @Getter
    public enum HotelNoticeCategory {
        City("城市通知"),
        Hotel("酒店通知"),
        ;

        private final String title;

        HotelNoticeCategory(String title) {
            this.title = title;
        }

        public static String getTitleByName(String name) {
            for (HotelNoticeCategory e : HotelNoticeCategory.values()) {
                if (e.name().equals(name)) {
                    return e.getTitle();
                }
            }
            return null;
        }
    }
}

