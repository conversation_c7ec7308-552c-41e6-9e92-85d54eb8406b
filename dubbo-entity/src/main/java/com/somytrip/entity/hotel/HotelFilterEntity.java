package com.somytrip.entity.hotel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * @projectName: hotel-service
 * @package: com.somytrip.entity.hotel
 * @className: HotelFilterEntity
 * @author: shadow
 * @description: 酒店筛选项Entity
 * @date: 2024/4/7 15:30
 * @version: 1.0
 */
@Data
@TableName(value = "hotel_filters", autoResultMap = true)
public class HotelFilterEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 筛选项Code
     */
    private String filterCode;

    /**
     * 筛选项类型
     * (3: 品牌, 5: 商圈...)
     */
    private String filterTypeCode;

    /**
     * 筛选项名称
     */
    private String filterName;

    /**
     * 筛选项适用的城市列表
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> cityCodes;
}
