package com.somytrip.entity.hotel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.somytrip.entity.enums.hotel.HotelOrigin;
import com.somytrip.entity.hotel.ELong.offline.ELongStaticHotelInfoResp;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * (HotelThemes)实体类
 *
 * <AUTHOR>
 * @since 2024-01-31 15:26:50
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "hotel_themes")
public class HotelThemeEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 261240495038195374L;

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 酒店来源
     */
    private HotelOrigin hotelOrigin;
    /**
     * 主题ID
     */
    private String themeId;
    /**
     * 主题中文名
     */
    private String themeName;
    /**
     * 主题英文名
     */
    private String themeNameEn;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public HotelThemeEntity(ELongStaticHotelInfoResp.Detail.Theme theme) {
        this.hotelOrigin = HotelOrigin.ELong;
        this.themeId = theme.getThemeId();
        this.themeName = theme.getThemeName();
        this.themeNameEn = theme.getThemeNameEn();
    }
}

