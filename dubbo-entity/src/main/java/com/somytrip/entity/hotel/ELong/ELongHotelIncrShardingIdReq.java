package com.somytrip.entity.hotel.ELong;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.somytrip.entity.enums.hotel.IncrType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;

import java.time.LocalDateTime;

/**
 * @ClassName: ELongHotelIncrShardingIdReq
 * @Description: 同程艺龙酒店分片增量编号请求参数
 * @Author: shadow
 * @Date: 2024/2/22 16:07
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ELongHotelIncrShardingIdReq {

    /**
     * 最后的更新时间
     * 第一次同步参考全量开始同步的时间。
     */
    @NotNull
    @JsonProperty("LastTime")
    private LocalDateTime lastTime;

    /**
     * 分片键
     * 1-16，用于查询指定分片数据
     */
    @NotNull
    @JsonProperty("ShardingKey")
    private Integer shardingKey;

    /**
     * 增量类型
     */
    @NotNull
    @JsonProperty("IncrType")
    private IncrType incrType;
}
