package com.somytrip.entity.hotel;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.somytrip.entity.enums.hotel.HotelOrigin;
import com.somytrip.entity.enums.hotel.NCNBStarEnum;
import com.somytrip.entity.hotel.ELong.offline.ELongStaticHotelInfoResp;
import com.somytrip.entity.hotel.NCNB.NCNBBaseEntity;
import com.somytrip.entity.hotel.NCNB.NCNBHotelSearchResp;
import com.somytrip.entity.hotel.SZJL.SZJLQueryHotelDetailResp;
import com.somytrip.entity.hotel.WEBBEDS.WEBBEDSHotelsResp;
import com.somytrip.entity.vo.hotel.HotelImageVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * (HotelGeneral)实体类
 *
 * <AUTHOR>
 * @since 2024-01-31 10:48:52
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "hotel_general", autoResultMap = true)
public class HotelGeneralEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 273167107505399943L;

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 酒店来源
     */
    private HotelOrigin hotelOrigin;
    /**
     * 酒店ID
     */
    private String hotelId;
    /**
     * 酒店名称
     */
    private String hotelName;
    /**
     * 酒店英文名称
     */
    private String hotelNameEn;
    /**
     * 酒店曾用名
     */
    private String hotelUsedName;
    /**
     * 酒店英文曾用名
     */
    private String hotelUsedNameEn;
    /**
     * 酒店当地中文名
     */
    private String hotelNameLocal;
    /**
     * 酒店当地英文名
     */
    private String hotelNameLocalEn;
    /**
     * 酒店状态(0:有效 1:无效 2:删除)
     */
    private Integer hotelStatus;
    /**
     * 酒店中文简称
     */
    private String shortName;
    /**
     * 酒店英文简称
     */
    private String shortNameEn;
    /**
     * 酒店中文地址
     */
    private String address;
    /**
     * 酒店英文地址
     */
    private String addressEn;
    /**
     * 邮编
     */
    private String postalCode;
    /**
     * 挂牌星级(0-无星级；1-一星级；2-二星级；3-三星级；4-四星级；5-五星级。当为0时对外显示可用Category的值，但请进行图标区分)
     */
    private Integer starRate;
    /**
     * 平台推荐星级(0,1,2：客栈；3：舒适；4：高档；5：豪华。如果StarRate和Category都为空或0，可展示暂无数据)
     */
    private Integer category;
    /**
     * 评分
     */
    private BigDecimal score;
    /**
     * 点评数
     */
    private Integer reviewCount;
    /**
     * 电话
     */
    private String phone;
    /**
     * 传真
     */
    private String fax;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 酒店时区
     */
    private String timezone;
    /**
     * 开业时间
     */
    private String establishmentDate;
    /**
     * 装修时间
     */
    private String renovationDate;
    /**
     * 客房总数量
     */
    private Integer roomTotalAmount;
    /**
     * 集团编号
     */
    private String groupId;
    /**
     * 集团名称
     */
    private String groupName;
    /**
     * 集团英文名称
     */
    private String groupNameEn;
    /**
     * 品牌编号
     */
    private String brandId;
    /**
     * 品牌code(筛选)
     */
    private String brandCode;
    /**
     * 品牌名称
     */
    private String brandName;
    /**
     * 品牌英文名称
     */
    private String brandNameEn;
    /**
     * 是否经济型(默认值为0，1代表是经济型酒店)
     */
    private Integer isEconomic;
    /**
     * 是否是公寓(默认值为0，1代表是酒店式公寓)
     */
    private Integer isApartment;
    /**
     * 入住时间(酒店入住的最早时间，例如14:00表示酒店在入住日当天14:00之后允许客人入住)
     */
    private String arrivalTime;
    /**
     * 离店时间(酒店离店的最晚时间，例如12:00表示客人必须在离店日当天12:00之前离店)
     */
    private String departureTime;
    /**
     * Google纬度
     */
    private String googleLat;
    /**
     * Google经度
     */
    private String googleLon;
    /**
     * Baidu纬度
     */
    private String baiduLat;
    /**
     * Baidu经度
     */
    private String baiduLon;
    /**
     * 国家ID
     */
    private String countryId;
    /**
     * 国家名称
     */
    private String countryName;
    /**
     * 国家英文名
     */
    private String countryNameEn;
    /**
     * 省份ID
     */
    private String provinceId;
    /**
     * 省份名称
     */
    private String provinceName;
    /**
     * 省份英文名
     */
    private String provinceNameEn;
    /**
     * CityCode
     */
    private String globalCityCode;
    /**
     * 城市ID
     */
    private String cityId;
    /**
     * 城市名称
     */
    private String cityName;
    /**
     * 城市英文名
     */
    private String cityNameEn;
    /**
     * 关联城市ID
     */
    private String cityId2;
    /**
     * 行政区ID
     */
    private String district;
    /**
     * 行政区名称
     */
    private String districtName;
    /**
     * 行政区英文名
     */
    private String districtNameEn;
    /**
     * 主商圈ID
     */
    private String businessZone;
    /**
     * 商圈code(筛选)
     */
    private String businessZoneCode;
    /**
     * 主商圈中文名
     */
    private String businessZoneName;
    /**
     * 主商圈英文名
     */
    private String businessZoneNameEn;
    /**
     * 主商圈距离(米)
     */
    private Integer businessZoneDistance;
    /**
     * 附属商圈ID
     */
    private String businessZone2;
    /**
     * 附属商圈中文名
     */
    private String businessZone2Name;
    /**
     * 附属商圈英文名
     */
    private String businessZone2NameEn;
    /**
     * 酒店支持的信用卡
     */
    private String creditCards;
    /**
     * 酒店支持的信用卡英文
     */
    private String creditCardsEn;
    /**
     * 是否允许返现(不允许返现的酒店请在各个渠道上面都不要提供返现或类似返现的活动，否则酒店会对此做出处罚)
     */
    private Boolean hasCoupon;
    /**
     * 酒店中文简介
     */
    private String introEditor;
    /**
     * 酒店英文简介
     */
    private String introEditorEn;
    /**
     * 中文描述
     */
    private String description;
    /**
     * 英文描述
     */
    private String descriptionEn;
    /**
     * 中文接机服务
     */
    private String airportPickUpService;
    /**
     * 英文接机服务
     */
    private String airportPickUpServiceEn;
    /**
     * 酒店基础设施ID列表
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> generalFacilities;
    /**
     * 酒店休闲设施ID列表
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> recreationFacilities;
    /**
     * 酒店服务设施ID列表
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> serviceFacilities;
    /**
     * 预定须知ID列表
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> bookingNoticeFacilities;
    /**
     * 酒店主题ID列表
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> theme;
    /**
     * 酒店类型ID列表
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> hotelTypes;
    /**
     * 酒店设施V2 ID列表
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Integer> facilitiesV2;
    /**
     * 服务与设施
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<ServiceAndFacilityEntity> serviceAndFacility;
    /**
     * 入离政策
     */
    private String checkPolicy;
    /**
     * 儿童政策
     */
    private String childrenPolicy;
    /**
     * 宠物政策
     */
    private String petPolicy;
    /**
     * 酒店封面
     */
    private String cover;
    /**
     * 酒店图片
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> images;
    /**
     * 酒店图片列表
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<HotelImageVo> hotelImages;
    /**
     * 国际化字典
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Map<String, String>> localeDict;
    /**
     * 是否可用
     */
    private boolean isActive;
    /**
     * 是否删除
     */
    private boolean isDel;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public HotelGeneralEntity(ELongStaticHotelInfoResp.Detail detail) {
        this.hotelOrigin = HotelOrigin.ELong;
        this.globalCityCode = detail.getGlobalCityCode();
        this.hotelId = detail.getHotelId();
        this.hotelName = detail.getHotelName();
        this.hotelNameEn = detail.getHotelNameEn();
        this.hotelUsedName = detail.getHotelUsedName();
        this.hotelUsedNameEn = detail.getHotelUsedNameEn();
        this.hotelNameLocal = detail.getHotelNameLocal();
        this.hotelNameLocalEn = detail.getHotelNameLocalEn();
        this.hotelStatus = detail.getHotelStatus();
        this.shortName = detail.getShortName();
        this.shortNameEn = detail.getShortNameEn();
        this.address = detail.getAddress();
        this.addressEn = detail.getAddressEn();
        this.postalCode = detail.getPostalCode();
        this.starRate = detail.getStarRate();
        this.category = detail.getCategory();
        this.phone = detail.getPhone();
        this.fax = detail.getFax();
        this.email = detail.getEmail();
        this.timezone = detail.getTimezone();
        this.establishmentDate = detail.getEstablishmentDate();
        this.renovationDate = detail.getRenovationDate();
        this.roomTotalAmount = detail.getRoomTotalAmount();
        this.groupId = detail.getGroupId();
        this.groupName = detail.getGroupName();
        this.groupNameEn = detail.getGroupNameEn();
        this.brandId = detail.getBrandId();
        this.brandName = detail.getBrandName();
        this.brandNameEn = detail.getBrandNameEn();
        this.isEconomic = detail.getIsEconomic();
        this.isApartment = detail.getIsApartment();
        this.arrivalTime = detail.getArrivalTime();
        this.departureTime = detail.getDepartureTime();
        this.googleLat = detail.getGoogleLat().stripTrailingZeros().toPlainString();
        this.googleLon = detail.getGoogleLon().stripTrailingZeros().toPlainString();
        this.baiduLat = detail.getBaiduLat().stripTrailingZeros().toPlainString();
        this.baiduLon = detail.getBaiduLon().stripTrailingZeros().toPlainString();
        this.countryId = detail.getCountryId();
        this.countryName = detail.getCountryName();
        this.countryNameEn = detail.getCountryNameEn();
        this.cityId = detail.getCityId();
        this.cityName = detail.getCityName();
        this.cityNameEn = detail.getCityNameEn();
        this.cityId2 = detail.getCityId2();
        this.district = detail.getDistrict();
        this.districtName = detail.getDistrictName();
        this.districtNameEn = detail.getDistrictNameEn();
        this.businessZone = detail.getBusinessZone();
        this.businessZoneName = detail.getBusinessZoneName();
        this.businessZoneNameEn = detail.getBusinessZoneNameEn();
        this.businessZone2 = detail.getBusinessZone2();
        this.businessZone2Name = detail.getBusinessZone2Name();
        this.businessZone2NameEn = detail.getBusinessZone2NameEn();
        this.creditCards = detail.getCreditCards();
        this.creditCardsEn = detail.getCreditCardsEn();
        this.hasCoupon = detail.getHasCoupon();
        this.introEditor = detail.getIntroEditor();
        this.introEditorEn = detail.getIntroEditorEn();
        this.description = detail.getDescription();
        this.descriptionEn = detail.getDescriptionEn();
        this.airportPickUpService = detail.getAirportPickUpService();
        this.airportPickUpServiceEn = detail.getAirportPickUpServiceEn();
//        this.generalFacilities = detail.getGeneralFacilities();
        this.generalFacilities = getELongFacilityIdList(detail.getGeneralFacilities());
//        this.recreationFacilities = detail.getRecreationFacilities();
        this.recreationFacilities = getELongFacilityIdList(detail.getRecreationFacilities());
//        this.serviceFacilities = detail.getServiceFacilities();
        this.serviceFacilities = getELongFacilityIdList(detail.getServiceFacilities());
//        this.bookingNoticeFacilities = detail.getBookingNoticeFacilities();
        this.bookingNoticeFacilities = getELongFacilityIdList(detail.getBookingNoticeFacilities());
//        this.theme = detail.getTheme();
        this.theme = getELongThemeIdList(detail.getThemes());
//        this.hotelTypes = detail.getHotelTypes();
        this.hotelTypes = getELongHotelTypeIdList(detail.getHotelTypes());
//        this.facilitiesV2 = detail.getFacilitiesV2();
        this.facilitiesV2 = getELongFacilityV2IdList(detail.getFacilityV2());
        this.petPolicy = detail.getPetPolicy();
        this.isActive = true;
        this.isDel = false;
    }

    /**
     * WEBBEDS酒店对象 -> HotelGeneralEntity
     *
     * @param webbedsHotel WEBBEDS酒店对象
     */
    public HotelGeneralEntity(WEBBEDSHotelsResp.Hotel webbedsHotel) {
        this.hotelOrigin = HotelOrigin.WEBBEDS;
        this.hotelId = String.valueOf(webbedsHotel.getId());
        this.cityId = String.valueOf(webbedsHotel.getCityId());
        this.cityName = webbedsHotel.getCityName();
        this.district = String.valueOf(webbedsHotel.getDistrictId());
        this.districtName = webbedsHotel.getDistrictName();
        this.postalCode = webbedsHotel.getZipCode();
        this.hotelName = webbedsHotel.getName();
        this.brandName = webbedsHotel.getBrandName();
        this.address = webbedsHotel.getAddress();
        this.introEditor = webbedsHotel.getDescription();
        this.establishmentDate = webbedsHotel.getStartDate();
        this.hotelTypes = List.of(webbedsHotel.getType());
        this.starRate = Math.toIntExact(webbedsHotel.getStar());
        this.phone = webbedsHotel.getTelephone();
        this.score = BigDecimal.valueOf(webbedsHotel.getRating());
        this.reviewCount = Math.toIntExact(webbedsHotel.getTotalRateCount());
        this.baiduLat = webbedsHotel.getLatitude();
        this.baiduLon = webbedsHotel.getLongitude();
        this.images = webbedsHotel.getImages();
        this.cover = CollectionUtil.isNotEmpty(webbedsHotel.getImages()) ? webbedsHotel.getImages().get(0) : null;
        if (webbedsHotel.getServicesAndFacilities() != null) {
            this.serviceAndFacility = webbedsHotel.getServicesAndFacilities().stream()
                    .map(ServiceAndFacilityEntity::new)
                    .toList();
        }
    }

    /**
     * NCNB酒店对象 -> HotelGeneralEntity
     *
     * @param ncnbHotelInfo NCNB酒店对象
     */
    public HotelGeneralEntity(NCNBHotelSearchResp.HotelInfo ncnbHotelInfo) {
        this.hotelOrigin = HotelOrigin.NCNB;
        this.hotelId = ncnbHotelInfo.getHotelId();
        this.hotelName = ncnbHotelInfo.getHotelName();
        this.cityId = ncnbHotelInfo.getCityId();
        this.cityName = ncnbHotelInfo.getCityName();
        this.provinceId = ncnbHotelInfo.getProvinceId();
        this.provinceName = ncnbHotelInfo.getProvinceName();
        this.countryId = ncnbHotelInfo.getCountryId();
        this.countryName = ncnbHotelInfo.getCountryName();
        this.email = ncnbHotelInfo.getEmail();
        this.phone = ncnbHotelInfo.getHotelTel();
        this.introEditor = ncnbHotelInfo.getIntro();
        this.address = ncnbHotelInfo.getAddress();
        this.baiduLat = ncnbHotelInfo.getLat();
        this.baiduLon = ncnbHotelInfo.getLon();
        this.postalCode = ncnbHotelInfo.getPostCode();
        this.establishmentDate = ncnbHotelInfo.getStartBusinessDate();
        this.renovationDate = ncnbHotelInfo.getRepairdate();
        this.cover = CollectionUtil.isNotEmpty(ncnbHotelInfo.getHotelImages()) ? ncnbHotelInfo.getHotelImages().get(0).getImageUrl() : null;
        if (StringUtils.isNotBlank(ncnbHotelInfo.getScore())) {
            this.score = new BigDecimal(ncnbHotelInfo.getScore());
        }
        if (StringUtils.isNotBlank(ncnbHotelInfo.getStar())) {
            this.starRate = NCNBStarEnum.getStarByCode(ncnbHotelInfo.getStar());
        }
        if (ncnbHotelInfo.getFacilityInfos() != null) {
            NCNBBaseEntity.FacilityInfos facilityInfos = ncnbHotelInfo.getFacilityInfos();
            this.brandId = facilityInfos.getAffiliatedGroupId();
        }
//        if (ncnbHotelInfo.getFacilityInfos() != null) {
//            ncnbHotelInfo.getFacilityInfos().getServices()
//        }
    }

    public HotelGeneralEntity(SZJLQueryHotelDetailResp.HotelInfo hotelInfo) {
        this.hotelOrigin = HotelOrigin.SZJL;
        this.hotelName = hotelInfo.getHotelNameCn();
        this.hotelNameEn = hotelInfo.getHotelNameEn();
        if (StringUtils.isNotBlank(hotelInfo.getThemeType())) {
            this.theme = new ArrayList<>(List.of(hotelInfo.getThemeType()));
        }
//        hotelInfo.getStar();
        this.address = hotelInfo.getAddressCn();
        this.addressEn = hotelInfo.getAddressEn();
        this.countryId = StrUtil.toStringOrNull(hotelInfo.getCountryId());
        this.provinceId = StrUtil.toStringOrNull(hotelInfo.getStateId());
        this.cityId = StrUtil.toStringOrNull(hotelInfo.getCityId());
        this.district = StrUtil.toStringOrNull(hotelInfo.getArea());
        this.businessZone = StrUtil.toStringOrNull(hotelInfo.getBusinessCircle());
        this.phone = hotelInfo.getPhone();
        if (List.of(70007, 70008, 70009).contains(hotelInfo.getCountryId())) {
            this.baiduLon = hotelInfo.getLongitude();
            this.baiduLat = hotelInfo.getLatitude();
        } else {
            this.googleLon = hotelInfo.getLongitude();
            this.googleLat = hotelInfo.getLatitude();
        }
        this.fax = hotelInfo.getFax();
        this.email = hotelInfo.getEmail();
        this.postalCode = hotelInfo.getPostCode();
        this.checkPolicy = hotelInfo.getCheckPolicy();
        this.childrenPolicy = hotelInfo.getChildrenPolicy();
        this.petPolicy = hotelInfo.getPetPolicy();
        this.establishmentDate = hotelInfo.getEstablishmentDate();
        this.renovationDate = hotelInfo.getRenovationDate();
        this.groupId = hotelInfo.getHotelGroup();
        this.brandId = hotelInfo.getHotelBrand();
        if (StringUtils.isNotBlank(hotelInfo.getFacilities())) {
            List<Integer> facilitiesV2 = new ArrayList<>();
            for (String s : hotelInfo.getFacilities().split("\\|")) {
                if (StringUtils.isBlank(s)) continue;
                facilitiesV2.add(new BigDecimal(s).intValue());
            }
            this.facilitiesV2 = facilitiesV2;
        }
        this.creditCards = hotelInfo.getCardType();
//        this.introEditor = hotelInfo.getIntroduceCn();
//        this.introEditorEn = hotelInfo.getIntroduceEn();
    }

    public HotelGeneralEntity(HotelThirdPartyEntity thirdPartyEntity) {
        this.hotelOrigin = HotelOrigin.NCNB;
        this.hotelId = thirdPartyEntity.getId();
        this.hotelName = thirdPartyEntity.getHotelName();
        this.countryName = thirdPartyEntity.getCountryName();
        this.provinceName = thirdPartyEntity.getProvinceName();
        this.cityId = thirdPartyEntity.getCityId();
        this.cityName = thirdPartyEntity.getCityName();
        this.address = thirdPartyEntity.getAddress();
        this.email = thirdPartyEntity.getEmail();
        this.establishmentDate = thirdPartyEntity.getStartBusinessDate();
        this.renovationDate = thirdPartyEntity.getRepairDate();
        this.postalCode = thirdPartyEntity.getPostCode();
        this.baiduLon = thirdPartyEntity.getLon();
        this.baiduLat = thirdPartyEntity.getLat();
        if (StringUtils.isNotBlank(thirdPartyEntity.getScore())) {
            this.score = new BigDecimal(thirdPartyEntity.getScore());
        }
        this.introEditor = thirdPartyEntity.getIntroduction();
        this.phone = thirdPartyEntity.getHotelTel();
        if (StringUtils.isNotBlank(thirdPartyEntity.getStar())) {
            this.starRate = Integer.valueOf(thirdPartyEntity.getStar());
        }
    }

    public boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(boolean active) {
        isActive = active;
    }

    public boolean getIsDel() {
        return isDel;
    }

    public void setIsDel(boolean del) {
        isDel = del;
    }

    public void setHotelImages(List<HotelImageVo> hotelImages) {
        if (hotelImages != null) {
            this.hotelImages = JSONArray.from(hotelImages).toJavaList(HotelImageVo.class);
        }
    }

    private List<String> getELongFacilityIdList(List<ELongStaticHotelInfoResp.Facility> facilities) {
        if (facilities == null) {
            return Collections.emptyList();
        }
        return facilities.stream().map(ELongStaticHotelInfoResp.Facility::getFacilityId).collect(Collectors.toList());
    }

    private List<Integer> getELongFacilityV2IdList(List<ELongStaticHotelInfoResp.FacilityType> facilityTypeList) {
        if (facilityTypeList == null) {
            return Collections.emptyList();
        }
        return facilityTypeList.stream().flatMap(type -> type.getFacilityInfoList().stream())
                .map(item -> item.getFacilityId().intValue())
                .collect(Collectors.toList());
    }

    private List<String> getELongHotelTypeIdList(List<ELongStaticHotelInfoResp.Detail.HotelType> hotelTypesList) {
        if (hotelTypesList == null) {
            return Collections.emptyList();
        }
        return hotelTypesList.stream()
                .map(ELongStaticHotelInfoResp.Detail.HotelType::getHotelTypeId)
                .collect(Collectors.toList());
    }

    private List<String> getELongThemeIdList(List<ELongStaticHotelInfoResp.Detail.Theme> themeList) {
        if (themeList == null) {
            return Collections.emptyList();
        }
        return themeList.stream().map(ELongStaticHotelInfoResp.Detail.Theme::getThemeId).collect(Collectors.toList());
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ServiceAndFacilityEntity {

        /**
         * 服务与设施组ID
         */
        private Integer id;

        /**
         * 服务与设施图标文件组ID
         */
        private Integer icon;

        /**
         * 服务与设施图标文件组名称
         */
        private String name;

        /**
         * 组内服务与设施列表
         */
//        @TableField(typeHandler = JacksonTypeHandler.class)
        private List<ServiceAndFacilityItemEntity> items;

        public ServiceAndFacilityEntity(WEBBEDSHotelsResp.Hotel.ServiceAndFacility webbedsServiceAndFacility) {
            this.id = webbedsServiceAndFacility.getId();
            this.icon = webbedsServiceAndFacility.getIcon();
            this.name = webbedsServiceAndFacility.getName();
            if (webbedsServiceAndFacility.getItems() != null) {
                this.items = webbedsServiceAndFacility.getItems().stream()
                        .map(ServiceAndFacilityItemEntity::new)
                        .toList();
            }
        }

        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class ServiceAndFacilityItemEntity {

            /**
             * 服务/设施ID
             */
            private Integer id;

            /**
             * 服务/设施图标ID
             */
            private Integer icon;

            /**
             * 服务/设施名称
             */
            private String name;

            /**
             * 所属房型ID
             */
            private Integer roomTypeId;

            /**
             * 所属Room ID
             */
            private Integer roomId;

            public ServiceAndFacilityItemEntity(
                    WEBBEDSHotelsResp.Hotel.ServiceAndFacility.ServiceAndFacilityItem webbedsServiceAndFacilityItem
            ) {
                this.id = webbedsServiceAndFacilityItem.getId();
                this.icon = webbedsServiceAndFacilityItem.getIcon();
                this.name = webbedsServiceAndFacilityItem.getName();
                this.roomTypeId = webbedsServiceAndFacilityItem.getRoomTypeId();
                this.roomId = webbedsServiceAndFacilityItem.getRoomId();
            }
        }
    }
}

