package com.somytrip.entity.hotel.ELong;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.List;

/**
 * @ClassName: ELongHotelDestinationResp
 * @Description: 同程艺龙目的地补全响应
 * @Author: shadow
 * @Date: 2024/3/1 10:27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ELongHotelDestinationResp {

    /**
     * 目的地结果列表
     */
    @NotNull
    @JsonProperty("RegionResult")
    private List<RegionResult> regionResult;

    /**
     * 目的地结果 节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RegionResult {

        /**
         * 目的地英文名
         */
        @NotNull
        @JsonProperty("SugEn")
        private String sugEn;

        /**
         * 目的地ID
         * 当目的地类型(RegionType字段)是城市时，此处为即hotel.list接口所使用的CityId
         */
        @NotNull
        @JsonProperty("RegionId")
        private String regionId;

        /**
         * 目的地中文名
         */
        @Nullable
        @JsonProperty("RegionNameCn")
        private String regionNameCn;

        /**
         * 目的地英文名
         * 一般为空，请优先使用 SugEn
         */
        @Nullable
        @JsonProperty("RegionNameEn")
        private String regionNameEn;

        /**
         * 父级目的地ID
         * 当目的地类型(RegionType字段)是城市时可以忽略本字段，
         * 目的地类型为其他类型时，本字段表示该目的地所属的城市ID，即hotel.list接口使用的CityId
         */
        @NotNull
        @JsonProperty("ParentId")
        private String parentId;

        /**
         * 父级目的地中文名
         * 当目的地类型是城市时本字段表示城市所属省份名称，部分城市（比如直辖市）本字段为空。
         * 当目的地类型为其他类型时，本字段为目的地所属城市的名称。
         */
        @NotNull
        @JsonProperty("ParentNameCn")
        private String parentNameCn;

        /**
         * 父级目的地英文名
         * 一般为空，逻辑和 ParentName 相同
         */
        @Nullable
        @JsonProperty("ParentNameEn")
        private String parentNameEn;

        /**
         * 目的地类型
         * 0-城市
         * 1-行政区
         * 2-景区
         * 3-商圈
         * 4-酒店
         * 5-地点
         * 6-医院
         * 7-学校
         * 8-景点
         * 9-地铁站
         * 10-机场/车站
         * 12-品牌
         * 13-集团
         */
        @NotNull
        @JsonProperty("RegionType")
        private Integer regionType;

        /**
         * 对应筛选项的类型
         * 用于 hotel.filter 接口的入参，获取本目的地对应的筛选项
         * 3-品牌
         * 4-行政区
         * 5-商圈
         * 6-POI(大学、周边游[风景区]、汽车站、火车站、地铁等)
         */
        @NotNull
        @JsonProperty("FilterType")
        private Integer filterType;

        /**
         * 对应筛选项的ID
         */
        @NotNull
        @JsonProperty("FilterId")
        private Integer filterId;

        /**
         * sug标记信息
         * 本目的地的唯一标识，一般不需要关注
         */
        @NotNull
        @JsonProperty("SugActInfo")
        private String sugActInfo;

        /**
         * POI地址信息
         * 目的地的地址信息描述
         */
        @NotNull
        @JsonProperty("Address")
        private String address;

        /**
         * 目的地名称
         * sug展示的目的地名称，一般是 RegionNameCn，ParentNameCn
         */
        @NotNull
        @JsonProperty("ComposedName")
        private String composedName;

        /**
         * 目的地展示类型
         * 含义和用法与 RegionType 相同，使用 RegionType 即可
         */
        @NotNull
        @JsonProperty("RegionShowType")
        private Integer regionShowType;

        /**
         * sug类型
         * 搜索的范围
         * 0-国内
         * 1-国际
         */
        @NotNull
        @JsonProperty("SugOrigin")
        private Integer sugOrigin;

        /**
         * 酒店基本属性
         * 仅在 RegionType 为 4 时有效，参考 BaseAttr 节点
         */
        @Nullable
        @JsonProperty("BaseAttr")
        private BaseAttr baseAttr;

        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class BaseAttr {

            /**
             * 酒店评分
             * 满分5分，与艺龙官网的评分相同
             */
            @Nullable
            @JsonProperty("Score")
            private String score;

            /**
             * 酒店所属商圈
             */
            @Nullable
            @JsonProperty("MallName")
            private String mallName;
        }
    }
}
