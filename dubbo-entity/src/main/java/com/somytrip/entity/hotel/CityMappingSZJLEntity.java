package com.somytrip.entity.hotel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.somytrip.entity.hotel.SZJL.SZJLQueryCityResp;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.hotel
 * @className: CityMappingSZJLEntity
 * @author: shadow
 * @description: 深圳捷旅城市映射entity
 * @date: 2024/10/13 14:19
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("city_mapping_szjl")
public class CityMappingSZJLEntity implements Serializable {

    @Serial
    private final static long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 统一城市code
     */
    private String global_city_code;

    /**
     * 城市ID
     */
    private Integer cityId;

    /**
     * 城市中文名
     */
    private String cityCn;

    /**
     * 城市英文名
     */
    private String cityEn;

    /**
     * 省份ID
     */
    private Integer stateId;

    /**
     * 省份中文名
     */
    private String stateCn;

    /**
     * 省份英文名
     */
    private String stateEn;

    /**
     * 国家ID
     */
    private Integer countryId;

    /**
     * 国家中文名
     */
    private String countryCn;

    /**
     * 国家英文名
     */
    private String countryEn;

    /**
     * 是否可用
     */
    private Boolean isActive;

    public CityMappingSZJLEntity(SZJLQueryCityResp.HotelGeo hotelGeo) {
        this.cityId = hotelGeo.getCityId();
        this.cityCn = hotelGeo.getCityCn();
        this.cityEn = hotelGeo.getCityEn();
        this.stateId = hotelGeo.getStateId();
        this.stateCn = hotelGeo.getStateCn();
        this.stateEn = hotelGeo.getStateEn();
        this.countryId = hotelGeo.getCountryId();
        this.countryCn = hotelGeo.getCountryCn();
        this.countryEn = hotelGeo.getCountryEn();
    }
}
