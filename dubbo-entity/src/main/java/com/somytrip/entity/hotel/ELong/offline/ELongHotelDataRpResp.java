package com.somytrip.entity.hotel.ELong.offline;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.somytrip.entity.enums.hotel.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName: ELongHotelDataRpResp
 * @Description: 同程艺龙酒店产品信息响应
 * @Author: shadow
 * @Date: 2024/2/18 11:46
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ELongHotelDataRpResp {

    /**
     * 酒店列表
     */
    @Nullable
    @JsonProperty("Hotels")
    private List<Hotel> hotels;

    /**
     * Hotel节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Hotel {

        /**
         * 酒店编号
         */
        @NotNull
        @JsonProperty("HotelID")
        private String hotelId;

        /**
         * 供应商列表
         * 包含多个Supplier节点，一个Supplier表示一个供应商，一家酒店可能代理给多个供应商，
         * 艺龙也会从其他供应商分销部分产品，所以一个HotelId会对应多个HotelCode来表示不同的供应商酒店
         */
        @Nullable
        @JsonProperty("Suppliers")
        private List<Supplier> suppliers;

        /**
         * 产品列表
         */
        @Nullable
        @JsonProperty("RatePlans")
        private List<RatePlan> ratePlans;

        /**
         * 礼包
         * 酒店送礼信息, 礼品信息和预订或入住日期相关。
         * 包含多个Gift节点（建议使用GiftPackages节点）
         */
        @Nullable
        @JsonProperty("Gifts")
        private List<Gift> gifts;

        /**
         * 礼包套餐
         * 参考多个GiftPackage节点，参考下方文末礼包套餐（GiftPackage）使用说明
         */
        @Nullable
        @JsonProperty("GiftPackages")
        private List<GiftPackage> giftPackages;

        /**
         * Supplier节点
         */
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class Supplier {

            /**
             * 供应商ID
             */
            @NotNull
            @JsonProperty("SupplierId")
            private String supplierId;

            /**
             * 酒店编码
             */
            @NotNull
            @JsonProperty("HotelCode")
            private String hotelCode;

            /**
             * 周末开始
             * 为0表示周末设置从周一开始
             */
            @NotNull
            @JsonProperty("WeekendStart")
            private Integer weekendStart;

            /**
             * 周末结束
             * 为0表示到周日结束，但是两个都为0表示无周末设置； 如果开始为3，结束为1，表示从周三到下周1都是周末设置
             * 1代表周一，7代表周日
             */
            @NotNull
            @JsonProperty("WeekendEnd")
            private Integer weekendEnd;

            /**
             * 预订规则
             */
            @Nullable
            @JsonProperty("BookingRules")
            private List<BookingRule> bookingRules;

            /**
             * 房间对应关系
             * 用于产品获取对应房型静态信息
             * 包含多个Room节点
             */
            @Nullable
            @JsonProperty("Rooms")
            private List<Room> rooms;

            /**
             * 预付产品发票模式
             */
            @NotNull
            @JsonProperty("InvoiceMode")
            private String invoiceMode;

            /**
             * 酒店等级
             * 1:特牌 2:金牌 3:银牌 4:蓝牌 0:非挂牌
             */
            @Nullable
            @JsonProperty("hotellevel")
            private String hotelLevel;

            /**
             * 直连供应商ID
             * 仅表示直连供应商Id
             */
            @Nullable
            @JsonProperty("DCSupplierId")
            private String DCSupplierId;

            /**
             * 供应商类型
             */
            @Nullable
            @JsonProperty("SupplierType")
            private String supplierType;

            /**
             * BookingRule节点
             */
            @Data
            @NoArgsConstructor
            @AllArgsConstructor
            public static class BookingRule {

                /**
                 * 规则类型
                 * NeedNationality：务必提供客人国籍
                 * PerRoomPerName：您预订了N间房，请您提供不少于N的入住客人姓名
                 * ForeignerNeedEnName：此酒店要求外宾务必留英文拼写 ，港澳台酒店出现这个字段的时候，所有人都需要填写英文名或姓名拼音
                 * RejectCheckinTime：几点到几点酒店不接受预订 , 此处校验的是下单时的当前时间
                 * NeedPhoneNo：务必提供联系人手机号(请加在联系人结点Contact上)
                 */
                @NotNull
                @JsonProperty("TypeCode")
                private String typeCode;

                /**
                 * 关联的销售房型Id
                 * all 表示所有房型
                 */
                @Nullable
                @JsonProperty("RoomTypeIds")
                private String roomTypeIds;

                /**
                 * 描述
                 */
                @NotNull
                @JsonProperty("Description")
                private String description;

                /**
                 * 日期类型
                 * BookDay –预订日期（订单的创建日期）
                 */
                @Nullable
                @JsonProperty("DateType")
                private String dateType;

                /**
                 * 开始日期
                 */
                @Nullable
                @JsonProperty("StartDate")
                private String startDate;

                /**
                 * 结束日期
                 */
                @Nullable
                @JsonProperty("EndDate")
                private String endDate;

                /**
                 * 每天开始时间
                 * 针对日期段内每天生效, 当TypeCode 为RejectCheckinTime时表示当前预订时间在StartHour到EndHour区间内酒店不接受预订。
                 * 当EndHour大于24点的时候是表示第二天的几点加上了24小时，如26:00表示第二天的2点。
                 */
                @Nullable
                @JsonProperty("StartHour")
                private String startHour;

                /**
                 * 每天结束时间
                 */
                @Nullable
                @JsonProperty("EndHour")
                private String endHour;
            }

            /**
             * Room节点
             */
            @Data
            @NoArgsConstructor
            @AllArgsConstructor
            public static class Room {

                /**
                 * 销售房型编号
                 * 关联RatePlan.RoomTypeIds
                 */
                @NotNull
                @JsonProperty("RoomTypeId")
                private String roomTypeId;

                /**
                 * 展示房型编号
                 */
                @NotNull
                @JsonProperty("RoomId")
                private String roomId;

                /**
                 * 销售房型可用状态
                 * true---该销售房型可销售，false-该销售房型不能销售
                 */
                @Nullable
                @JsonProperty("Status")
                private Boolean status;
            }
        }

        /**
         * RatePlan节点
         */
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class RatePlan {

            /**
             * 产品编号
             */
            @NotNull
            @JsonProperty("RatePlanId")
            private Integer ratePlanId;

            /**
             * 产品名称
             */
            @NotNull
            @JsonProperty("RatePlanName")
            private String ratePlanName;

            /**
             * 宾客类型
             */
            @NotNull
            @JsonProperty("CustomerType")
            private CustomerType customerType;

            /**
             * 适用人群
             * 宾客类型的适用人群：
             * 0:不限
             * 1:持中国身份证的居民
             * 2:持回乡证的港澳人士
             * 3:持台胞证的台湾人士
             * 4:持中国护照的侨胞
             * 5:持大陆工作证/居留许可的外籍人士
             * 6:持非中国护照的外籍人士
             * 7:其他
             */
            @Nullable
            @JsonProperty("guestType")
            private String guestType;

            /**
             * 其他
             * 适用人群中其他的内容
             */
            @Nullable
            @JsonProperty("guestTypeExtendCh")
            private String guestTypeExtendCh;

            /**
             * 对应的酒店编码
             */
            @NotNull
            @JsonProperty("HotelCode")
            private String hotelCode;

            /**
             * 付款类型
             */
            @NotNull
            @JsonProperty("PaymentType")
            private PaymentType paymentType;

            /**
             * 关联的房型编码
             * 多个房型编码时以英文逗号分隔
             */
            @NotNull
            @JsonProperty("RoomTypeIds")
            private String roomTypeIds;

            /**
             * 产品特性类型
             * 3-限时抢购
             * 4-钟点房
             * 5-手机专享
             * 6-铂涛产品，下单需提供入住人身份证（Identification字段已包含此逻辑，涉及到身份信息验证的可统一按照Identification字段来处理）
             * 17-景酒打包产品，只能与景点门票打包销售，默认不吐出
             * 25-床位房（床位房类型此处已无效，判断床位房请解析RatePlanName或者Room节点的Name字段，其中只要有一个字段包含“床位”字样即为床位房）
             * 99-未知，艺龙内部使用，一般代理可忽略
             */
            @Nullable
            @JsonProperty("ProductTypes")
            private String productTypes;

            /**
             * 是否需要提供身份证号
             * 如果是铂涛产品，不能通过此字段判断，不管true还是false，都要传入身份证。
             * 如果不是铂涛产品，指该RatePlan在下单的时候，是否需要传入入住人的身份证号信息，如果该字段不为空且为true，则在成单时必须传入身份证号
             * （将来废弃，新字段请参考身份信息验证类型【Identification】）
             */
            @Nullable
            @JsonProperty("NeedIdNo")
            private Boolean needIdNo;


            /**
             * 身份信息验证类型
             * 0-无特殊验证要求（默认值）
             * 1-整个订单至少传一个身份证
             * 2-订单中每个房间至少传一个证件
             * 3-订单中每个房间至少传一个身份证
             * 4-每个客人传一个身份证
             * 5-整个订单至少传一个身份证且需预订本人入住
             * （后续可能会继续增加其他验证类型，建议接入时将非文档中给出类型的产品做过滤处理）
             */
            @NotNull
            @JsonProperty("Identification")
            private Integer identification;

            /**
             * 分销渠道
             * 1-分销专享，艺龙内部使用，一般代理可忽略
             */
            @Nullable
            @JsonProperty("sellChannels")
            private String sellChannels;

            /**
             * 是否是今日特价(尾房)
             * 如果为true，则要校验当前时间是否在StartTime和EndTime的范围内，从而决定这个RP是否显示在可销售产品中。
             */
            @Nullable
            @JsonProperty("IsLimitTimeSale")
            private Boolean isLimitTimeSale;

            /**
             * 尾房每天预订开始时间
             * 默认值：00:00。仅在尾房情况下有效。
             */
            @Nullable
            @JsonProperty("StartTime")
            private String startTime;

            /**
             * 尾房每天预订结束时间
             * 默认值:23:59。仅在尾房情况下有效。
             * 如果结束时间是00:00至6:00，则表示是次日。当EndTime大于24点的时候是表示第二天的几点加上了24小时，如26:00表示第二天的2点。
             */
            @Nullable
            @JsonProperty("EndTime")
            private String endTime;

            /**
             * 预订最少数量
             * 默认值：1
             */
            @Nullable
            @JsonProperty("MinAmount")
            private Integer minAmount;

            /**
             * 最少入住天数
             * 默认值：1
             */
            @Nullable
            @JsonProperty("MinDays")
            private Integer minDays;

            /**
             * 最多入住天数
             * 默认值：365
             */
            @Nullable
            @JsonProperty("MaxDays")
            private Integer maxDays;

            /**
             * 最多预订间数
             * 规定了每个订单最多预订多少间
             * 存在下单订多间不通过，但是一间一间的下单可以下多单的情况，这种情况就是此字段造成的。
             * 为0时默认按照10间处理
             * 默认值：9999
             */
            @Nullable
            @JsonProperty("MaxCheckinRooms")
            private Integer maxCheckinRooms;

            /**
             * 最少提前预订小时数
             * 按checkInDate的23:59:59(一般认为24点)来计算
             */
            @NotNull
            @JsonProperty("MinAdvHours")
            private Integer minAdvHours;

            /**
             * 最多提前预订小时数
             * 按checkInDate的23:59:59(一般认为24点)来计算
             */
            @NotNull
            @JsonProperty("MaxAdvHours")
            private Integer maxAdvHours;

            /**
             * 担保规则
             */
            @Nullable
            @JsonProperty("GuaranteeRules")
            private List<GuaranteeRule> guaranteeRules;

            /**
             * 预付规则
             */
            @Nullable
            @JsonProperty("PrepayRules")
            private List<PrepayRule> prepayRules;

            /**
             * 新预付规则
             */
            @Nullable
            @JsonProperty("PrepayRuleExtends")
            private List<PrepayRuleExtend> prepayRuleExtends;

            /**
             * 新担保规则
             */
            @Nullable
            @JsonProperty("GuaranteeRuleExtends")
            private List<GuaranteeRuleExtend> guaranteeRuleExtends;

            /**
             * 增值服务
             */
            @Nullable
            @JsonProperty("ValueAdds")
            private List<ValueAdd> valueAdds;

            /**
             * 新餐食节点
             * 相较增值服务中的早餐，拓展了午餐、晚餐和动态餐食，去掉了无用字段。
             */
            @Nullable
            @JsonProperty("Meals")
            private List<Meal> meals;

            /**
             * 促销规则
             */
            @Nullable
            @JsonProperty("DrrRules")
            private List<DrrRule> drrRules;

            /**
             * Coupon信息
             * Coupon和入住日期相关，本处返回仅供参考。
             */
            @Nullable
            @JsonProperty("Coupon")
            private Coupon coupon;

            /**
             * 产品提供服务的时间
             * 酒店或供应商能处理订单的时间，无数据（即不返回这个字段）代表全天服务
             * 预订当天入住且预订时间不在服务时间范围内的产品不可展示销售给客人，避免不必要的投诉。
             */
            @Nullable
            @JsonProperty("serviceTimePolicyInfo")
            private ServiceTimePolicyInfo serviceTimePolicyInfo;

            /**
             * 产品可以展示销售的渠道
             * 需要 Options入参传入1后才能返回。
             * 逗号分隔的数字列表：
             * 1---线上(普通的PC访问的Web)
             * 2---线下(呼叫中心、门店)
             * 3---手机(Mobile App、H5)
             */
            @Nullable
            @JsonProperty("BookingChannels")
            private String bookingChannels;

            /**
             * 是否为限价产品
             * 表示本RatePlan是否为限价产品，限价产品必须按照艺龙给出的售价进行售卖，即按照hotel.data.rate接口指定的价格卖给客人。
             * 不同限价类型约束规则不同，详见下面PriceLimitedType字段，接入完成后通知商务变更，新校验规则生效。
             * false --- 非限价
             * true --- 限价
             * (版本1.36新增)
             * 判断限价时与hotel.data.rate接口中IsPriceLimit字段为或关系，两者有其一为true，均为限价。
             */
            @NotNull
            @JsonProperty("IsPriceLimitProduct")
            private Boolean isPriceLimitProduct;

            /**
             * 限价类型
             * 二进制bit位分别表示各个限价条件
             * 从右往左从一开始的三位分别是：不可抬价、不可立减、不可返现
             * 0为非限价
             * PriceLimitedType&1==1时限价条件“需要展示卖价 不可抬价”成立
             * PriceLimitedType&2==2时限价条件“不可立减”成立
             * PriceLimitedType&4==4时限价条件“不可返现”成立
             * 如果是限价产品，但是不符合当前已定义限价条件时，建议过滤处理。
             */
            @NotNull
            @JsonProperty("PriceLimitedType")
            private Integer priceLimitedType;

            /**
             * 可售会员等级
             * 0-非会员 1-普通会员 2-贵宾会员 3-龙萃会员 4-钻石龙萃
             */
            @Nullable
            @JsonProperty("CustomerLevel")
            private List<Integer> customerLevel;

            /**
             * 预付产品发票模式
             * 仅用于预付产品的发票开具模式。
             * Elong-艺龙开发票、Hotel-酒店开发票
             * 前台自付产品都是酒店开发票，这里的过滤是针对预付产品。
             * 需要注意Elong艺龙开发票其实是艺龙可以提供代开发票服务，如果需要开通，请联系商务，否则可能需要自行开具发票
             */
            @NotNull
            @JsonProperty("InvoiceMode")
            private String invoiceMode;

            /**
             * 酒店签约类型
             * 1为直签，2为非直签，0为未知
             */
            @Nullable
            @JsonProperty("CooperationType")
            private Integer cooperationType;

            /**
             * 可住开始时间
             * 1、钟点房产品特有字段。
             * 2、可住时长stayTime为客人实际可住时长最大值，客人实际的可住时长需要代理根据可住结束时间减去当前时间进行计算
             * 3、建议实际可住时长不足一小时时不要再展示给客人
             * 4、当结束时间小于开始时间时表示第二天结束
             * 5、注意钟点房是有可售时间范围的，可售开始时间为earliestToliveTime，可售结束时间为latestToliveTime减去stayTime，在可售时间范围外试单不能通过，即最晚入住时间为可售结束时间。
             * V1.61版本开始支持钟点房跨天
             * latestToliveTime大于24时即为钟点房跨天产品(前端具体展示格式可参考同程旅行官网)
             */
            @Nullable
            @JsonProperty("earliestToliveTime")
            private String earliestToLiveTime;

            /**
             * 可住结束时间
             * 1、钟点房产品特有字段。
             * 2、可住时长stayTime为客人实际可住时长最大值，客人实际的可住时长需要代理根据可住结束时间减去当前时间进行计算
             * 3、建议实际可住时长不足一小时时不要再展示给客人
             * 4、当结束时间小于开始时间时表示第二天结束
             * 5、注意钟点房是有可售时间范围的，可售开始时间为earliestToliveTime，可售结束时间为latestToliveTime减去stayTime，在可售时间范围外试单不能通过，即最晚入住时间为可售结束时间。
             * V1.61版本开始支持钟点房跨天
             * latestToliveTime大于24时即为钟点房跨天产品(前端具体展示格式可参考同程旅行官网)
             */
            @Nullable
            @JsonProperty("latestToliveTime")
            private String latestToLiveTime;

            /**
             * 可住时长
             * 1、钟点房产品特有字段。
             * 2、可住时长stayTime为客人实际可住时长最大值，客人实际的可住时长需要代理根据可住结束时间减去当前时间进行计算
             * 3、建议实际可住时长不足一小时时不要再展示给客人
             * 4、当结束时间小于开始时间时表示第二天结束
             * 5、注意钟点房是有可售时间范围的，可售开始时间为earliestToliveTime，可售结束时间为latestToliveTime减去stayTime，在可售时间范围外试单不能通过，即最晚入住时间为可售结束时间。
             * V1.61版本开始支持钟点房跨天
             * latestToliveTime大于24时即为钟点房跨天产品(前端具体展示格式可参考同程旅行官网)
             */
            @Nullable
            @JsonProperty("stayTime")
            private String stayTime;

            /**
             * 限时抢规则
             */
            @Nullable
            @JsonProperty("timeRushRuleList")
            private List<TimeRushRule> timeRushRuleList;

            /**
             * 可入住人数
             * 房型补充说明
             */
            @Nullable
            @JsonProperty("xStayPeopleNum")
            private String xStayPeopleNum;

            /**
             * 可入住性别
             * 房型补充说明
             */
            @Nullable
            @JsonProperty("xStaySex")
            private String xStaySex;

            /**
             * 床型
             * 房型补充说明
             */
            @Nullable
            @JsonProperty("xBedType")
            private String xBedType;

            /**
             * 楼层
             * 房型补充说明
             */
            @Nullable
            @JsonProperty("xFloor")
            private String xFloor;

            /**
             * 朝向
             * 房型补充说明
             */
            @Nullable
            @JsonProperty("xOrientation")
            private String xOrientation;

            /**
             * 自定义说明
             * 房型补充说明
             */
            @Nullable
            @JsonProperty("xUserDefined")
            private String xUserDefined;

            /**
             * 是否支持专票
             * true 支持专票，false 不支持
             */
            @Nullable
            @JsonProperty("SupportSpecialInvoice")
            private Boolean SupportSpecialInvoice;

            /**
             * GuaranteeRule节点
             */
            @Data
            @NoArgsConstructor
            @AllArgsConstructor
            public static class GuaranteeRule {

                /**
                 * 描述
                 */
                @NotNull
                @JsonProperty("Description")
                private String description;

                /**
                 * 日期类型
                 */
                @NotNull
                @JsonProperty("DateType")
                private DateType dateType;

                /**
                 * 开始日期
                 * 举例：DateType为CheckInDay：表示当前订单的入住日期落在StartDate和EndDate之间，
                 * 并且入住日期符合周设置时才需要判断其它条件是否担保，否则不需要担保
                 */
                @NotNull
                @JsonProperty("StartDate")
                private String startDate;

                /**
                 * 结束日期
                 * 举例：DateType为CheckInDay：表示当前订单的入住日期落在StartDate和EndDate之间，
                 * 并且入住日期符合周设置时才需要判断其它条件是否担保，否则不需要担保
                 */
                @NotNull
                @JsonProperty("EndDate")
                private String endDate;

                /**
                 * 周有效天数， 一般为周一到周日都有效， 判断日期符合日期段同时也要满足周设置的有效
                 * 周一对应为1，周二对应为2， 依次类推;逗号分隔
                 * 为空时表示无周末设置
                 * DateType为StayDay：表示当前订单的客人只要有住在店里面的日期（ArrivalDate,DepartureDate）落在StartDate和EndDate之间，
                 * 并且入住日期符合周设置时才需要判断其它条件是否担保，否则不需要担保
                 */
                @Nullable
                @JsonProperty("WeekSet")
                private String weekSet;

                /**
                 * 是否到店时间担保
                 * False:为不校验到店时间
                 * True:为需要校验到店时间
                 * 此字段与之后的IsAmountGuarantee字段用法比较特殊，请仔细阅读注意事项中关于这两个字段的说明。
                 */
                @NotNull
                @JsonProperty("IsTimeGuarantee")
                private Boolean isTimeGuarantee;

                /**
                 * 到店担保开始时间
                 * 用于IsTimeGuarantee==true进行检查。
                 */
                @Nullable
                @JsonProperty("StartTime")
                private String startTime;

                /**
                 * 到店担保结束时间
                 * 当EndTime小于StartTime的时候，默认从StartTime到次日6点都需要担保。
                 */
                @Nullable
                @JsonProperty("EndTime")
                private String endTime;

                /**
                 * 到店担保的结束时间是否为第二天
                 * 0为当天，1为次日
                 */
                @Nullable
                @JsonProperty("IsTomorrow")
                private Boolean isTomorrow;

                /**
                 * 是否房量担保
                 * False:为不校验房量条件
                 * True:为校验房量条件
                 */
                @NotNull
                @JsonProperty("IsAmountGuarantee")
                private Boolean isAmountGuarantee;

                /**
                 * 担保的房间数,预定几间房以上要担保
                 * 用于IsAmountGuarantee==true进行检查
                 */
                @Nullable
                @JsonProperty("Amount")
                private Integer amount;

                /**
                 * 担保类型
                 * FirstNightCost为首晚房费担保
                 * FullNightCost为全额房费担保
                 */
                @Nullable
                @JsonProperty("GuaranteeType")
                private String guaranteeType;

                /**
                 * 变更规则
                 * 担保规则取消变更规则：
                 * NoChange、不允许变更取消
                 * NeedSomeDay、允许变更/取消,需在XX日YY时之前通知
                 * NeedCheckinTime、允许变更/取消,需在最早到店时间之前几小时通知
                 * NeedCheckin24hour、允许变更/取消,需在到店日期的24点之前几小时通知
                 */
                @Nullable
                @JsonProperty("ChangeRule")
                private GuaranteeChangeRule changeRule;

                /**
                 * 日期参数
                 * ChangeRule=NeedSomeDay时，对应规则2描述中 “允许变更/取消,需在XX日YY时之前通知” 中的XX日，YY时
                 */
                @Nullable
                @JsonProperty("Day")
                private String day;

                /**
                 * 时间参数
                 * ChangeRule=NeedSomeDay时，对应规则2描述中 “允许变更/取消,需在XX日YY时之前通知” 中的XX日，YY时
                 */
                @Nullable
                @JsonProperty("Time")
                private String time;

                /**
                 * 小时参数
                 * ChangeRule=NeedCheckinTime时，对应规则3描述中 “ 允许变更/取消,需在最早到店时间之前几小时通知” 中的几小时
                 * ChangeRule=NeedCheckin24hour时，对应规则4描述中“ 允许变更/取消,需在到店日期的24点之前几小时通知” 中的几小时
                 */
                @Nullable
                @JsonProperty("Hour")
                private Integer hour;
            }

            /**
             * PrepayRule节点
             */
            @Data
            @NoArgsConstructor
            @AllArgsConstructor
            public static class PrepayRule {

                /**
                 * 描述
                 */
                @NotNull
                @JsonProperty("Description")
                private String description;

                /**
                 * 日期类型
                 * CheckInDay：入住日期（该字段后期下线，可以不用判断）
                 */
                @NotNull
                @JsonProperty("DateType")
                private String dateType;

                /**
                 * 开始日期
                 * 使用离线数据模式需要判断
                 */
                @Nullable
                @JsonProperty("StartDate")
                private String startDate;

                /**
                 * 结束日期
                 * 使用离线数据模式需要判断
                 */
                @Nullable
                @JsonProperty("EndDate")
                private String endDate;

                /**
                 * 周有效设置
                 * 使用离线数据模式需要判断
                 */
                @Nullable
                @JsonProperty("WeekSet")
                private String weekSet;

                /**
                 * 变更规则
                 */
                @NotNull
                @JsonProperty("ChangeRule")
                private PrepayChangeRule changeRule;

                /**
                 * 第一阶段提前的几小时
                 * 用于PrepayNeedSomeDay
                 */
                @Nullable
                @JsonProperty("Hour")
                private Integer hour;

                /**
                 * 第二阶段提前的几小时
                 * 用于PrepayNeedSomeDay
                 */
                @Nullable
                @JsonProperty("Hour2")
                private Integer hour2;

                /**
                 * 具体取消时间日期部分
                 * 用于PrepayNeedOneTime
                 */
                @Nullable
                @JsonProperty("DateNum")
                private String dateNum;

                /**
                 * 具体取消时间小时部分
                 * 用于PrepayNeedOneTime
                 */
                @Nullable
                @JsonProperty("Time")
                private String time;

                /**
                 * 在变更时间点前是否扣费
                 * 用于 PrepayNeedSomeDay的Hour前扣款类型（一般不收罚金）。DeductFeesBefore为1表示扣款，0表示不扣款。
                 */
                @Nullable
                @JsonProperty("DeductFeesBefore")
                private Integer deductFeesBefore;

                /**
                 * 时间点前扣费的金额或比例
                 * 用于 PrepayNeedSomeDay的Hour前扣款类型（一般不收罚金）。DeductFeesBefore为1表示扣款，0表示不扣款。
                 */
                @Nullable
                @JsonProperty("DeductNumBefore")
                private BigDecimal DeductNumBefore;

                /**
                 * 时间点后扣款类型
                 */
                @Nullable
                @JsonProperty("CashScaleFirstAfter")
                private PrepayCashType CashScaleFirstAfter;

                /**
                 * 在变更时间点后是否扣费
                 * 用于 PrepayNeedSomeDay的Hour到Hour2之间的扣款类型。DeductFeesAfter为1表示扣款，0表示不扣款。
                 * 如果CashScaleFirstAfter为FristNight，则返回-1，没有意义
                 */
                @Nullable
                @JsonProperty("DeductFeesAfter")
                private Integer deductFeesAfter;

                /**
                 * 时间点后扣费的金额或比例
                 * 用于 PrepayNeedSomeDay的Hour到Hour2之间的扣款类型。DeductFeesAfter为1表示扣款，0表示不扣款。
                 * 如果CashScaleFirstAfter为FristNight，则返回-1，没有意义
                 */
                @Nullable
                @JsonProperty("DeductNumAfter")
                private BigDecimal deductNumAfter;

                /**
                 * 时间点前扣款类型
                 */
                @Nullable
                @JsonProperty("CashScaleFirstBefore")
                private PrepayCashType cashScaleFirstBefore;
            }

            /**
             * PrepayRuleExtend节点
             */
            @Data
            @NoArgsConstructor
            @AllArgsConstructor
            public static class PrepayRuleExtend {

                /**
                 * 开始时间
                 * 按入住日匹配，入住日在开始时间和结束时间之间，且符合周有效规则，即为命中此规则
                 */
                @NotNull
                @JsonProperty("StartDate")
                private String startDate;

                /**
                 * 结束时间
                 */
                @NotNull
                @JsonProperty("EndDate")
                private String endDate;

                /**
                 * 周有效设置
                 */
                @NotNull
                @JsonProperty("WeekSet")
                private String weekSet;

                /**
                 * 取消费用类型
                 * 0:跟随取消费用 1:订单全额（目前只有0）
                 */
                @Nullable
                @JsonProperty("NoshowPenalty")
                private Integer noShowPenalty;

                /**
                 * 取消规则列表
                 * 解析示例: https://open.elong.com/doc/info/cn-api-meta-hotel_data_rp#RuleExtend%E8%A7%A3%E6%9E%90
                 */
                @NotNull
                @JsonProperty("PenaltyRuleList")
                private List<PenaltyWindowType> penaltyRuleList;

                /**
                 * 规则类型
                 * 1：长期规则；2：特殊规则，优先看特殊规则
                 */
                @Nullable
                @JsonProperty("SubId")
                private Integer subId;
            }

            /**
             * GuaranteeRuleExtend节点
             */
            @Data
            @NoArgsConstructor
            @AllArgsConstructor
            public static class GuaranteeRuleExtend {

                /**
                 * 开始时间
                 * 按入住日匹配，入住日在开始时间和结束时间之间，且符合周有效规则，即为命中此规则
                 */
                @Nullable
                @JsonProperty("StartDate")
                private String startDate;

                /**
                 * 结束时间
                 */
                @Nullable
                @JsonProperty("EndDate")
                private String endDate;

                /**
                 * 周有效设置
                 */
                @Nullable
                @JsonProperty("WeekSet")
                private String weekSet;

                /**
                 * 担保类型
                 * 0:需担保 1:无需担保 2:超时担保
                 */
                @Nullable
                @JsonProperty("GuaranteeType")
                private Integer guaranteeType;

                /**
                 * 取消费用类型
                 * 0:跟随取消费用 1:订单全额 计算担保金额用，0时取PenaltyRuleList计算出的罚金列表中罚金的最大值
                 */
                @Nullable
                @JsonProperty("NoshowPenalty")
                private Integer noShowPenalty;

                /**
                 * 超时担保时间
                 * 单位分钟，相对入住日24点的小时偏移量, 范围[0,840]
                 */
                @Nullable
                @JsonProperty("GrtLatestCheckTime")
                private Integer grtLatesCheckTime;

                /**
                 * 取消规则列表
                 * 解析示例: https://open.elong.com/doc/info/cn-api-meta-hotel_data_rp#RuleExtend%E8%A7%A3%E6%9E%90
                 */
                @Nullable
                @JsonProperty("PenaltyRuleList")
                private List<PenaltyWindowType> penaltyRuleList;

                /**
                 * 规则类型
                 * 1：长期规则；2：特殊规则，优先看特殊规则
                 */
                @Nullable
                @JsonProperty("SubId")
                private Integer subId;
            }

            /**
             * PenaltyWindowType节点
             */
            @Data
            @NoArgsConstructor
            @AllArgsConstructor
            public static class PenaltyWindowType {

                /**
                 * 扣款类型
                 * 0:百分比 1:晚数  2:首晚百分比
                 */
                @NotNull
                @JsonProperty("PenaltyType")
                private Integer penaltyType;

                /**
                 * 罚金
                 * PenaltyType 为 0,2时，此值为两位小数。
                 * PenaltyType  为1是，此值使用时会取整，按整数处理。
                 */
                @NotNull
                @JsonProperty("PenaltyValue")
                private BigDecimal penaltyValue;

                /**
                 * 规则时间分割起始点
                 * 单位分钟，第一个点为1439280
                 */
                @NotNull
                @JsonProperty("Deadline")
                private String deadline;
            }

            /**
             * ValueAdd节点
             */
            @Data
            @NoArgsConstructor
            @AllArgsConstructor
            public static class ValueAdd {

                /**
                 * 业务代码
                 * 01-早餐
                 * 02-午餐
                 * 03-晚餐
                 * 04-宽带上网
                 * 05-服务费
                 * 06-政府税
                 * 99-特殊早餐，有效日期内生效，优先级高于01早餐
                 * 当99特殊早餐和01早餐同时存在时，需要根据特殊早餐的有效日期判断哪种早餐生效，
                 * 即在特殊早餐有效日期内99特殊早餐生效，有效日期外01早餐生效。
                 */
                @NotNull
                @JsonProperty("TypeCode")
                private String typeCode;

                /**
                 * 描述
                 * 附加服务描述，代理不想解析的话，可以直接显示该描述
                 */
                @NotNull
                @JsonProperty("Description")
                private String description;

                /**
                 * 是否包含在房费中
                 * false-不包含 true-包含，例如业务代码为早餐时，false即为不含早，true为含早
                 */
                @NotNull
                @JsonProperty("IsInclude")
                private Boolean isInclude;

                /**
                 * 包含的份数
                 */
                @Nullable
                @JsonProperty("Amount")
                private Integer amount;

                /**
                 * 货币代码
                 */
//                @NotNull
                @Nullable
                @JsonProperty("CurrencyCode")
                // TODO: 枚举
                private String currencyCode;

                /**
                 * 单价默认选项
                 */
                @Nullable
                @JsonProperty("PriceOption")
                private PriceOption priceOption;

                /**
                 * 单价
                 * 视PriceOption表示金额或比例，比例值保存的百分数，不是最终的小数，例如 20%，则该字段保存为20
                 */
                @Nullable
                @JsonProperty("Price")
                private BigDecimal price;

                /**
                 * 是否单加
                 * 目前只有早餐服务该字段有意义
                 */
                @Nullable
                @JsonProperty("IsExtAdd")
                private Boolean isExtAdd;

                /**
                 * 单加单价默认选项
                 */
                @NotNull
                @JsonProperty("ExtOption")
                private PriceOption extOption;

                /**
                 * 单加单价
                 * 视 extOption 不同表示金额或比例值, 比例值保存的百分数，不是最终的小数 例如 20%， 则该字段保存为20
                 */
                @Nullable
                @JsonProperty("ExtPrice")
                private BigDecimal extPrice;

                /**
                 * 开始日期
                 * 特殊早餐有效日期
                 */
                @Nullable
                @JsonProperty("StartDate")
                private String startDate;

                /**
                 * 结束日期
                 * 特殊早餐有效日期
                 */
                @Nullable
                @JsonProperty("EndDate")
                private String endDate;

                /**
                 * 周有效设置
                 * 特殊早餐有效日期
                 */
                @Nullable
                @JsonProperty("WeekSet")
                private String weekSet;
            }

            /**
             * Meal节点
             */
            @Data
            @NoArgsConstructor
            @AllArgsConstructor
            public static class Meal {

                /**
                 * 餐食类型
                 * 01-默认餐食
                 * 02-带有效期范围的餐食
                 * 当02和01同时存在时，入住日在02餐食有效日期内则02餐食生效，入住日在02范围内解析是无餐食也不看01默认餐食了；否则有效日期外01默认餐食生效。
                 * 02餐食可能有多条，01默认餐食最多1条。
                 * （注：此节点为餐食的原始规则节点，hotel.detail#Meals为移位后的到天餐食结果表格。以下2种情况需要移位：
                 * 1、入住日期内全部为固定餐食，
                 * 2、入住日期内全部为半固定餐食且固定餐食类型一样时，固定餐食早餐、午餐需向后移一天展示；动态餐食分别与入住日期对应，不需要后移一天）
                 */
                @NotNull
                @JsonProperty("Type")
                private String type;

                /**
                 * 是否包含在房费中
                 * false-不包含 true-包含，false即为不含餐食，true为含餐食
                 */
                @NotNull
                @JsonProperty("IsInclude")
                private Boolean isInclude;

                /**
                 * 早餐份数
                 */
                @NotNull
                @JsonProperty("NumberOfBreakfast")
                private Integer numberOfBreakfast;

                /**
                 * 午餐份数
                 */
                @NotNull
                @JsonProperty("NumberOfLunch")
                private Integer numberOfLunch;

                /**
                 * 晚餐份数
                 */
                @NotNull
                @JsonProperty("NumberOfDinner")
                private Integer numberOfDinner;

                /**
                 * 餐食种类数量
                 * 表示早餐份数、午餐份数、晚餐份数这三个字段大于0的字段数量之和；最小为0，最大为3
                 */
                @NotNull
                @JsonProperty("NumberOfTypeMeal")
                private Integer numberOfTypeMeal;

                /**
                 * 可选餐食种类数量
                 * 当NumberOfTypeMeal>NumberOfOptionalMeal时，表示动态餐食，有以下几种情况：
                 * 1、早午晚餐，三选一、三选二
                 * 2、早午餐，二选一
                 * 3、午晚餐，二选一
                 * 4、早晚餐，二选一
                 * 当NumberOfTypeMeal=NumberOfOptionalMeal时，表示固定餐食，有以下几种情况：
                 * 1、早+午+晚
                 * 2、早+午
                 * 3、早+晚
                 * 4、午+晚
                 * 5、只有早/午/晚
                 * NumberOfTypeMeal为0或NumberOfOptionalMeal为0，均为无餐食
                 */
                @NotNull
                @JsonProperty("NumberOfOptionalMeal")
                private Integer numberOfOptionalMeal;

                /**
                 * 可选餐食类型
                 * 表示可选餐食的类型，多个类型以“,”分割，只有当餐食为固定+动态餐食时才会有值
                 * 比如餐食为早餐两份+午餐2份或晚餐2份(到店2选1)，该字段有值，为"Lunch,Dinner"
                 */
                @Nullable
                @JsonProperty("optionalMeals")
                private String optionalMeals;

                /**
                 * 描述
                 * 餐食描述
                 */

                @NotNull
                @JsonProperty("Description")
                private String description;

                /**
                 * 早餐描述
                 */
                @Nullable
                @JsonProperty("DescribeOfBreakfast")
                private String describeOfBreakfast;

                /**
                 * 午餐描述
                 */
                @Nullable
                @JsonProperty("DescribeOfLunch")
                private String describeOfLunch;

                /**
                 * 晚餐描述
                 */
                @Nullable
                @JsonProperty("DescribeOfDinner")
                private String describeOfDinner;

                /**
                 * 开始日期
                 * 02餐食的有效日期；01默认餐食时此字段均为空
                 */
                @Nullable
                @JsonProperty("StartDate")
                private String startDate;

                /**
                 * 结束日期
                 * 02餐食的有效日期；01默认餐食时此字段均为空
                 */
                @Nullable
                @JsonProperty("EndDate")
                private String endDate;

                /**
                 * 周有效设置
                 * 02餐食的有效日期；01默认餐食时此字段均为空
                 */
                @Nullable
                @JsonProperty("WeekSet")
                private String weekSet;
            }

            /**
             * DrrRule节点
             */
            @Data
            @NoArgsConstructor
            @AllArgsConstructor
            public static class DrrRule {

                /**
                 * 关联的房型
                 * 表示这个DrrRule关联的销售房型
                 */
                @Nullable
                @JsonProperty("RoomTypeIds")
                private String roomTypeIds;

                /**
                 * 产品促销规则类型代码
                 */
                @NotNull
                @JsonProperty("TypeCode")
                private DRRTypeCode typeCode;

                /**
                 * 描述
                 */
                @Nullable
                @JsonProperty("Description")
                private String description;

                /**
                 * 日期类型
                 */
                @NotNull
                @JsonProperty("DateType")
                private DateType dateType;

                /**
                 * 促销生效开始日期
                 */
                @Nullable
                @JsonProperty("StartDate")
                private String startDate;

                /**
                 * 促销生效结束日期
                 */
                @Nullable
                @JsonProperty("EndDate")
                private String EndDate;

                /**
                 * 提前几天
                 */
                @Nullable
                @JsonProperty("DayNum")
                private Integer dayNum;

                /**
                 * 连住几天
                 */
                @Nullable
                @JsonProperty("CheckInNum")
                private Integer checkInNum;

                /**
                 * 每连住几晚
                 */
                @Nullable
                @JsonProperty("EveryCheckInNum")
                private Integer everyCheckInNum;

                /**
                 * 最后几天
                 */
                @Nullable
                @JsonProperty("LastDayNum")
                private Integer lastDayNum;

                /**
                 * 第几晚及以后优惠
                 */
                @Nullable
                @JsonProperty("WhichDayNum")
                private Integer whichDayNum;

                /**
                 * 按金额或按比例来优惠
                 * Cash-金额 Scale-比例
                 */
                @Nullable
                @JsonProperty("CashScale")
                private CashScale cashScale;

                /**
                 * 按金额或比例优惠的数值
                 * 当CashScale为Percent时，该值保存的为百分数，例如30%
                 */
                @Nullable
                @JsonProperty("DeductNum")
                private BigDecimal deductNum;

                /**
                 * 星期有效设置
                 * 日期符合Weekset中的周设置，才享受 feetype所对应的价格
                 * 仅DRRStayWeekDay和DRRCheckInWeekDay的时候使用
                 */
                @Nullable
                @JsonProperty("WeekSet")
                private String weekSet;

                /**
                 * 关联的房型
                 */
                @Nullable
                @JsonProperty("FeeType")
                private FeeType feeType;
            }

            /**
             * Coupon节点
             */
            @Data
            @NoArgsConstructor
            @AllArgsConstructor
            public static class Coupon {

                /**
                 * 有效开始日期
                 */
                @NotNull
                @JsonProperty("EffectiveDateFrom")
                private String effectiveDateFrom;

                /**
                 * 有效结束日期
                 */
                @NotNull
                @JsonProperty("EffectiveDateTo")
                private String effectiveDateTo;

                /**
                 * 最高上限值
                 * 根据用户优惠券的情况实行抵扣，抵扣不能超过这个数额
                 */
                @NotNull
                @JsonProperty("CouponRedemptionUpperLimit")
                private BigDecimal couponRedemptionUpperLimit;

                /**
                 * 类型
                 * 1 - 返现
                 * 9 - 预付立减
                 */
                @NotNull
                @JsonProperty("PromotionType")
                private Integer promotionType;

                /**
                 * 关联的房型
                 */
                @NotNull
                @JsonProperty("RoomTypeID")
                private String roomTypeId;

                /**
                 * 关联的产品
                 */
                @NotNull
                @JsonProperty("RatePlanID")
                private Integer ratePlanId;
            }

            /**
             * ServiceTimePolicyInfo节点
             */
            @Data
            @NoArgsConstructor
            @AllArgsConstructor
            public static class ServiceTimePolicyInfo {

                /**
                 * 服务开始日期
                 * 酒店或供应商能处理订单的时间，无数据代表全天服务
                 * 预订当天入住且预订时间不在服务时间范围内的产品不可展示销售给客人，避免不必要的投诉。
                 */
                @Nullable
                @JsonProperty("start_date")
                private String startDate;

                /**
                 * 服务结束日期
                 * 酒店或供应商能处理订单的时间，无数据代表全天服务
                 * 预订当天入住且预订时间不在服务时间范围内的产品不可展示销售给客人，避免不必要的投诉。
                 */
                @Nullable
                @JsonProperty("end_date")
                private String endDate;

                /**
                 * 服务周有效
                 * 周日:0,周一:1,周二:2,周三:3,周四:4,周五:5,周六:6
                 * 在服务开始结束日期范围内，周无效代表不可服务
                 */
                @Nullable
                @JsonProperty("week_effective")
                private List<Integer> weekEffective;

                /**
                 * 服务开始时间
                 * 可服务的时间段，如果end_time小于start_time，则end_time表示的是次日的时间
                 */
                @Nullable
                @JsonProperty("start_time")
                private String startTime;

                /**
                 * 服务结束时间
                 * 可服务的时间段，如果end_time小于start_time，则end_time表示的是次日的时间
                 */
                @Nullable
                @JsonProperty("end_time")
                private String endTime;
            }

            /**
             * TimeRushRule节点
             */
            @Data
            @NoArgsConstructor
            @AllArgsConstructor
            public static class TimeRushRule {

                /**
                 * 规则生效开始日期
                 * 限时抢产品规则，只对产品类型为限时抢的产品生效。
                 * 在规则生效日期时间范围内开启限时抢，可抢购时间范围为startTime到endTime。
                 */
                @Nullable
                @JsonProperty("startDate")
                private String startDate;

                /**
                 * 规则生效开始时间
                 * 限时抢产品规则，只对产品类型为限时抢的产品生效。
                 * 在规则生效日期时间范围内开启限时抢，可抢购时间范围为startTime到endTime。
                 */
                @Nullable
                @JsonProperty("bookingStartTime")
                private String bookingStartTime;

                /**
                 * 规则生效结束日期
                 * 限时抢产品规则，只对产品类型为限时抢的产品生效。
                 * 在规则生效日期时间范围内开启限时抢，可抢购时间范围为startTime到endTime。
                 */
                @Nullable
                @JsonProperty("endDate")
                private String endDate;

                /**
                 * 规则生效结束时间
                 * 限时抢产品规则，只对产品类型为限时抢的产品生效。
                 * 在规则生效日期时间范围内开启限时抢，可抢购时间范围为startTime到endTime。
                 */
                @Nullable
                @JsonProperty("bookingEndTime")
                private String bookingEndTime;

                /**
                 * 限时抢开始时间
                 * 限时抢产品规则，只对产品类型为限时抢的产品生效。
                 * 在规则生效日期时间范围内开启限时抢，可抢购时间范围为startTime到endTime。
                 */
                @Nullable
                @JsonProperty("startTime")
                private String startTime;

                /**
                 * 限时抢结束时间
                 * 限时抢产品规则，只对产品类型为限时抢的产品生效。
                 * 在规则生效日期时间范围内开启限时抢，可抢购时间范围为startTime到endTime。
                 */
                @Nullable
                @JsonProperty("endTime")
                private String endTime;
            }
        }

        /**
         * Gift节点
         */
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class Gift {

            /**
             * 送礼编号
             * 关联RatePlan.GiftId
             */
            @JsonProperty("HotelGiftId")
            private Integer hotelGiftId;

            /**
             * 礼包副标题
             * 关于礼品副标题的描述
             */
            @JsonProperty("GiftDescription")
            private String giftDescription;

            /**
             * 描述
             * 关于礼品的描述
             */
            @JsonProperty("Description")
            private String description;

//            /**
//             * 开始时间
//             */
//            @JsonProperty("StartDate")
//            private String startDate;
//
//            /**
//             * 结束时间
//             */
//            @JsonProperty("EndDate")
//            private String endDate;

            /**
             * 有效日期列表
             */
            @JsonProperty("EffectiveDates")
            private List<EffectiveDate> effectiveDates;

            /**
             * 日期类型
             */
            @JsonProperty("DateType")
            private DateType dateType;

            /**
             * 星期设置
             */
            @JsonProperty("WeekSet")
            private String weekSet;

            /**
             * 活动内容
             */
            @JsonProperty("GiftContent")
            private String giftContent;

            /**
             * 送礼类型
             * 注意：1.52版本及以后该字段废弃，请使用GiftInfos字段，1.52之前的版本扔使用该字段。
             */
            @JsonProperty("GiftTypes")
            private String giftTypes;

            /**
             * 新的送礼类型
             */
            @JsonProperty("GiftInfos")
            private List<GiftInfo> giftInfos;

            /**
             * 小时数
             */
            @JsonProperty("HourNumber")
            private Integer hourNumber;

            /**
             * 小时数的类型
             */
            @JsonProperty("HourType")
            private HourType hourType;

            /**
             * 送礼方式
             */
            @JsonProperty("WayOfGiving")
            private WayOfGiving wayOfGiving;

            /**
             * 其他的送礼具体方式
             * 送礼方式为其他的时候，送礼活动的名称
             */
            @JsonProperty("WayOfGivingOther")
            private String wayOfGivingOther;

            /**
             * 礼包价值
             * 礼包、套餐的预估总价值
             */
            @JsonProperty("GiftValue")
            private BigDecimal giftValue;

            /**
             * EffectiveDate节点
             */
            @Data
            public static class EffectiveDate {

                /**
                 * 开始时间
                 */
                @JsonProperty("StartDate")
                private String startDate;

                /**
                 * 结束时间
                 */
                @JsonProperty("EndDate")
                private String endDate;
            }

            /**
             * GiftInfo节点
             */
            @Data
            @NoArgsConstructor
            @AllArgsConstructor
            public static class GiftInfo {

                /**
                 * 礼包一级编号
                 * 1.含礼品
                 * 2.延迟退房
                 * 3.含餐饮
                 * 4.含旅游门票
                 * 5.含折扣/抵扣券
                 * 6.含交通
                 * 7.其他
                 */
                @NotNull
                @JsonProperty("GiftInfo")
                private Integer giftInfo;

                /**
                 * 二级礼包内容
                 */
                @NotNull
                @JsonProperty("GiftSubInfos")
                private List<GiftSubInfo> giftSubInfos;

                /**
                 * GiftSubInfo节点
                 */
                @Data
                @NoArgsConstructor
                @AllArgsConstructor
                public static class GiftSubInfo {

                    /**
                     * 礼包二级编号
                     * 1.含果盘
                     * 3.含水果
                     * 4.含饮品
                     * 5.含精美艺品
                     * 6.其他
                     * 7.延迟至13点退房
                     * 8.延迟至14点退房
                     * 9.延迟至15点退房
                     * 10.其他
                     * 11.含午餐
                     * 12.含晚餐
                     * 13.含下午茶
                     * 14.含餐券
                     * 15.其他
                     * 16.含景点门票
                     * 17.含演出门票
                     * 18.其他
                     * 19.含店内折扣/抵扣券
                     * 20.含外部折扣/抵扣券
                     * 21.其他
                     * 22.含接站
                     * 23.含接机
                     * 24.含送站
                     * 25.含送机
                     * 26.含景区直通车
                     * 27.其他
                     * 28.其他
                     * 注意：1-6对应一级编号的1，7-10对应一级编号的2，11-15对应一级编号的3，16-18对应一级编号的4，19-21对应一级编号的5，22-27对应一级编号的6，28对应一级编号的7
                     */
                    @NotNull
                    @JsonProperty("SubInfo")
                    private Integer subInfo;
                }
            }
        }

        /**
         * GiftPackage节点
         */
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class GiftPackage {

            /**
             * 礼包套餐ID
             * 关联RatePlan.PkgProductids
             */
            @NotNull
            @JsonProperty("PkgProductId")
            private String pkgProductId;

            /**
             * 礼包套餐类型
             * 0：礼包，1：套餐
             */
            @NotNull
            @JsonProperty("PkgType")
            private Integer pkgType;

            /**
             * 礼包套餐状态
             * 0：无效，1：有效
             */
            @NotNull
            @JsonProperty("Status")
            private Integer status;

            /**
             * 礼包套餐名字
             */
            @NotNull
            @JsonProperty("PkgProductName")
            private String pkgProductName;

            /**
             * 礼包套餐特别说明
             */
            @Nullable
            @JsonProperty("RuleDescriptionAdditional")
            private String ruleDescriptionAdditional;

            /**
             * 礼包套餐图片
             */
            @Nullable
            @JsonProperty("Pictures")
            private List<Picture> pictures;

            /**
             * X产品列表
             */
            @Nullable
            @JsonProperty("XProducts")
            private List<XProduct> xProducts;

            /**
             * 礼包套餐关联的产品
             */
            @NotNull
            @JsonProperty("RelatedProduct")
            private RelatedProduct relatedProduct;

            /**
             * Picture节点
             */
            @Data
            @NoArgsConstructor
            @AllArgsConstructor
            public static class Picture {

                /**
                 * 礼包图片顺序
                 */
                @NotNull
                @JsonProperty("ImgIndex")
                private Integer imgIndex;

                /**
                 * 礼包图片链接
                 */
                @NotNull
                @JsonProperty("ImgUrl")
                private String imgUrl;
            }

            /**
             * XProduct节点
             */
            @Data
            @NoArgsConstructor
            @AllArgsConstructor
            public static class XProduct {

                /**
                 * X产品ID
                 * X产品即一个具体的礼包
                 */
                @NotNull
                @JsonProperty("XProductId")
                private String xProductId;

                /**
                 * X产品名字
                 */
                @NotNull
                @JsonProperty("XProductName")
                private String xProductName;

                /**
                 * X产品状态
                 * 0：无效，1：有效
                 */
                @NotNull
                @JsonProperty("Status")
                private Integer status;

                /**
                 * X产品类型
                 */
                @Nullable
                @JsonProperty("TypeName")
                private String typeName;

                /**
                 * X产品数量
                 */
                @Nullable
                @JsonProperty("Quantity")
                private String quantity;

                /**
                 * X产品接待时间
                 */
                @Nullable
                @JsonProperty("ReceptionTimes")
                private String receptionTimes;

                /**
                 * X产品适用人数
                 */
                @Nullable
                @JsonProperty("Capacity")
                private String capacity;

                /**
                 * X产品预订电话
                 */
                @Nullable
                @JsonProperty("BookingPhone")
                private String bookingPhone;

                /**
                 * X产品预订规则
                 */
                @Nullable
                @JsonProperty("AppointPolicy")
                private String appointPolicy;
            }

            /**
             * RelatedProduct节点
             */
            @Data
            @NoArgsConstructor
            @AllArgsConstructor
            public static class RelatedProduct {

                /**
                 * 供应商房型id
                 */
                @NotNull
                @JsonProperty("RoomTypeId")
                private String roomTypeId;

                /**
                 * 价格计划ID
                 */
                @NotNull
                @JsonProperty("RatePlanId")
                private Integer ratePlanId;
            }
        }
    }
}
