package com.somytrip.entity.hotel.ELong;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.math.BigDecimal;

/**
 * @ClassName: ELongHotelOrderCancelResp
 * @Description: 同程艺龙酒店取消订单响应
 * @Author: shadow
 * @Date: 2024/2/24 15:01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ELongHotelOrderCancelResp {

    /**
     * 取消请求处理结果
     * 注意，是三个s。
     * 此处返回true的时候表示艺龙已经收到了请求，是否能够退款需要查看订单详情中的refundDetail节点
     */
    @NotNull
    @JsonProperty("Successs")
    private Boolean success;

    /**
     * 取消罚金
     * 取消罚金值
     */
    @Nullable
    @JsonProperty("PenaltyAmount")
    private BigDecimal penaltyAmount;
}
