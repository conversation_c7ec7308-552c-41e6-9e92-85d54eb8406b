package com.somytrip.entity.hotel.WEBBEDS;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName: WEBBEDSHotelsResp
 * @Description: WEBBEDS酒店列表响应
 * @Author: shadow
 * @Date: 2024/3/19 2:53
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WEBBEDSHotelsResp {

    /**
     * 酒店列表
     */
    private List<Hotel> hotels;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Hotel {

        /**
         * 酒店ID
         */
        private Long id;

        /**
         * 城市ID
         */
        @JsonProperty("city_id")
        private Long cityId;

        /**
         * 城市名称
         */
        @JsonProperty("city_name")
        private String cityName;

        /**
         * 行政区ID
         */
        @JsonProperty("district_id")
        private Long districtId;

        /**
         * 行政区名称
         */
        @JsonProperty("district_name")
        private String districtName;

        /**
         * 邮政编码
         */
        @JsonProperty("zip_code")
        private String zipCode;

        /**
         * 酒店名称
         */
        private String name;

        /**
         * 品牌名称
         */
        @JsonProperty("brand_name")
        private String brandName;

        /**
         * 地址
         */
        private String address;

        /**
         * 酒店简介
         */
        private String description;

        /**
         * 开业日期
         */
        @JsonProperty("start_date")
        private String startDate;

        /**
         * 酒店类型
         */
        private String type;

        /**
         * 酒店星级
         */
        private Long star;

        /**
         * 酒店电话
         */
        private String telephone;

        /**
         * 酒店评分
         */
        private Double rating;

        /**
         * 酒店评分总量
         */
        @JsonProperty("total_rate_count")
        private Long totalRateCount;

        /**
         * 酒店纬度
         */
        private String latitude;

        /**
         * 酒店精度
         */
        private String longitude;

        /**
         * 酒店服务与设施
         */
        @JsonProperty("services_and_facilities")
        private List<ServiceAndFacility> servicesAndFacilities;

        /**
         * 酒店通知列表
         */
        private List<Notice> notices;

        /**
         * 酒店图片列表
         */
        private List<String> images;

        /**
         * 是否可订
         */
        private Boolean bookable;

        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class ServiceAndFacility {

            /**
             * 服务与设施组ID
             */
            private Integer id;

            /**
             * 服务与设施图标文件组ID
             */
            private Integer icon;

            /**
             * 服务与设施图标文件组名称
             */
            private String name;

            /**
             * 组内服务与设施列表
             */
            private List<ServiceAndFacilityItem> items;

            @Data
            @NoArgsConstructor
            @AllArgsConstructor
            public static class ServiceAndFacilityItem {

                /**
                 * 服务/设施ID
                 */
                private Integer id;

                /**
                 * 服务/设施图标ID
                 */
                private Integer icon;

                /**
                 * 服务/设施名称
                 */
                private String name;

                /**
                 * 所属房型ID
                 */
                @JsonProperty("room_type_id")
                private Integer roomTypeId;

                /**
                 * 所属Room ID
                 */
                @JsonProperty("room_id")
                private Integer roomId;
            }
        }

        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class Notice {

            /**
             * 通知标题
             */
            private String title;

            /**
             * 通知内容列表
             */
            private List<NoticeItem> items;

            @Data
            @NoArgsConstructor
            @AllArgsConstructor
            public static class NoticeItem {

                /**
                 * ID
                 */
                private Integer id;

                /**
                 * 内容
                 */
                private String text;

                /**
                 * 开始生效时间
                 */
                @JsonProperty("start_date")
                private String startDate;

                /**
                 * 结束时间
                 */
                @JsonProperty("end_date")
                private String endDate;
            }
        }
    }
}
