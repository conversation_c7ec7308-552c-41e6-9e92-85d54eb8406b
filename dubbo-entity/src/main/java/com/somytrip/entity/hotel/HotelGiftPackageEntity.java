package com.somytrip.entity.hotel;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.somytrip.entity.enums.hotel.HotelOrigin;
import com.somytrip.entity.hotel.ELong.offline.ELongHotelDataRpResp;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * (HotelGiftPackages)实体类
 *
 * <AUTHOR>
 * @since 2024-02-19 17:44:55
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("hotel_gift_packages")
public class HotelGiftPackageEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 630158229103448142L;

    /**
     * 自增主键ID
     */
    private Long id;
    /**
     * 酒店来源
     */
    private HotelOrigin hotelOrigin;
    /**
     * 酒店ID
     */
    private String hotelId;
    /**
     * 礼包套餐ID
     */
    private String pkgProductId;
    /**
     * 礼包套餐类型，0：礼包，1：套餐
     */
    private Integer pkgType;
    /**
     * 礼包套餐状态，0：无效，1：有效
     */
    private Integer status;
    /**
     * 礼包套餐名称
     */
    private String pkgProductName;
    /**
     * 礼包套餐特别说明
     */
    private String ruleDescriptionAdditional;
    /**
     * 礼包套餐图片
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<PictureEntity> pictures;
    /**
     * x产品列表
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<XProductEntity> xProducts;
    /**
     * 礼包套餐关联的产品
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private RelatedProductEntity relatedProduct;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public HotelGiftPackageEntity(ELongHotelDataRpResp.Hotel.GiftPackage elongGiftPackage, String hotelId) {
        this.hotelId = hotelId;
        this.hotelOrigin = HotelOrigin.ELong;
        this.pkgProductId = elongGiftPackage.getPkgProductId();
        this.pkgType = elongGiftPackage.getPkgType();
        this.status = elongGiftPackage.getStatus();
        this.pkgProductName = elongGiftPackage.getPkgProductName();
        this.ruleDescriptionAdditional = elongGiftPackage.getRuleDescriptionAdditional();
        if (elongGiftPackage.getPictures() != null) {
            List<PictureEntity> pictureEntityList = new ArrayList<>();
            for (ELongHotelDataRpResp.Hotel.GiftPackage.Picture picture : elongGiftPackage.getPictures()) {
                pictureEntityList.add(new PictureEntity(picture));
            }
            this.pictures = pictureEntityList;
        }
        if (elongGiftPackage.getXProducts() != null) {
            List<XProductEntity> xProductEntityList = new ArrayList<>();
            for (ELongHotelDataRpResp.Hotel.GiftPackage.XProduct xProduct : elongGiftPackage.getXProducts()) {
                xProductEntityList.add(new XProductEntity(xProduct));
            }
            this.xProducts = xProductEntityList;
        }
        this.relatedProduct = new RelatedProductEntity(elongGiftPackage.getRelatedProduct());
    }

    /**
     * Picture节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PictureEntity {

        /**
         * 礼包图片顺序
         */
        private Integer imgIndex;

        /**
         * 礼包图片链接
         */
        private String imgUrl;

        public PictureEntity(ELongHotelDataRpResp.Hotel.GiftPackage.Picture elongPicture) {
            this.imgIndex = elongPicture.getImgIndex();
            this.imgUrl = elongPicture.getImgUrl();
        }
    }

    /**
     * XProduct节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class XProductEntity {

        /**
         * X产品ID
         * X产品即一个具体的礼包
         */
        private String xProductId;

        /**
         * X产品名字
         */
        private String xProductName;

        /**
         * X产品状态
         * 0：无效，1：有效
         */
        private Integer status;

        /**
         * X产品类型
         */
        private String typeName;

        /**
         * X产品数量
         */
        private String quantity;

        /**
         * X产品接待时间
         */
        private String receptionTimes;

        /**
         * X产品适用人数
         */
        private String capacity;

        /**
         * X产品预订电话
         */
        private String bookingPhone;

        /**
         * X产品预订规则
         */
        private String appointPolicy;

        public XProductEntity(ELongHotelDataRpResp.Hotel.GiftPackage.XProduct elongXProduct) {
            this.xProductId = elongXProduct.getXProductId();
            this.xProductName = elongXProduct.getXProductName();
            this.status = elongXProduct.getStatus();
            this.typeName = elongXProduct.getTypeName();
            this.quantity = elongXProduct.getQuantity();
            this.receptionTimes = elongXProduct.getReceptionTimes();
            this.capacity = elongXProduct.getCapacity();
            this.bookingPhone = elongXProduct.getBookingPhone();
            this.appointPolicy = elongXProduct.getAppointPolicy();
        }
    }

    /**
     * RelatedProduct节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RelatedProductEntity {

        /**
         * 供应商房型id
         */
        private String roomTypeId;

        /**
         * 价格计划ID
         */
        private Integer ratePlanId;

        public RelatedProductEntity(ELongHotelDataRpResp.Hotel.GiftPackage.RelatedProduct elongRelatedProduct) {
            this.roomTypeId = elongRelatedProduct.getRoomTypeId();
            this.ratePlanId = elongRelatedProduct.getRatePlanId();
        }
    }
}

