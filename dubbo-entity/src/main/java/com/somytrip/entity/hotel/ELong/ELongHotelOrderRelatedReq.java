package com.somytrip.entity.hotel.ELong;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;

/**
 * @ClassName: ELongHotelOrderRelated
 * @Description: 同程艺龙酒店关联订单请求参数
 * @Author: shadow
 * @Date: 2024/2/24 15:20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ELongHotelOrderRelatedReq {

    /**
     * 订单编号
     * 支持多个订单号查询，最多10个，以逗号分隔
     */
    @NotNull
    @JsonProperty("OrderIds")
    private String orderIds;

    /**
     * 关联类型
     * Child: 根据原来订单号查询新生成的订单
     * Parent: 根据新生成的订单号查询原来订单
     */
    @NotNull
    @JsonProperty("RelationType")
    private RelationType relationType;

    public enum RelationType {
        Child,
        Parent,
    }
}
