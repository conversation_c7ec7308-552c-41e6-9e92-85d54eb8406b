package com.somytrip.entity.hotel.WEBBEDS;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName: WEBBEDSBookResp
 * @Description: WEBBEDS创建订单响应
 * @Author: shadow
 * @Date: 2024/3/19 4:19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WEBBEDSBookResp {

    /**
     * 订单号
     */
    @JsonProperty("booking_no")
    private String bookingNo;

    /**
     * 取消政策
     */
    @JsonProperty("cancel_policies")
    private List<CancelPolicy> cancelPolicies;

    /**
     * 币种
     */
    private String currency;

    /**
     * 通知
     */
    private String notices;

    /**
     * 订单状态
     */
    @JsonProperty("order_status")
    private Integer orderStatus;

    /**
     * 订单总价
     */
    @JsonProperty("total_amount")
    private BigDecimal totalAmount;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CancelPolicy {

        /**
         * 开始日期
         */
        @JsonProperty("from_date")
        private String fromDate;

        /**
         * 截止日期
         */
        @JsonProperty("to_date")
        private String toDate;

        /**
         * 取消费用
         */
        @JsonProperty("cancel_charge")
        private BigDecimal cancelCharge;
    }
}


