package com.somytrip.entity.hotel.ELong.offline;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * @ClassName: ELongHotelIncrShardingInvResp
 * @Description: 同程艺龙酒店分片库存增量响应
 * @Author: shadow
 * @Date: 2024/2/22 16:24
 */
@Data
public class ELongHotelIncrShardingInvResp {

    /**
     * 库存变化合集
     */
    @Nullable
    @JsonProperty("Inventories")
    private List<Inventory> inventories;


    /**
     * Inventory节点
     */
    @Data
    public static class Inventory {

        /**
         * 增量ID
         */
        @Nullable
        @JsonProperty("LastId")
        private Long lastId;

        /**
         * 变化时间
         */
        @Nullable
        @JsonProperty("Time")
        private LocalDateTime time;

        /**
         * 酒店ID
         */
        @Nullable
        @JsonProperty("HotelId")
        private String hotelId;

        /**
         * 房型ID
         */
        @Nullable
        @JsonProperty("RoomTypeId")
        private String roomTypeId;

        /**
         * 酒店编码
         */
        @Nullable
        @JsonProperty("HotelCode")
        private String hotelCode;

        /**
         * 库存时间
         */
        @Nullable
        @JsonProperty("Date")
        private LocalDate date;

        /**
         * 库存状态
         * True: 表示当天房态为可用，需要更新本地房态
         * False:表示当天房态为无效，需关闭本地房态
         */
        @NotNull
        @JsonProperty("Status")
        private Boolean status;

        /**
         * 库存数量
         */
        @NotNull
        @JsonProperty("Amount")
        private Integer amount;

        /**
         * 超售状态
         * 0---可超售，可以卖出超过Amount的房间数量
         * 1—不可超售，只能销售Amount及以下的房间数量
         */
        @NotNull
        @JsonProperty("OverBooking")
        private Integer overBooking;

        /**
         * 可用开始日期
         */
        @NotNull
        @JsonProperty("StartDate")
        private LocalDate startDate;

        /**
         * 可用结束日期
         */
        @NotNull
        @JsonProperty("EndDate")
        private LocalDate endDate;

        /**
         * 可用开始时间
         */
        @NotNull
        @JsonProperty("StartTime")
        private LocalTime startTime;

        /**
         * 可用结束时间
         */
        @NotNull
        @JsonProperty("EndTime")
        private LocalTime endTime;

        /**
         * 当天库存是否支持即时确认
         */
        @Nullable
        @JsonProperty("IsInstantConfirm")
        private Boolean isInstantConfirm;

        /**
         * 预订当天即时确认可用开始时间
         */
        @Nullable
        @JsonProperty("IC_BeginTime")
        private LocalTime ic_beginTime;

        /**
         * 预订当天即时确认可用结束时间
         */
        @Nullable
        @JsonProperty("IC_EndTime")
        private LocalTime ic_endTime;
    }
}
