package com.somytrip.entity.hotel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.somytrip.entity.enums.hotel.HotelOrigin;
import com.somytrip.entity.enums.hotel.imagetype.HotelImageType;
import com.somytrip.entity.enums.hotel.imagetype.SZJLHotelImageType;
import com.somytrip.entity.hotel.ELong.ELongHotelDetailResp;
import com.somytrip.entity.hotel.ELong.offline.ELongStaticHotelInfoResp;
import com.somytrip.entity.hotel.SZJL.SZJLQueryHotelDetailResp;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 酒店图片表(HotelImages)实体类
 *
 * <AUTHOR>
 * @since 2024-02-02 15:34:25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "hotel_images", autoResultMap = true)
public class HotelImageEntity implements Serializable {


    @Serial
    private static final long serialVersionUID = -19393458902443863L;

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 酒店主键ID
     */
    private Long hotelPid;
    /**
     * 酒店来源
     */
    private HotelOrigin hotelOrigin;
    /**
     * 酒店ID
     */
    private String hotelId;
    /**
     * 关联的房间ID
     */
    private String roomId;
    /**
     * 对应的房间ID列表
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> roomIds;
    /**
     * 图片类型，1-餐厅，2-休闲，3-会议室，5-外观，6-大堂/接待台，8-客房，10-其他，11-公共区域，12-周边景点
     */
    private Integer type;
    /**
     * 图片类型中文名
     */
    private String typeName;
    /**
     * 图片类型英文名
     */
    private String typeNameEn;
    /**
     * 图片来源
     */
    private String authorType;
    /**
     * 是否是主图，当值为true的时候，表示这个图片是主图(封面图)，可用于显示在列表中
     */
    private Boolean isCoverImage;
    /**
     * 是否为房间主图，当值为true的时候，表示这个图片是房间主图，可用于显示在列表中
     */
    private Boolean isRoomCoverImage;
    /**
     * 图片地址 {waterMark:..., size:..., url:...}
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<LocationEntity> location;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public HotelImageEntity(ELongStaticHotelInfoResp.Image elongImage, Long hotelPid) {
        this.hotelPid = hotelPid;
        this.roomId = elongImage.getRoomId();
        this.type = elongImage.getType();
        this.typeName = elongImage.getTypeName();
        this.typeNameEn = elongImage.getTypeNameEn();
        this.authorType = elongImage.getAuthorType();
        this.isCoverImage = elongImage.getIsCoverImage();
        this.isRoomCoverImage = elongImage.getIsRoomCoverImage();
        if (elongImage.getLocations() != null) {
            List<LocationEntity> locationEntities = new ArrayList<>();
            for (ELongStaticHotelInfoResp.Image.Location location : elongImage.getLocations()) {
                locationEntities.add(new LocationEntity(location));
            }
            this.location = locationEntities;
        }
    }

    public HotelImageEntity(ELongStaticHotelInfoResp.Image elongImage, String hotelId) {
        this.hotelOrigin = HotelOrigin.ELong;
        this.hotelId = hotelId;
        this.roomId = elongImage.getRoomId();
        this.type = elongImage.getType();
        this.typeName = elongImage.getTypeName();
        this.typeNameEn = elongImage.getTypeNameEn();
        this.authorType = elongImage.getAuthorType();
        this.isCoverImage = elongImage.getIsCoverImage();
        this.isRoomCoverImage = elongImage.getIsRoomCoverImage();
        if (elongImage.getLocations() != null) {
            List<LocationEntity> locationEntities = new ArrayList<>();
            for (ELongStaticHotelInfoResp.Image.Location location : elongImage.getLocations()) {
                locationEntities.add(new LocationEntity(location));
            }
            this.location = locationEntities;
        }
    }

    public HotelImageEntity(ELongHotelDetailResp.Hotel.Image elongImage) {
        this.roomId = elongImage.getRoomId();
        this.type = elongImage.getType();
        if (StringUtils.isNotBlank(elongImage.getIsCoverImage())) {
            this.isCoverImage = Boolean.valueOf(elongImage.getIsCoverImage());
        }
        if (StringUtils.isNotBlank(elongImage.getIsRoomCoverImage())) {
            this.isRoomCoverImage = Boolean.valueOf(elongImage.getIsRoomCoverImage());
        }
        if (elongImage.getLocations() != null) {
            List<LocationEntity> locationEntities = new ArrayList<>();
            for (ELongHotelDetailResp.Hotel.Image.Location location : elongImage.getLocations()) {
                locationEntities.add(new LocationEntity(location));
            }
            this.location = locationEntities;
        }
    }

    public HotelImageEntity(SZJLQueryHotelDetailResp.Image image, String hotelId) {
        this.hotelOrigin = HotelOrigin.SZJL;
        this.hotelId = hotelId;
//        image.getImageId();
        if (StringUtils.isNotBlank(image.getType())) {
            Integer globalType = SZJLHotelImageType.getGlobalTypeFromType(Integer.parseInt(image.getType()));
            HotelImageType hotelImageType = HotelImageType.fromType(globalType);
            this.type = hotelImageType.getType();
            this.typeName = hotelImageType.getName();
        }
        if (StringUtils.isNotBlank(image.getRoomTypeIds())) {
            this.roomIds = new ArrayList<>(Arrays.asList(image.getRoomTypeIds().split(",")));
        }
//        image.getRoomTypeIds();
        LocationEntity locationEntity = new LocationEntity();
        locationEntity.setSize(image.getImageSize());
        locationEntity.setUrl(image.getImageUrl());
        int waterMark = 0;
        if (Objects.equals(0, image.getImageLogo())) {
            waterMark = 1;
        }
        locationEntity.setWaterMark(waterMark);
        this.location = new ArrayList<>(List.of(locationEntity));
    }

    /**
     * 获取酒店详情图片列表
     *
     * @param imageEntityList 总图片Entity列表
     * @return Item<String> 酒店详情图片列表
     */
    public static List<String> getHotelDetailImageList(List<HotelImageEntity> imageEntityList) {

        List<String> imageList = new ArrayList<>();
        // 首图
        HotelImageEntity coverImageEntity =
                imageEntityList.stream()
                        .filter(HotelImageEntity::getIsCoverImage)
                        .findFirst()
                        .orElse(null);
        if (coverImageEntity != null) {
            List<LocationEntity> location = coverImageEntity.getLocation();
            String coverImageUrl = getImageUrlBySize(location, 8);
            if (StringUtils.isNotBlank(coverImageUrl)) {
                imageList.add(coverImageUrl);
            }
        }

        // 公共区域
        String publicAreaUrl = getImageUrlByType(imageEntityList, 11);
        if (StringUtils.isNotBlank(publicAreaUrl)) {
            imageList.add(publicAreaUrl);
        }

        // 客房图片
        while (imageList.size() < 4) {
            for (HotelImageEntity imageEntity : imageEntityList) {
                if (StringUtils.isNotBlank(imageEntity.getRoomId()) && imageEntity.getIsRoomCoverImage()) {
                    String curUrl = getImageUrlBySize(imageEntity.getLocation(), 8);
                    imageList.add(curUrl);
                    if (imageList.size() == 4) {
                        return imageList;
                    }
                }
            }
        }
        return imageList;
    }

    public static String getImageUrlByType(List<HotelImageEntity> imageEntityList, Integer type) {

        List<HotelImageEntity> targetTypeList =
                imageEntityList.stream().filter(item -> Objects.equals(item.getType(), type)).toList();
        if (targetTypeList.isEmpty()) {
            return null;
        }

        HotelImageEntity imageEntity = targetTypeList.get(0);
        return getImageUrlBySize(imageEntity.getLocation(), 8);
    }

    public static String getImageUrlBySize(List<LocationEntity> location, Integer size) {

        if (ObjectUtils.isNull(location, size)) {
            return null;
        }

        ObjectMapper objectMapper = new ObjectMapper();
        List<LocationEntity> newList = objectMapper.convertValue(location, new TypeReference<>() {
        });
        return newList.stream()
                .filter(item -> Objects.equals(item.getSize(), size))
                .map(LocationEntity::getUrl)
                .findFirst()
                .orElse(null);
    }

    /**
     * 图片地址节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LocationEntity {

        /**
         * 是否有水印(0-无,1-有。默认为有水印图片)
         */
        private Integer waterMark;

        /**
         * 尺寸类型
         * 包含以下尺寸类型
         * 375x200、350x350、640x960、800x600、1080x800、1140x640
         */
        private Integer size;

        /**
         * 图片地址
         */
        private String url;

        public LocationEntity(ELongStaticHotelInfoResp.Image.Location location) {
            if (location == null) {
                return;
            }
            this.waterMark = location.getWaterMark();
            this.size = location.getSize();
            this.url = location.getUrl();
        }

        public LocationEntity(ELongHotelDetailResp.Hotel.Image.Location location) {
            if (location == null) {
                return;
            }
            this.waterMark = location.getWaterMark() ? 1 : 0;
            this.size = location.getSizeType();
            this.url = location.getUrl();
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }
            LocationEntity that = (LocationEntity) o;
            return Objects.equals(this.url, that.getUrl());
        }

        @Override
        public int hashCode() {
            return Objects.hash(this.url);
        }
    }
}

