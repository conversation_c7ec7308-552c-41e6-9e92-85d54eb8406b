package com.somytrip.entity.hotel;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.somytrip.entity.dto.hotel.HotelFacilityInfos;
import com.somytrip.entity.hotel.NCNB.NCNBBaseEntity;
import com.somytrip.entity.hotel.NCNB.NCNBHotelSearchResp;
import com.somytrip.entity.vo.hotel.HotelVo;
import com.somytrip.utils.CommonConstants;
import com.somytrip.utils.StaticInfoUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @ClassName: HotelThirdPartyEntity
 * @Description:
 * @Author: shadow
 * @Date: 2023/12/28 10:11
 */
@Data
@TableName("hotel_third_party")
public class HotelThirdPartyEntity implements Serializable {

    private static final long SerialVersionUID = 1L;

    /**
     * 酒店id
     */
    private String id;
    /**
     * 酒店来源(NCNB: 捷旅, ELong: 同程, smt: 搜旅)
     */
    private String hotelOrigin;
    /**
     * 酒店名称
     */
    private String hotelName;
    /**
     * 国家名
     */
    private String countryName;
    /**
     * 省份名
     */
    private String provinceName;
    /**
     * 城市名
     */
    private String cityName;
    /**
     * 城市ID
     */
    private String cityId;
    /**
     * 地址
     */
    private String address;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 时间
     */
    private String startBusinessDate;
    /**
     * 最后装修时间
     */
    private String repairDate;
    /**
     * 邮政编码
     */
    private String postCode;
    /**
     * 星级
     */
    private String star;
    /**
     * 经度
     */
    private String lon;
    /**
     * 纬度
     */
    private String lat;
    /**
     * 评分
     */
    private String score;
    /**
     * 介绍
     */
    private String introduction;
    /**
     * 酒店电话
     */
    private String hotelTel;
    /**
     * 酒店图片列表
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<HotelVo.HotelImage> hotelImages = new ArrayList<>();
    /**
     * 酒店设施
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private HotelFacilityInfos facilityInfos = new HotelFacilityInfos();
    ;
    /**
     * 地标数据
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private JSONObject landmarks;

    public HotelThirdPartyEntity(NCNBHotelSearchResp.HotelInfo item) {

        this.id = item.getHotelId();
        this.hotelOrigin = "NCNB";
        this.hotelName = item.getHotelName();
//        this.countryId = item.getCountryId();
        this.countryName = item.getCountryName();
//        this.provinceId = item.getProvinceId();
        this.provinceName = item.getProvinceName();
//        this.cityId = item.getCityId();
        this.cityName = item.getCityName();
        this.address = item.getAddress();
        this.email = item.getEmail();
        this.postCode = item.getPostCode();
        this.startBusinessDate = item.getStartBusinessDate();
        this.repairDate = item.getRepairdate();
        this.star = StaticInfoUtil.getNameByCode(CommonConstants.NCNB_STATIC_STAR, item.getStar());
        this.lon = item.getLon();
        this.lat = item.getLat();
        this.score = item.getScore();
        this.introduction = item.getIntro();
        this.hotelTel = item.getHotelTel();
        if (item.getHotelImages() != null) {
            for (NCNBBaseEntity.HotelImage image : item.getHotelImages()) {
                HotelVo.HotelImage hotelImage = new HotelVo.HotelImage(image.getImageName(), image.getImageUrl());
                this.hotelImages.add(hotelImage);
            }
        }
        if (item.getFacilityInfos() != null && item.getFacilityInfos().getServices() != null) {
            List<HotelFacilityInfos.Service> serviceList = item.getFacilityInfos().getServices().stream()
                    .map(serviceItem ->
                            new HotelFacilityInfos.Service(
                                    serviceItem.getServiceName(),
                                    serviceItem.getGroupId()
                            )
                    )
                    .collect(Collectors.toList());
            this.facilityInfos.setServices(serviceList);
        }
        this.landmarks = JSONObject.from(item.getLandmarks());
    }

    public HotelThirdPartyEntity() {
    }

    public HotelThirdPartyEntity(String id, String hotelOrigin, String hotelName, String countryName, String provinceName, String cityName, String cityId, String address, String email, String startBusinessDate, String repairDate, String postCode, String star, String lon, String lat, String score, String introduction, String hotelTel, List<HotelVo.HotelImage> hotelImages, HotelFacilityInfos facilityInfos, JSONObject landmarks) {
        this.id = id;
        this.hotelOrigin = hotelOrigin;
        this.hotelName = hotelName;
        this.countryName = countryName;
        this.provinceName = provinceName;
        this.cityName = cityName;
        this.cityId = cityId;
        this.address = address;
        this.email = email;
        this.startBusinessDate = startBusinessDate;
        this.repairDate = repairDate;
        this.postCode = postCode;
        this.star = star;
        this.lon = lon;
        this.lat = lat;
        this.score = score;
        this.introduction = introduction;
        this.hotelTel = hotelTel;
        this.hotelImages = hotelImages;
        this.facilityInfos = facilityInfos;
        this.landmarks = landmarks;
    }

    public boolean hasNCNB() {
        return Objects.equals("NCNB", hotelOrigin);
    }

    public boolean hasELong() {
        return Objects.equals("ELong", hotelOrigin);
    }
}
