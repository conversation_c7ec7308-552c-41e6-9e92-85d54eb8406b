package com.somytrip.entity.hotel.ELong;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName: ELongHotelDetailResp
 * @Description: 同程艺龙酒店详情响应
 * @Author: shadow
 * @Date: 2024/3/1 16:23
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ELongHotelDetailResp {

    /**
     * 查询到的酒店总数
     */
    @NotNull
    @JsonProperty("Count")
    private Integer count;

    /**
     * 酒店结果集
     */
    @Nullable
    @JsonProperty("Hotels")
    private List<Hotel> hotels;

    /**
     * 汇率信息
     */
    @Nullable
    @JsonProperty("ExchangeRateList")
    private List<ELongHotelListResp.ExchangeRate> exchangeRateList;

    /**
     * Hotel 节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Hotel {

        /**
         * 酒店编号
         */
        @NotNull
        @JsonProperty("HotelId")
        private String hotelId;

        /**
         * 最低价格
         */
        @NotNull
        @JsonProperty("LowRate")
        private BigDecimal lowRate;

        /**
         * 最低价格的货币
         */
        @Nullable
        @JsonProperty("CurrencyCode")
        private String currencyCode;

        /**
         * 预订规则
         */
        @Nullable
        @JsonProperty("BookingRules")
        private List<ELongHotelListResp.Hotel.BookingRule> bookingRules;

        /**
         * 担保规则
         */
        @Nullable
        @JsonProperty("GuaranteeRules")
        private List<ELongHotelListResp.Hotel.GuaranteeRule> guaranteeRules;

        /**
         * 预付规则
         */
        @Nullable
        @JsonProperty("PrepayRules")
        private List<ELongHotelListResp.Hotel.PrepayRule> prepayRules;

        /**
         * 新担保规则
         */
        @Nullable
        @JsonProperty("GuaranteeRuleExtends")
        private List<ELongHotelListResp.Hotel.GuaranteeRuleExtend> guaranteeRuleExtends;

        /**
         * 新预付规则
         */
        @Nullable
        @JsonProperty("PrepayRuleExtends")
        private List<ELongHotelListResp.Hotel.PrepayRuleExtend> prepayRuleExtends;

        /**
         * 增值服务
         */
        @Nullable
        @JsonProperty("ValueAdds")
        private List<ELongHotelListResp.Hotel.ValueAdd> valueAdds;

        /**
         * 促销规则
         */
        @Nullable
        @JsonProperty("DrrRules")
        private List<ELongHotelListResp.Hotel.DrrRule> drrRules;

        /**
         * 酒店设施
         */
        @Nullable
        @JsonProperty("Facilities")
        private String facilities;

        /**
         * 房型列表
         */
        @Nullable
        @JsonProperty("Rooms")
        private List<Room> rooms;

        /**
         * 酒店信息
         */
        @Nullable
        @JsonProperty("Detail")
        private Detail detail;

        /**
         * 酒店图片
         */
        @Nullable
        @JsonProperty("Images")
        private List<Image> images;

        /**
         * 送礼活动
         */
        @Nullable
        @JsonProperty("Gifts")
        private List<ELongHotelListResp.Hotel.Gift> gifts;

        /**
         * 礼包套餐
         */
        @Nullable
        @JsonProperty("GiftPackages")
        private List<ELongHotelListResp.Hotel.GiftPackage> giftPackages;

        /**
         * 酒店特殊信息提示
         */
        @Nullable
        @JsonProperty("HAvailPolicys")
        private List<ELongHotelListResp.Hotel.HAvailPolicy> hAvailPolicys;

        /**
         * 好评分数
         */
        @Nullable
        @JsonProperty("praiseScore")
        private BigDecimal praiseScore;

        @Nullable
        @JsonProperty("Distance")
        private Integer distance;

        /**
         * Room 节点
         */
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class Room {

            /**
             * 展示房型编号
             */
            @NotNull
            @JsonProperty("RoomId")
            private String roomId;

            /**
             * 房间英文名
             */
            @NotNull
            @JsonProperty("NameEn")
            private String nameEn;

            /**
             * 房型名称
             */
            @NotNull
            @JsonProperty("Name")
            private String name;

            /**
             * 产品信息
             */
            @Nullable
            @JsonProperty("RatePlans")
            private List<ELongHotelListResp.Hotel.Room.RatePlan> ratePlans;

            /**
             * 图片地址
             */
            @Nullable
            @JsonProperty("ImageUrl")
            private String imageUrl;

            /**
             * 楼层
             */
            @Nullable
            @JsonProperty("Floor")
            private String floor;

            /**
             * 上网情况
             */
            @Nullable
            @JsonProperty("Broadnet")
            private String broadnet;

            /**
             * 床型
             */
            @Nullable
            @JsonProperty("BedType")
            private String bedType;

            /**
             * 床型描述
             */
            @Nullable
            @JsonProperty("BedDesc")
            private String bedDesc;

            /**
             * 房间描述
             */
            @Nullable
            @JsonProperty("Description")
            private String description;

            /**
             * 房间备注
             */
            @Nullable
            @JsonProperty("Comments")
            private String comments;

            /**
             * 面积
             */
            @Nullable
            @JsonProperty("Area")
            private String area;

            /**
             * 可容纳人数
             */
            @Nullable
            @JsonProperty("Capcity")
            private String capacity;

            /**
             * 窗户类型ID
             */
            @Nullable
            @JsonProperty("WindowTypeId")
            private Integer windowTypeId;

            /**
             * 窗户类型描述
             */
            @Nullable
            @JsonProperty("WindosType")
            private String windowType;
        }

        /**
         * Detail 节点
         */
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class Detail {

            /**
             * 酒店名称
             */
            @NotNull
            @JsonProperty("HotelName")
            private String hotelName;

            /**
             * 挂牌星级
             */
            @NotNull
            @JsonProperty("StarRate")
            private Integer starRate;

            /**
             * 艺龙推荐级别
             */
            @NotNull
            @JsonProperty("Category")
            private Integer category;

            /**
             * 纬度
             */
            @Nullable
            @JsonProperty("Latitude")
            private String latitude;

            /**
             * 经度
             */
            @Nullable
            @JsonProperty("Longitude")
            private String longitude;

            /**
             * 地址
             */
            @Nullable
            @JsonProperty("Address")
            private String address;

            /**
             * 前台电话
             */
            @Nullable
            @JsonProperty("Phone")
            private String phone;

            /**
             * 封面图片
             */
            @Nullable
            @JsonProperty("ThumbNailUrl")
            private String thumbnailUrl;

            /**
             * 城市ID
             */
            @Nullable
            @JsonProperty("City")
            private String city;

            /**
             * 城市名称
             */
            @Nullable
            @JsonProperty("CityName")
            private String cityName;

            /**
             * 行政区ID
             */
            @Nullable
            @JsonProperty("District")
            private String district;

            /**
             * 行政区名称
             */
            @Nullable
            @JsonProperty("DistrictName")
            private String districtName;

            /**
             * 商业区ID
             */
            @Nullable
            @JsonProperty("BusinessZone")
            private String businessZone;

            /**
             * 商业区名称
             */
            @Nullable
            @JsonProperty("BusinessZoneName")
            private String businessZoneName;

            /**
             * 评价
             */
            @Nullable
            @JsonProperty("Review")
            private ELongHotelListResp.Hotel.Detail.Review review;

            /**
             * 特色介绍
             */
            @Nullable
            @JsonProperty("Features")
            private String features;

            /**
             * 设施服务
             */
            @Nullable
            @JsonProperty("GeneralAmenities")
            private String generalAmenities;

            /**
             * 交通状况
             */
            @Nullable
            @JsonProperty("Traffic")
            private String traffic;

            /**
             * 酒店描述
             */
            @Nullable
            @JsonProperty("Description")
            private String description;

            /**
             * 酒店英文名
             */
            @Nullable
            @JsonProperty("HotelNameEn")
            private String hotelNameEn;

            /**
             * 英文地址
             */
            @Nullable
            @JsonProperty("AddressEn")
            private String addressEn;

            /**
             * 到店时间
             */
            @Nullable
            @JsonProperty("CheckInTime")
            private String checkInTime;

            /**
             * 离店时间
             */
            @Nullable
            @JsonProperty("CheckOutTime")
            private String checkOutTime;

            /**
             * 国家名称
             */
            @Nullable
            @JsonProperty("HotelCountryName")
            private String hotelCountryName;

            /**
             * 国家编码
             */
            @Nullable
            @JsonProperty("HotelCountryCode")
            private String hotelCountryCode;

            /**
             * 国家id
             */
            @NotNull
            @JsonProperty("HotelCountryId")
            private Integer hotelCountryId;

            /**
             * 酒店政策
             */
            @Nullable
            @JsonProperty("HotelPolicy")
            private String hotelPolicy;
        }

        /**
         * Image 节点
         */
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class Image {

            /**
             * 图片类型
             * 1 - 餐厅 (Restaurant)
             * 2 - 休闲 (Recreation Facilities)
             * 3 - 会议室 (Meeting/Conference)
             * 5 - 外观 (Exterior)
             * 6 - 大堂/接待台   (Lobby/ Reception)
             * 8 - 客房 (Guest Room)
             * 10 - 其他 (Other Facilities)
             * 11 - 公共区域 (Public Area)
             * 12 - 周边景点 (Nearby Attractions)
             */
            @NotNull
            @JsonProperty("Type")
            private Integer type;

            /**
             * 是否是酒店封面
             */
            @Nullable
            @JsonProperty("IsCoverImage")
            private String isCoverImage;

            /**
             * 是否为房型封面
             */
            @Nullable
            @JsonProperty("IsRoomCoverImage")
            private String isRoomCoverImage;

            /**
             * 房型编号
             */
            @NotNull
            @JsonProperty("RoomId")
            private String roomId;

            /**
             * 图片地址
             */
            @Nullable
            @JsonProperty("Locations")
            private List<Location> locations;

            /**
             * Location 节点
             */
            @Data
            @NoArgsConstructor
            @AllArgsConstructor
            public static class Location {

                /**
                 * 图片规格
                 * 1：jpg图片，固定长边350，固定长边350缩放图片(用于详情页图片展示)
                 * 7：png图片，固定长边640放缩图片
                 * 8：1080x800规格的图片
                 * 9：1140x640规格的图片
                 * 10：800x600规格的图片
                 * 11：960x640规格的图片
                 * 12：375x200规格的图片
                 */
                @NotNull
                @JsonProperty("SizeType")
                private Integer sizeType;

                /**
                 * 是否有水印
                 * 默认为有水印图片
                 */
                @NotNull
                @JsonProperty("WaterMark")
                private Boolean waterMark;

                /**
                 * 图片地址
                 */
                @NotNull
                @JsonProperty("Url")
                private String url;
            }
        }
    }
}
