package com.somytrip.entity.hotel.ELong;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;

/**
 * @ClassName: ELongHotelIncrShardingIdResp
 * @Description: 同程艺龙酒店分片增量编号响应
 * @Author: shadow
 * @Date: 2024/2/22 16:08
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ELongHotelIncrShardingIdResp {

    /**
     * 最后的更新ID
     */
    @NotNull
    @JsonProperty("LastId")
    private Long lastId;
}
