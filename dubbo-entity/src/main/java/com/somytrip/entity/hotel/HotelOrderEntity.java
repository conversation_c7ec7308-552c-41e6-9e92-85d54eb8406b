package com.somytrip.entity.hotel;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.somytrip.entity.dto.hotel.*;
import com.somytrip.entity.enums.hotel.HotelOrigin;
import com.somytrip.entity.vo.hotel.CancelPolicyVo;
import com.somytrip.entity.vo.hotel.RoomListVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName: HotelOrderEntity
 * @Description: 酒店订单entity
 * @Author: shadow
 * @Date: 2023/11/29 14:28
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class HotelOrderEntity {

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单序列号
     */
    private String orderSn;

    /**
     * 第三方订单ID
     */
    private String thirdPartyOrderId;

    /**
     * 用户ID
     */
    private Long uid;

    /**
     * 酒店确认号
     */
    private String hotelConfirmationNo;

    /**
     * 实际支付金额(单位: 分)
     */
    private Integer payAmount;

    /**
     * 原始金额(单位: 分)
     */
    private Integer originalAmount;

    /**
     * 实际金额(单位根据currency字段)
     * 废弃
     */
    @Deprecated
    private BigDecimal realPrice;

    /**
     * 真实支付价格
     * 2.1新增
     */
    private BigDecimal realPayPrice;

    /**
     * 真实原始价格
     * 2.1新增
     */
    private BigDecimal realOriginalPrice;

//    /**
//     * 每晚价格
//     */
//    private BigDecimal perNightPrice;

    /**
     * 订单状态
     */
    private Integer orderStatus;

    /**
     * 订单名称(酒店名)
     */
    private String orderName;

    /**
     * 酒店来源
     */
    @Deprecated
    private String orderOrigin;

    /**
     * 酒店来源(2.1新增)
     */
    private HotelOrigin hotelOrigin;

    /**
     * 订单酒店信息
     */
    private OrderHotelInfo hotelInfo;

    /**
     * 币种
     */
    private String currency = "CNY";

    /**
     * 房间信息列表
     */
    private OrderRoomInfo roomInfo;

    /**
     * 产品ID
     */
    private String ratePlanId;

    /**
     * 产品
     */
    private RoomListVo.RoomListRatePlanVo ratePlan;

    /**
     * 原始每晚价格
     */
    private List<String> originalNightlyRates;

    /**
     * 住客数
     */
    private Integer numberOfCustomers;

    /**
     * 膳食信息
     */
    private OrderBoardInfo orderBoardInfo;

    /**
     * 入住时间区间
     * 废弃
     */
    @Deprecated
    private HotelDateRange dateRange;

    /**
     * 订单入住时间区间
     * 2.1新增
     */
//    private HotelDateRangeV2 orderDateRange;
    private HotelOrderDateRange orderDateRange;

    /**
     * 预定人信息(废弃)
     */
    @Deprecated
    private HotelBookInfo bookInfo;

    /**
     * 入住人信息(废弃, 使用 customers属性)
     */
    @Deprecated
    private List<OrderGuestInfo> guestInfos;

    /**
     * 联系人信息(2.1新增)
     */
    private ContactDto contact;

    /**
     * 入住人信息(2.1新增)
     */
    private List<CustomerDto> customers;

    /**
     * 特殊要求
     */
    private String specialRemark = "";

    /**
     * 国籍(国际酒店用)
     */
    private String nationality = "";

    /**
     * 取消政策
     * 废弃
     */
    @Deprecated
    private List<HotelCancelPolicy> cancelPolicyList;

    /**
     * 取消政策
     * 2.1 新增
     */
    private CancelPolicyVo cancelPolicy;

    /**
     * 订单创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime createTime;

    /**
     * 订单更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime updateTime;

    /**
     * 订单支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime paymentTime;

    /**
     * 酒店确认时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime confirmTime;

    /**
     * 入住时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime checkInTime;

    /**
     * 离店时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime checkOutTime;

    /**
     * 订单完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime completeTime;

    /**
     * 订单评论时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime evaluationTime;

    /**
     * 订单取消时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime cancelTime;

    /**
     * 订单删除时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime deleteTime;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HotelOrderDateRange {

        /**
         * 入住时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd")
        @JsonSerialize(using = LocalDateSerializer.class)
        @JsonDeserialize(using = LocalDateDeserializer.class)
        private LocalDate checkIn;

        /**
         * 退房时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd")
        @JsonSerialize(using = LocalDateSerializer.class)
        @JsonDeserialize(using = LocalDateDeserializer.class)
        private LocalDate checkOut;

        /**
         * 最早到店时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
        @JsonSerialize(using = LocalDateTimeSerializer.class)
        @JsonDeserialize(using = LocalDateTimeDeserializer.class)
        private LocalDateTime earliestArrivalTime;

        /**
         * 最晚到点时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
        @JsonSerialize(using = LocalDateTimeSerializer.class)
        @JsonDeserialize(using = LocalDateTimeDeserializer.class)
        private LocalDateTime latestArrivalTime;

        public HotelOrderDateRange(HotelDateRangeV2 hotelDateRangeV2) {
            this.checkIn = hotelDateRangeV2.getCheckIn();
            this.checkOut = hotelDateRangeV2.getCheckOut();
            this.earliestArrivalTime = hotelDateRangeV2.getEarliestArrivalTime();
            this.latestArrivalTime = hotelDateRangeV2.getLatestArrivalTime();
        }
    }
}
