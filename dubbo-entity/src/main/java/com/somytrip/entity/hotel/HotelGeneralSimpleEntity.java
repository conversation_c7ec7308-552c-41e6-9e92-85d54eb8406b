package com.somytrip.entity.hotel;

import com.somytrip.entity.enums.hotel.HotelOrigin;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.hotel
 * @className: HotelGeneralSimplEntity
 * @author: shadow
 * @description: 酒店General简略信息Entity
 * @date: 2024/4/19 11:03
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HotelGeneralSimpleEntity {

    /**
     * 酒店来源
     */
    private HotelOrigin hotelOrigin;

    /**
     * 酒店ID
     */
    private String hotelId;

    public HotelGeneralSimpleEntity(HotelGeneralEntity generalEntity) {
        hotelOrigin = generalEntity.getHotelOrigin();
        hotelId = generalEntity.getHotelId();
    }
}