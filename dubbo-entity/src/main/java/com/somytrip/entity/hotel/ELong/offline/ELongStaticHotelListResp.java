package com.somytrip.entity.hotel.ELong.offline;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName: ELongStaticHotelResp
 * @Description: 同程艺龙静态酒店响应
 * @Author: shadow
 * @Date: 2024/1/27 10:52
 */
@Data
public class ELongStaticHotelListResp {

    /**
     * 酒店数量
     */
    @JsonProperty("Count")
    private Integer count;

    /**
     * 酒店列表
     */
    @JsonProperty("Hotels")
    private List<Hotel> hotels;

    @Data
    public static class Hotel {

        /**
         * 酒店ID
         */
        @JsonProperty("HotelId")
        private String hotelId;

        /**
         * 酒店中文名称
         */
        @JsonProperty("HotelName")
        private String hotelName;

        /**
         * 酒店英文名称
         */
        @JsonProperty("HotelNameEn")
        private String hotelNameEn;

        /**
         * 酒店状态(0:有效 1:无效 2:删除)
         */
        @JsonProperty("HotelStatus")
        private Integer hotelStatus;

        /**
         * 酒店更新
         * 0:酒店基础数据，1:房型数据，2:图片数据，4:供应商数据，
         * 5:酒店点评、评分数据(hotel.static.grade)，多个用“;”隔开，以最后一次更新为主
         */
        @JsonProperty("Modification")
        private String modification;

        /**
         * 酒店数据更新时间
         */
        @JsonProperty("UpdateTime")
        private LocalDateTime updateTime;
    }
}
