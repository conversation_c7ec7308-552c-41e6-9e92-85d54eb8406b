package com.somytrip.entity.hotel.ELong.offline;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.somytrip.entity.enums.hotel.*;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName: ELongStaticHotelInfoResp
 * @Description: 同程艺龙静态酒店信息响应体
 * @Author: shadow
 * @Date: 2024/1/27 11:55
 */
@Data
public class ELongStaticHotelInfoResp {

    /**
     * 详情
     */
    @JsonProperty("Detail")
    private Detail detail;

    /**
     * 供应商列表
     */
    @JsonProperty("Suppliers")
    private List<Supplier> suppliers;

    /**
     * 房间列表
     */
    @JsonProperty("Rooms")
    private List<Room> rooms;

    /**
     * 图片列表
     */
    @JsonProperty("Images")
    private List<Image> images;

    /**
     * 详情节点
     */
    @Data
    public static class Detail {

        private String globalCityCode;

        /**
         * 酒店ID
         */
        @JsonProperty("HotelId")
        private String hotelId;

        /**
         * 酒店中文名称
         */
        @JsonProperty("HotelName")
        private String hotelName;

        /**
         * 酒店英文名称
         */
        @JsonProperty("HotelNameEn")
        private String hotelNameEn;

        /**
         * 酒店中文曾用名
         */
        @JsonProperty("HotelUsedName")
        private String hotelUsedName;

        /**
         * 酒店英文曾用名
         */
        @JsonProperty("HotelUsedNameEn")
        private String hotelUsedNameEn;

        /**
         * 酒店当地中文名
         */
        @JsonProperty("HotelNameLocal")
        private String hotelNameLocal;

        /**
         * 酒店当地英文名
         */
        @JsonProperty("HotelNameLocalEn")
        private String hotelNameLocalEn;

        /**
         * 状态(0:有效 1:无效 2:删除)
         */
        @JsonProperty("HotelStatus")
        private Integer hotelStatus;

        /**
         * 酒店中文简称
         */
        @JsonProperty("ShortName")
        private String shortName;

        /**
         * 酒店英文简称
         */
        @JsonProperty("ShortNameEn")
        private String shortNameEn;

        /**
         * 酒店中文地址
         */
        @JsonProperty("Address")
        private String address;

        /**
         * 酒店英文地址
         */
        @JsonProperty("AddressEn")
        private String addressEn;

        /**
         * 邮编
         */
        @JsonProperty("PostalCode")
        private String postalCode;

        /**
         * 挂牌星级(0-无星级；1-一星级；2-二星级；3-三星级；4-四星级；5-五星级。当为0时对外显示可用Category的值，但请进行图标区分)
         */
        @JsonProperty("StarRate")
        private Integer starRate;

        /**
         * 艺龙推荐星级 (0,1,2：客栈；3：舒适；4：高档；5：豪华。如果StarRate和Category都为空或0，可展示暂无数据)
         */
        @JsonProperty("Category")
        private Integer category;

        /**
         * 电话
         */
        @JsonProperty("Phone")
        private String phone;

        /**
         * 传真
         */
        @JsonProperty("Fax")
        private String fax;

        /**
         * 酒店邮箱地址
         */
        @JsonProperty("Email")
        private String email;

        /**
         * 酒店时区
         */
        @JsonProperty("Timezone")
        private String timezone;

        /**
         * 营业执照
         */
        @JsonProperty("Licenses")
        private List<String> licenses;

        /**
         * 开业时间
         */
        @JsonProperty("EstablishmentDate")
        private String establishmentDate;

        /**
         * 装修时间
         */
        @JsonProperty("RenovationDate")
        private String renovationDate;

        /**
         * 客房总数量
         */
        @JsonProperty("RoomTotalAmount")
        private Integer roomTotalAmount;

        /**
         * 集团编号
         */
        @JsonProperty("GroupId")
        private String groupId;

        /**
         * 集团中文名称
         */
        @JsonProperty("GroupName")
        private String groupName;

        /**
         * 集团英文名称
         */
        @JsonProperty("GroupNameEn")
        private String groupNameEn;

        /**
         * 品牌编号
         */
        @JsonProperty("BrandId")
        private String brandId;

        /**
         * 品牌中文名称
         */
        @JsonProperty("BrandName")
        private String brandName;

        /**
         * 品牌英文名称
         */
        @JsonProperty("BrandNameEn")
        private String brandNameEn;

        /**
         * 是否经济型
         */
        @JsonProperty("IsEconomic")
        private Integer isEconomic;

        /**
         * 是否是公寓
         */
        @JsonProperty("IsApartment")
        private Integer isApartment;

        /**
         * 入住时间
         */
        @JsonProperty("ArrivalTime")
        private String arrivalTime;

        /**
         * 离店时间
         */
        @JsonProperty("DepartureTime")
        private String departureTime;

        /**
         * Google纬度
         */
        @JsonProperty("GoogleLat")
        private BigDecimal googleLat;

        /**
         * Google经度
         */
        @JsonProperty("GoogleLon")
        private BigDecimal googleLon;

        /**
         * Baidu纬度
         */
        @JsonProperty("BaiduLat")
        private BigDecimal baiduLat;

        /**
         * Baidu经度
         */
        @JsonProperty("BaiduLon")
        private BigDecimal baiduLon;

        /**
         * 国家Id
         */
        @JsonProperty("CountryId")
        private String countryId;

        /**
         * 国家中文名
         */
        @JsonProperty("CountryName")
        private String countryName;

        /**
         * 国家英文名
         */
        @JsonProperty("CountryNameEn")
        private String countryNameEn;

        /**
         * 主城市Id
         */
        @JsonProperty("CityId")
        private String cityId;

        /**
         * 城市中文名
         */
        @JsonProperty("CityName")
        private String cityName;

        /**
         * 城市英文名
         */
        @JsonProperty("CityNameEn")
        private String cityNameEn;

        /**
         * 关联城市
         */
        @JsonProperty("CityId2")
        private String cityId2;

        /**
         * 行政区Id
         */
        @JsonProperty("District")
        private String district;

        /**
         * 行政区中文名称
         */
        @JsonProperty("DistrictName")
        private String districtName;

        /**
         * 行政区英文名称
         */
        @JsonProperty("DistrictNameEn")
        private String districtNameEn;

        /**
         * 主商圈Id
         */
        @JsonProperty("BusinessZone")
        private String businessZone;

        /**
         * 主商圈中文名称
         */
        @JsonProperty("BusinessZoneName")
        private String businessZoneName;

        /**
         * 主商圈英文名称
         */
        @JsonProperty("BusinessZoneNameEn")
        private String businessZoneNameEn;

        /**
         * 附属商圈Id
         */
        @JsonProperty("BusinessZone2")
        private String businessZone2;

        /**
         * 附属商圈中文名
         */
        @JsonProperty("BusinessZone2Name")
        private String businessZone2Name;

        /**
         * 附属商圈英文名
         */
        @JsonProperty("BusinessZone2NameEn")
        private String businessZone2NameEn;

        /**
         * 酒店支持的信用卡
         */
        @JsonProperty("CreditCards")
        private String creditCards;

        /**
         * 酒店支持的信用卡英文
         */
        @JsonProperty("CreditCardsEn")
        private String creditCardsEn;

        /**
         * 是否允许返现
         */
        @JsonProperty("HasCoupon")
        private Boolean hasCoupon;

        /**
         * 酒店中文简介
         */
        @JsonProperty("IntroEditor")
        private String introEditor;

        /**
         * 英文简介
         */
        @JsonProperty("IntroEditorEn")
        private String introEditorEn;

        /**
         * 中文描述
         */
        @JsonProperty("Description")
        private String description;

        /**
         * 英文描述
         */
        @JsonProperty("DescriptionEn")
        private String descriptionEn;

        /**
         * 中文接机服务
         */
        @JsonProperty("AirportPickUpService")
        private String airportPickUpService;

        /**
         * 英文接机服务
         */
        @JsonProperty("AirportPickUpServiceEn")
        private String airportPickUpServiceEn;

        /**
         * 酒店基础设施列表
         */
        @JsonProperty("GeneralFacilities")
        private List<Facility> generalFacilities;

        /**
         * 酒店休闲设施列表
         */
        @JsonProperty("RecreationFacilities")
        private List<Facility> recreationFacilities;

        /**
         * 酒店服务设施列表
         */
        @JsonProperty("ServiceFacilities")
        private List<Facility> serviceFacilities;

        /**
         * 预订须知列表
         */
        @JsonProperty("BookingNoticeFacilities")
        private List<Facility> bookingNoticeFacilities;

        /**
         * 酒店主题列表
         */
        @JsonProperty("Themes")
        private List<Theme> themes;

        /**
         * 酒店类型
         */
        @JsonProperty("HotelTypes")
        private List<HotelType> hotelTypes;

        /**
         * 酒店服务指数
         */
        @JsonProperty("ServiceRank")
        private ServiceRank serviceRank;

        /**
         * 停车信息
         */
        @JsonProperty("ParkInfos")
        private List<ParkInfo> parkInfos;

        /**
         * 电话信息
         */
        @JsonProperty("TelList")
        private List<Tel> telList;

        /**
         * 宠物政策
         */
        @JsonProperty("PetPolicy")
        private String petPolicy;

        /**
         * 新预定政策
         */
        @JsonProperty("Notices")
        private List<Notice> notices;

        /**
         * 押金政策
         */
        @JsonProperty("DepositPolicy")
        private DepositPolicy depositPolicy;

        /**
         * 入住方式
         */
        @JsonProperty("CheckinPolicies")
        private List<CheckinPolicy> checkinPolicies;

        /**
         * 住宿规定
         */
        @JsonProperty("StayPolicy")
        private StayPolicy stayPolicy;

        /**
         * 新V2设施
         */
        @JsonProperty("FacilityV2")
        private List<FacilityType> facilityV2;

        /**
         * 主题节点
         */
        @Data
        public static class Theme {

            /**
             * 主题ID
             */
            @JsonProperty("ThemeId")
            private String themeId;

            /**
             * 主题中文名称
             */
            @JsonProperty("ThemeName")
            private String themeName;

            /**
             * 主题英文名称
             */
            @JsonProperty("ThemeNameEn")
            private String themeNameEn;
        }

        /**
         * 酒店类型节点
         */
        @Data
        public static class HotelType {

            /**
             * 酒店类型ID
             */
            @JsonProperty("HotelTypeId")
            private String hotelTypeId;

            /**
             * 酒店类型中文名称
             */
            @JsonProperty("HotelTypeName")
            private String hotelTypeName;

            /**
             * 酒店类型英文名称
             */
            @JsonProperty("HotelTypeNameEn")
            private String hotelTypeNameEn;
        }

        /**
         * 酒店服务指数节点
         */
        @Data
        public static class ServiceRank {

            /**
             * 酒店服务总评分(满分为5分)
             */
            @JsonProperty("SummaryScore")
            private String summaryScore;
        }

        /**
         * 停车信息节点
         */
        @Data
        public static class ParkInfo {

            /**
             * 名称
             */
            @JsonProperty("title")
            private String title;

            /**
             * 描述
             */
            @JsonProperty("desc")
            private List<String> desc;

            /**
             * 类型
             */
            @JsonProperty("type")
            private String type;
        }

        /**
         * 电话信息节点
         */
        @Data
        public static class Tel {

            /**
             * 国家码
             */
            @JsonProperty("nationCode")
            private String nationCode;

            /**
             * 区域码
             */
            @JsonProperty("areaCode")
            private String areaCode;

            /**
             * 主机号
             */
            @JsonProperty("mainCode")
            private String mainCode;

            /**
             * 分机号
             */
            @JsonProperty("extCode")
            private String extCode;

            /**
             * 类型(1:座机 2:手机号)
             */
            @JsonProperty("type")
            private String type;
        }

        /**
         * 消息通知节点
         */
        @Data
        public static class Notice {

            /**
             * 分类(Hotel:酒店维度 City:城市维度)
             */
            @JsonProperty("Category")
            private String category;

            /**
             * 开始日期
             */
            @JsonProperty("Start")
//            private LocalDate start;
            private String start;

            /**
             * 结束日期
             */
            @JsonProperty("End")
//            private LocalDate end;
            private String end;

            /**
             * 中文文本
             */
            @JsonProperty("Text")
            private String text;

            /**
             * 英文文本
             */
            @JsonProperty("TextEn")
            private String textEn;
        }

        /**
         * 押金政策节点
         */
        @Data
        public static class DepositPolicy {

            /**
             * 是否收取押金(1:是, 0:否, null:未知)
             */
            @JsonProperty("DepositSwitch")
            private Integer depositSwitch;

            /**
             * 收取频次(1:固定金额，2:每间，3:每晚)
             */
            @JsonProperty("Frequency")
            private Integer frequency;

            /**
             * 收取金额
             */
            @JsonProperty("Amount")
            private BigDecimal amount;

            /**
             * 押金支付方式(1:现金，2:信用卡，3:借记卡，4:第三方平台)
             */
            @JsonProperty("PayType")
            private List<Integer> payType;

            /**
             * 押金退还方式(0:不原路退还，1:原路退还)
             */
            @JsonProperty("RefundType")
            private List<Integer> refundType;

            /**
             * 押金退还时间(0:当日退还，1:一周内退还，2:两周内退还)
             */
            @JsonProperty("RefundTime")
            private Integer refundTime;

            /**
             * 押金币种(默认RMB)
             */
            @JsonProperty("Currency")
            private String currency;
        }

        /**
         * 入住方式节点
         */
        @Data
        public static class CheckinPolicy {

            /**
             * 入住方式
             * frontdesk:请到前台领取钥匙/门卡
             * reception:住宿方会有专人等侯迎接
             * password:住宿方会提供住宿的进门密码
             * keybox:住宿方会将钥匙存放于隐蔽处，并会在你入住前提供详细说明
             * keyhide:住宿方会将钥匙存在保管箱内，并会在你入住前提供详细说明
             * instruction:住宿方会在你入住前提供详细说明
             * contactus:【注】本酒店/民宿务必提前联系，确认入住事宜，否则可能影响入住
             * other:其他
             */
            @JsonProperty("CheckInWay")
            private String checkinWay;

            /**
             * 入住地址
             */
            @JsonProperty("CheckInAddress")
            private String checkinAddress;

            /**
             * 入住地址英文
             */
            @JsonProperty("CheckInAddressEn")
            private String checkinAddressEn;

            /**
             * 入住方式描述
             */
            @JsonProperty("CheckInNote")
            private String checkinNote;

            /**
             * 入住方式描述英文
             */
            @JsonProperty("CheckInNoteEn")
            private String checkinNoteEn;
        }

        /**
         * 住宿规定节点
         */
        @Data
        public static class StayPolicy {

            /**
             * 安静时间
             */
            @JsonProperty("QuietTime")
            private List<String> quiteTime;

            /**
             * 是否允许派对
             */
            @JsonProperty("PartyAllowed")
            private String partyAllowed;

            /**
             * 是否允许拍照
             */
            @JsonProperty("PhotoAllowed")
            private String photoAllowed;
        }
    }

    /**
     * 供应商
     */
    @Data
    public static class Supplier {

        /**
         * 供应商ID
         */
        @JsonProperty("SupplierID")
        private String supplierId;

        /**
         * 对应供应商编码
         */
        @JsonProperty("HotelCode")
        private String hotelCode;

        /**
         * 星期开始设置
         * 用于房价的周末价计算。
         * 为0表示周末设置从周一开始
         */
        @JsonProperty("WeekendStart")
        private Integer weekendStart;

        /**
         * 星期结束设置
         * 为0表示到周日结束，但是两个都为0表示无周末设置； 如果开始为3，结束为1，表示从周三到下周1都是周末设置
         * 1代表周一，7代表周日
         */
        @JsonProperty("WeekendEnd")
        private Integer weekendEnd;

        /**
         * 即时确认的销售房型
         * 多个房型以逗号分隔
         */
        @JsonProperty("InstantRoomTypes")
        private String instantRoomTypes;

        /**
         * 供应商有效状态
         * 是否有效；无效的供应商关联的产品和库存不能销售
         */
        @JsonProperty("Status")
        private Integer status;

        /**
         * 酒店使用库存和价格的方式
         * DATA： 使用离线数据接口和搜索接口均可获取到该酒店；
         * SEARCH：只有搜索接口能获取到该酒店。
         * 为空时默认为DATA（该字段已废弃）
         */
        @JsonProperty("InvokeType")
        private String invokeType;

        /**
         * 特殊政策
         * 请把此信息展示给用户，以便用户预订。
         */
        @JsonProperty("AvailPolicy")
        private AvailPolicy availPolicy;

        /**
         * 温馨提示
         */
        @JsonProperty("HelpfulTip")
        private HelpfulTip helpfulTip;

        /**
         * 特殊政策节点
         */
        @Data
        public static class AvailPolicy {

            /**
             * 特殊政策开始日期
             * 格式：yyyy-MM-dd'T'HH:mm:ss'+08:00'；例如：2021-09-15T00:00:00+08:00
             */
            @JsonProperty("StartDate")
            private String startDate;

            /**
             * 特殊政策结束日期
             * 格式：yyyy-MM-dd'T'HH:mm:ss'+08:00'；例如：2021-09-15T00:00:00+08:00
             */
            @JsonProperty("EndDate")
            private String endDate;

            /**
             * 特殊政策中文描述
             * 例如：此酒店不能接待外宾；
             * 例如：1、客人延住请发延住单，不要发新订单;2、此酒店无停车场。
             */
            @JsonProperty("Description")
            private String description;

            /**
             * 特殊政策英文描述
             */
            @JsonProperty("DescriptionEn")
            private String descriptionEn;
        }

        /**
         * 温馨提示节点
         */
        @Data
        public static class HelpfulTip {

            /**
             * 温馨提示开始日期
             */
            @JsonProperty("StartDate")
            private String startDate;

            /**
             * 温馨提示结束日期
             */
            @JsonProperty("EndDate")
            private String endDate;

            /**
             * 温馨提示中文描述
             */
            @JsonProperty("Description")
            private String description;

            /**
             * 温馨提示英文描述
             */
            @JsonProperty("DescriptionEn")
            private String descriptionEn;
        }
    }

    /**
     * 房型节点
     */
    @Data
    public static class Room {

        /**
         * 房型ID
         */
        @JsonProperty("RoomID")
        private String roomId;

        /**
         * 房型中文名称
         */
        @JsonProperty("RoomName")
        private String roomName;

        /**
         * 房型英文名称
         */
        @JsonProperty("RoomNameEn")
        private String roomNameEn;

        /**
         * 房型面积
         */
        @JsonProperty("Area")
        private String area;

        /**
         * 楼层
         */
        @JsonProperty("Floor")
        private String floor;

        /**
         * 上网情况
         * 0表示无宽带，1 表示有宽带, 2 表示有WIFI   国际酒店不存在、国内酒店存在
         */
        @JsonProperty("BroadnetAccess")
        private Integer broadnetAccess;

        /**
         * 上网费用
         * 0表示免费，1 表示收费 2未知  国际酒店不存在、国内酒店存在
         */
        @JsonProperty("BroadnetFee")
        private Integer broadnetFee;

        /**
         * 床型中文名称
         */
        @JsonProperty("BedType")
        private String bedType;

        /**
         * 床型英文名称
         */
        @JsonProperty("BedTypeEn")
        private String bedTypeEn;

        /**
         * 中文描述
         */
        @JsonProperty("Description")
        private String description;

        /**
         * 英文描述
         */
        @JsonProperty("DescriptionEn")
        private String descriptionEn;

        /**
         * 中文备注
         */
        @JsonProperty("Comments")
        private String comments;

        /**
         * 英文备注
         */
        @JsonProperty("CommentsEn")
        private String commentsEn;

        /**
         * 房间最大入住人数
         * 如没有提供请根据房间名称判断：单人间或有单字的为1人，三人间的为3人，其他的默认2人；7表示6人以上
         */
        @JsonProperty("Capacity")
        private Integer capacity;

        /**
         * 房型数量
         */
        @JsonProperty("Amount")
        private Integer amount;

        /**
         * 房间设施列表
         */
        @JsonProperty("Facilities")
        private List<Facility> facilities;

        /**
         * 新床型信息
         */
        @JsonProperty("RoomBed")
        private RoomBed roomBed;

        /**
         * 新房间设施
         */
        @JsonProperty("FacilityV2")
        private List<FacilityType> facilityV2;

        /**
         * 户型类型
         * 民宿特有  bed:床位 true:独立单间 false:整套
         */
        @JsonProperty("Sharing")
        private String sharing;

        /**
         * 户型
         * 几室几厅几卫
         */
        @JsonProperty("RoomTypeSummary")
        private String roomTypeSummary;

        /**
         * 床型节点
         */
        @Data
        public static class RoomBed {

            /**
             * 床型信息
             */
            @JsonProperty(("RoomBedInfo"))
            private List<RoomBedInfo> roomBedInfo;

            /**
             * 床型信息节点
             */
            @Data
            public static class RoomBedInfo {

                /**
                 * 卧室床信息
                 */
                @JsonProperty("RoomDetailBeds")
                private List<RoomBedDetail> roomDetailBeds;

                /**
                 * 客厅床信息
                 */
                @JsonProperty("LivingRoomBeds")
                private List<RoomBedDetail> livingRoomBeds;

                /**
                 * 床信息节点
                 */
                @Data
                public static class RoomBedDetail {

                    /**
                     * 或分组
                     * BedGroups链表的每个元素之间为“或”的关系
                     */
                    @JsonProperty("BedGroups")
                    private List<BedGroup> bedGroups;

                    /**
                     * 房间号
                     */
                    @JsonProperty("RoomIndex")
                    private Integer roomIndex;

                    /**
                     * 或分组节点
                     */
                    @Data
                    public static class BedGroup {

                        /**
                         * 和分组
                         * BedInfos链表的每个元素之间为“和”的关系
                         */
                        @JsonProperty("BedInfos")
                        private List<BedInfo> bedInfos;

                        @Data
                        public static class BedInfo {

                            /**
                             * 床型ID
                             */
                            @JsonProperty("BedTypeId")
                            private Integer bedTypeId;

                            /**
                             * 床宽
                             */
                            @JsonProperty("BedWidth")
                            private Double bedWidth;

                            /**
                             * 床的数量
                             */
                            @JsonProperty("BedCount")
                            private Integer bedCount;

                            /**
                             * 床的名称
                             */
                            @JsonProperty("BedName")
                            private String bedName;

                            /**
                             * 备注
                             */
                            @JsonProperty("Remark")
                            private String remark;
                        }
                    }
                }
            }
        }
    }

    /**
     * 图片节点
     */
    @Data
    public static class Image {

        /**
         * 关联的房间ID
         */
        @JsonProperty("RoomID")
        private String roomId;

        /**
         * 图片类型
         * 1 - 餐厅 (Restaurant)
         * 2 - 休闲 (Recreation Facilities)
         * 3 - 会议室 (Meeting/Conference)
         * 5 - 外观 (Exterior)
         * 6 - 大堂/接待台   (Lobby/ Reception)
         * 8 - 客房 (Guest Room)
         * 10 - 其他 (Other Facilities)
         * 11 - 公共区域 (Public Area)
         * 12 - 周边景点 (Nearby Attractions)
         */
        @JsonProperty("Type")
        private Integer type;

        /**
         * 图片类型中文名
         */
        @JsonProperty("TypeName")
        private String typeName;

        /**
         * 图片类型英文名
         */
        @JsonProperty("TypeNameEn")
        private String typeNameEn;

        /**
         * 图片来源
         * 由用户上传，或者酒店上传，Hotel - 酒店；User - 用户
         */
        @JsonProperty("AuthorType")
        private String authorType;

        /**
         * 是否是主图
         * 当值为true的时候，表示这个图片是主图(封面图)，可用于显示在列表中
         */
        @JsonProperty("IsCoverImage")
        private Boolean isCoverImage;

        /**
         * 是否为房间主图
         * 当值为true的时候，表示这个图片是房间主图，可用于显示在列表中
         */
        @JsonProperty("IsRoomCoverImage")
        private Boolean isRoomCoverImage;

        /**
         * 图片地址列表
         */
        @JsonProperty("Locations")
        private List<Location> locations;

        /**
         * 图片地址节点
         */
        @Data
        public static class Location {

            /**
             * 是否有水印(0-无,1-有。默认为有水印图片)
             */
            @JsonProperty("WaterMark")
            private Integer waterMark;

            /**
             * 尺寸类型
             * 包含以下尺寸类型
             * 375x200、350x350、640x960、800x600、1080x800、1140x640
             */
            @JsonProperty("Size")
            private Integer size;

            /**
             * 图片地址
             */
            @JsonProperty("Url")
            private String url;
        }
    }

    /**
     * 设施v2节点
     */
    @Data
    public static class FacilityType {

        /**
         * 设施分类ID
         */
        @JsonProperty("FacilityTypeId")
        private Long facilityTypeId;

        /**
         * 设施分类名称
         */
        @JsonProperty("FacilityTypeName")
        private String facilityTypeName;

        /**
         * 设施信息
         */
        @JsonProperty("FacilityInfoList")
        private List<FacilityInfo> facilityInfoList;

        /**
         * 设施信息节点
         */
        @Data
        public static class FacilityInfo {

            /**
             * 设施ID
             */
            @JsonProperty("FacilityId")
            private Long facilityId;

            /**
             * 设施名称
             */
            @JsonProperty("FacilityName")
            private String facilityName;

            /**
             * 设施收费信息
             */
            @JsonProperty("FeeInfo")
            private List<FeeInfo> feeInfo;

            /**
             * 设施营业时间
             */
            @JsonProperty("BusinessHourInfos")
            private List<BusinessHourInfo> businessHourInfos;

            /**
             * 设施年龄限制信息
             */
            @JsonProperty("AgeLimitInfo")
            private AgeLimitInfo ageLimitInfo;

            /**
             * 设施预约信息
             */
            @JsonProperty("ReservationInfo")
            private ReservationInfo reservationInfo;

            /**
             * 设施收费信息节点
             */
            @Data
            public static class FeeInfo {

                /**
                 * 设施收费类型(Paid：收费；Free：免费；None：未知)
                 */
                @EnumValue
                @JsonProperty("FeeChargeType")
                private FeeChargeType feeChargeType;

                /**
                 * 设施收费明细
                 */
                @JsonProperty("FeeDetail")
                private List<FeeDetail> feeDetail;

                /**
                 * 设施收费明细节点
                 */
                @Data
                public static class FeeDetail {

                    /**
                     * 设施费用金额
                     */
                    @JsonProperty("Amount")
                    private BigDecimal amount;

                    /**
                     * 设施费用币种
                     */
                    @JsonProperty("Currency")
                    private String currency;

                    /**
                     * 设施费用类型
                     * Time：次数；Minute：分钟；Quarter：一刻钟；HalfHour：半小时；Hour：小时；Day：天；
                     * Week：周；Person：人；Bed：床；Car：车；Bottle：瓶
                     */
                    @JsonProperty("FeeTimeType")
                    private FeeTimeType feeTimeType;
                }
            }

            /**
             * 设施营业时间节点
             */
            @Data
            public static class BusinessHourInfo {

                /**
                 * 设施营业时间类型(OpenDay：开放时间；CloseDay:关闭时间)
                 */
                @JsonProperty("OpenDayType")
                private OpenDayType openDayType;

                /**
                 * 设施营业时间开始时间(HH:MM)
                 */
                @JsonProperty("StartTime")
                private String startTime;

                /**
                 * 设施营业时间结束时间(HH:MM)
                 */
                @JsonProperty("EndTime")
                private String endTime;

                /**
                 * 设施营业时间周有效
                 * 适用星期几，从周一到周日 （如1110110表示周四、周日无效）
                 */
                @JsonProperty("WeeklyIndex")
                private String weeklyIndex;
            }

            @Data
            public static class AgeLimitInfo {

                /**
                 * 设施使用最小年龄
                 */
                @JsonProperty("MinAge")
                private String minAge;

                /**
                 * 设施使用最大年龄
                 */
                @JsonProperty("MaxAge")
                private String maxAge;
            }

            @Data
            public static class ReservationInfo {

                /**
                 * 设施预约限制(T：需要预约；F：无需预约)
                 */
                @JsonProperty("Reserve")
                private Reserve reserve;

                /**
                 * 提前预约时间
                 */
                @JsonProperty("Time")
                private String time;

                /**
                 * 提前预约时间单位(Dia：天；Hora：小时；Minuto：分钟)
                 */
                @JsonProperty("TimeUnit")
                private TimeUnit timeUnit;
            }
        }
    }

    /**
     * 设施节点
     */
    @Data
    public static class Facility {

        /**
         * 设施ID
         */
        @JsonProperty("FacilityId")
        private String facilityId;

        /**
         * 设施中文名称
         */
        @JsonProperty("FacilityName")
        private String facilityName;

        /**
         * 设施英文名称
         */
        @JsonProperty("FacilityNameEn")
        private String facilityNameEn;
    }
}
