package com.somytrip.entity.hotel.WEBBEDS;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName: WEBBEDSPrebookResp
 * @Description: WEBBEDS验价响应
 * @Author: shadow
 * @Date: 2024/3/19 4:42
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WEBBEDSPrebookResp {

    /**
     * 验价ID 30分钟内有效  传入下单接口
     */
    @JsonProperty("prebook_id")
    private String prebookId;

    /**
     * 币种
     */
    private String currency;

    /**
     * 总金额
     */
    @JsonProperty("total_amount")
    private Long totalAmount;

    /**
     * 注意事项
     */
    private List<WEBBEDSHotelsResp.Hotel.Notice> notices;

    /**
     * 取消政策
     */
    @JsonProperty("cancel_policies")
    private List<WEBBEDSBookResp.CancelPolicy> cancelPolicies;

    /**
     * 每日价格
     */
    @JsonProperty("daily_prices")
    private List<WEBBEDSPricesResp.PriceItem.DailyPrice> dailyPrices;
}
