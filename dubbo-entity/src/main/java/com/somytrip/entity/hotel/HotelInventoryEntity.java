package com.somytrip.entity.hotel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.somytrip.entity.enums.hotel.HotelOrigin;
import com.somytrip.entity.hotel.ELong.offline.ELongHotelDataInventoryResp;
import com.somytrip.utils.LocalDateTimeUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * (HotelInventory)实体类
 *
 * <AUTHOR>
 * @since 2024-02-20 11:32:44
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("hotel_inventory")
public class HotelInventoryEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 874628605875816394L;

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 酒店来源
     */
    private HotelOrigin hotelOrigin;
    /**
     * 酒店ID
     */
    private String hotelId;
    /**
     * 房型ID
     */
    private String roomTypeId;
    /**
     * 酒店编码
     */
    private String hotelCode;
    /**
     * 库存时间
     */
    private LocalDate date;
    /**
     * 库存状态，False-不可用 True-可用
     */
    private Boolean status;
    /**
     * 库存数量
     */
    private Integer amount;
    /**
     * 超售状态，0---可超售，1—不可超售
     */
    private Integer overBooking;
    /**
     * 可用开始日期
     */
    private LocalDate startDate;
    /**
     * 可用结束日期
     */
    private LocalDate endDate;
    /**
     * 可用开始时间
     */
    private LocalTime startTime;
    /**
     * 可用结束时间
     */
    private LocalTime endTime;
    /**
     * 库存是否支持及时确认
     */
    private Boolean isInstantConfirm;
    /**
     * 及时确认可用开始时间
     */
    private LocalTime icBeginTime;
    /**
     * 及时确认可用结束时间
     */
    private LocalTime icEndTime;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public HotelInventoryEntity(ELongHotelDataInventoryResp.Inventory elongInventory) {
        this.hotelOrigin = HotelOrigin.ELong;
        this.hotelId = elongInventory.getHotelId();
        this.roomTypeId = elongInventory.getRoomTypeId();
        this.hotelCode = elongInventory.getHotelCode();
        this.date = LocalDateTimeUtil.convertISOStr2LocalDate(elongInventory.getDate());
        this.status = elongInventory.getStatus();
        this.amount = elongInventory.getAmount();
        this.overBooking = elongInventory.getOverBooking();
        this.startDate = LocalDateTimeUtil.convertISOStr2LocalDate(elongInventory.getStartDate());
        this.endDate = LocalDateTimeUtil.convertISOStr2LocalDate(elongInventory.getEndDate());
        this.startTime = LocalTime.parse(elongInventory.getStartTime());
        this.endTime = LocalTime.parse(elongInventory.getEndTime());
        this.isInstantConfirm = elongInventory.getIsInstantConfirm();
        if (elongInventory.getIc_beginTime() != null) {
            this.icBeginTime = LocalTime.parse(elongInventory.getIc_beginTime());
        }
        if (elongInventory.getIc_EndTime() != null) {
            this.icEndTime = LocalTime.parse(elongInventory.getIc_EndTime());
        }
    }
}

