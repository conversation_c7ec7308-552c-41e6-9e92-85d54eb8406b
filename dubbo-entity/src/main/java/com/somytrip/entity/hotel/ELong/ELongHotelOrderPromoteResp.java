package com.somytrip.entity.hotel.ELong;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.Nullable;

/**
 * @ClassName: ELongHotelOrderPromoteResp
 * @Description: 同程艺龙酒店订单催确认响应
 * @Author: shadow
 * @Date: 2024/2/24 15:18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ELongHotelOrderPromoteResp {

    /**
     * 调整后的反馈时间点
     * 格式：hh:mm (如果早于当前时间则表示第二天的时间点)
     */
    @Nullable
    @JsonProperty("AdjustTime")
    private String adjustTime;
}
