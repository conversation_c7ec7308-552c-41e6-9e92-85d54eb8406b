package com.somytrip.entity.hotel.ELong;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @ClassName: ELongPublicRequestData
 * @Description: 同程艺龙公共请求Data参数
 * @Author: shadow
 * @Date: 2024/1/25 14:59
 */
@Data
public class ELongPublicRequestData<T> {


    @JsonProperty("Version")
    private BigDecimal version;

    /**
     * en_US,zh_CN
     */
    @JsonProperty("Local")
    private String local;

    /**
     * 请求参数
     */
    @JsonProperty("Request")
    private T request;
}
