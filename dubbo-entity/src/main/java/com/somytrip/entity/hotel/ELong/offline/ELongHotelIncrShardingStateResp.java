package com.somytrip.entity.hotel.ELong.offline;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.List;

/**
 * @ClassName: ELongHotelIncrShardingStateResp
 * @Description: 同程艺龙分片状态增量响应
 * @Author: shadow
 * @Date: 2024/2/22 15:58
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ELongHotelIncrShardingStateResp {

    /**
     * 变化合集
     */
    @Nullable
    @JsonProperty("States")
    private List<State> states;

    /**
     * State节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class State {

        /**
         * 增量ID
         */
        @NotNull
        @JsonProperty("LastId")
        private Long lastId;

        /**
         * 变化时间
         */
        @NotNull
        @JsonProperty("Time")
        private String time;

        /**
         * 酒店编号
         */
        @Nullable
        @JsonProperty("HotelId")
        private String hotelId;

        /**
         * 酒店供应商编码
         */
        @Nullable
        @JsonProperty("HotelCode")
        private String hotelCode;

        /**
         * 展示房型ID
         */
        @Nullable
        @JsonProperty("RoomId")
        private String roomId;

        /**
         * 销售房型编号
         */
        @Nullable
        @JsonProperty("RoomTypeId")
        private String roomTypeId;

        /**
         * 产品编号
         */
        @Nullable
        @JsonProperty("RatePlanId")
        private Integer ratePlanId;

        /**
         * 对象的名称
         */
        @Nullable
        @JsonProperty("Name")
        private String name;

        /**
         * 有效状态
         * HotelId：酒店
         * HotelCode：酒店供应商
         * RoomId：展示房型
         * RoomTypeId：销售房型
         * RatePlanId：产品
         * RatePlanPolicy：担保或预付规则
         */
        @NotNull
        @JsonProperty("StateType")
        private String stateType;

        /**
         * 变化类型
         * 当StateType为RatePlanPolicy的时候Status无意义
         */
        @Nullable
        @JsonProperty("Status")
        private Boolean status;

        /**
         * DCSupplierRelationID
         */
        @Nullable
        @JsonProperty("DCSupplierRelationID")
        private String dcSupplierRelationId;
    }
}
