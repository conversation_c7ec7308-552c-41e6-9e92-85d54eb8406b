package com.somytrip.entity.hotel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.somytrip.entity.enums.hotel.HotelOrigin;
import com.somytrip.entity.hotel.ELong.offline.ELongStaticHotelInfoResp;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 酒店住宿规定表(HotelStayPolicy)实体类
 *
 * <AUTHOR>
 * @since 2024-02-02 10:38:28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "hotel_stay_policy", autoResultMap = true)
public class HotelStayPolicyEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 684217567017981239L;

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 酒店来源
     */
    private HotelOrigin hotelOrigin;
    /**
     * 酒店ID
     */
    private String hotelId;
    /**
     * 酒店主键ID
     */
    private Long hotelPid;
    /**
     * 安静时间
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> quiteTime;
    /**
     * 是否允许派对
     */
    private String partyAllowed;
    /**
     * 是否允许拍照
     */
    private String photoAllowed;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public HotelStayPolicyEntity(ELongStaticHotelInfoResp.Detail.StayPolicy stayPolicy, String hotelId) {

        if (stayPolicy == null) {
            return;
        }
        this.hotelOrigin = HotelOrigin.ELong;
        this.hotelId = hotelId;
        this.quiteTime = stayPolicy.getQuiteTime();
        this.partyAllowed = stayPolicy.getPartyAllowed();
        this.photoAllowed = stayPolicy.getPhotoAllowed();
    }
}

