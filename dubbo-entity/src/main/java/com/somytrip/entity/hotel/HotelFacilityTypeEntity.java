package com.somytrip.entity.hotel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.somytrip.entity.enums.hotel.HotelOrigin;
import com.somytrip.entity.hotel.ELong.offline.ELongStaticHotelInfoResp;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Map;

/**
 * 酒店设施类型表(HotelFacilityTypes)实体类
 *
 * <AUTHOR>
 * @since 2024-02-02 10:48:45
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "hotel_facility_types", autoResultMap = true)
public class HotelFacilityTypeEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = -70761687594712277L;

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 酒店来源
     */
    private HotelOrigin hotelOrigin;
    /**
     * 设施分类
     */
    private Long facilityTypeId;
    /**
     * 设施分类名称
     */
    private String facilityTypeName;
    /**
     * 图标ID
     */
    private String iconId;
    /**
     * 国际化字典
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Map<String, String>> localeDict;

    public HotelFacilityTypeEntity(ELongStaticHotelInfoResp.FacilityType facilityType) {
        this.hotelOrigin = HotelOrigin.ELong;
        this.facilityTypeId = facilityType.getFacilityTypeId();
        this.facilityTypeName = facilityType.getFacilityTypeName();
    }
}

