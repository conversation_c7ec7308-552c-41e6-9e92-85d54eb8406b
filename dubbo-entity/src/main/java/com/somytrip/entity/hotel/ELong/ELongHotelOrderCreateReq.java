package com.somytrip.entity.hotel.ELong;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.somytrip.entity.enums.hotel.*;
import com.somytrip.entity.enums.hotel.idcardtype.IdCardType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName: ELongHotelOrderCreateReq
 * @Description: 同程艺龙酒店创建订单请求参数
 * @Author: shadow
 * @Date: 2024/2/23 14:58
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ELongHotelOrderCreateReq {

    /**
     * 合作伙伴订单确认号
     * 合作伙伴订单号。不同的订单这个值不一样，同一个订单发送多次请求也须用同一个值。
     * 如果这个值相同，即使之后的HotelId、入住人、联系人等所有的信息都不同，那么也不会创建新单，而是返回上一次成单的订单号。
     * 多次发送请求间隔需要在45秒以上。
     */
    @NotNull
    @JsonProperty("AffiliateConfirmationId")
    private String affiliateConfirmationId;

    /**
     * 酒店编号
     */
    @NotNull
    @JsonProperty("HotelId")
    private String hotelId;

    /**
     * 展示房型编号
     * 允许为空，当传入时会校验房型编号绑定关系
     */
    @Nullable
    @JsonProperty("RoomId")
    private String roomId;

    /**
     * 销售房型编号
     * 注意区分RoomId和RoomTypeId
     * 如果为实时搜索模式，对应的是hotel.list、hotel.detail接口中的RatePlan.RoomTypeId
     */
    @JsonProperty("RoomTypeId")
    private String roomTypeId;

    /**
     * 产品编号
     */
    @JsonProperty("RatePlanId")
    private Integer ratePlanId;

    /**
     * 入住日期
     * 如果是凌晨入住，那么入住日期是前一天, 使用yyyy-MM-dd格式，例如:2022-12-09
     */
    @JsonProperty("ArrivalDate")
    private LocalDate arrivalDate;

    /**
     * 离店日期
     * 使用yyyy-MM-dd格式，例如:2022-12-09
     */
    @JsonProperty("DepartureDate")
    private LocalDate departureDate;

    /**
     * 钟点房入住开始时间
     * 当成单的产品为钟点房(小时房)时，需要入参该字段，格式为yyyy-MM-dd HH:mm:ss，且应符合钟点房的开始结束时间；
     */
    @JsonProperty("HourRoomStartTime")
    private LocalDateTime hourRoomStartTime;

    /**
     * 钟点房入住结束时间
     * 当成单的产品为钟点房(小时房)时，需要入参该字段，格式为yyyy-MM-dd HH:mm:ss，且应符合钟点房的开始结束时间；
     */
    @JsonProperty("HourRoomEndTime")
    private LocalDateTime hourRoomEndTime;

    /**
     * 客人类型
     */
    @JsonProperty("CustomerType")
    private CustomerType customerType;

    /**
     * 付款类型
     * SelfPay-前台现付、Prepay-预付
     */
    @JsonProperty("PaymentType")
    private PaymentType paymentType;

    /**
     * 房间数量
     * 有几个房间，对应OrderRoom数组节点数量，每一订单房间数量不要超过7间（小于8）
     */
    @JsonProperty("NumberOfRooms")
    private Integer numberOfRooms;

    /**
     * 客人数量
     * 客人数量需要大于等于房间数量
     */
    @JsonProperty("NumberOfCustomers")
    private Integer numberOfCustomers;

    /**
     * 最早到店时间
     * 1、两者都是必填字段,可让用户选择两个时间点，也可以只让客人选择最晚到店时间，系统根据下面的规则计算出最早到店时间。
     * 2、最早到店时间范围：入住日7:00(建议14:00,因一般酒店接待开始时间是14点)-23:59；最晚到店时间范围：入住日7:00-23:59和次日1:00-6:00; 都必须是整点或半点或23:59。
     * 3、最早到店时间须晚于当前时间, 最晚到店时间须晚于最早到店时间，一般相差3个小时。
     * 4、如果客人凌晨预订凌晨入住，则入住的是前一天的房间，须设置入住日期为前一天，最早到店时间为入住日期的23:59，最晚到店时间为入住日期的第二天的06:00。
     * 5、最早到店和最晚到店时间是完整的日期时间类型，如2017-03-16 13:06:30，不要只传入时间13:06:30，否则会报错。
     */
    @JsonProperty("EarliestArrivalTime")
    private LocalDateTime earliestArrivalTime;

    /**
     * 最晚到店时间
     * 同上
     */
    @JsonProperty("LatestArrivalTime")
    private LocalDateTime latestArrivalTime;

    /**
     * 货币类型
     * 和获取价格的地方保持一致
     */
    @JsonProperty("CurrencyCode")
    private String currencyCode;

    /**
     * 总价
     * 原币种价格
     * RatePlan的TotalRate * 房间数,
     * 开通了结算价的分销商，此处应该传入结算价
     */
    @JsonProperty("TotalPrice")
    private BigDecimal totalPrice;

    /**
     * 客人访问IP
     * ip格式，如 ***************；请提供真实的客人IP，将更好控制恶意订单
     */
    @JsonProperty("CustomerIPAddress")
    private String customerIpAddress;

    /**
     * 是否已担保或已付款
     * 开通了公司担保业务的合作伙伴才能使用该属性。当贵公司确定对客人扣款成功后，才可以设置为true，否则设置为false
     */
    @JsonProperty("IsGuaranteeOrCharged")
    private Boolean isGuaranteeOrCharged;

    /**
     * 确认类型
     * NotAllowedConfirm -- 不发送确认短信，合作伙伴自查订单状态后自行联系客人，确认短信模板可参考艺龙确认短信模板示例
     * SMS_cn -- 艺龙发确认短信给客人,订单确认失败的时候会主动联系
     * NoNeed -- 艺龙发确认短信给客人,订单确认失败的时候不主动联系
     * 注：除了NotAllowedConfirm，其余的选项艺龙会发送确认短信；下单时如果输入了邮箱那么都会发送邮件
     */
    @JsonProperty("ConfirmationType")
    private OrderConfirmationType confirmationType;

    /**
     * 给酒店备注
     * 客人给酒店的备注，尽量不填写，以免影响房间确认速度，并可减少投诉；若必须要填，尽量对要求进行规范，便于沟通和处理。
     * 特别注意：目前艺龙不支持传入该字段，即使传入也会忽略掉
     */
    @JsonProperty("NoteToHotel")
    private String noteToHotel;

    /**
     * 给艺龙备注
     */
    @JsonProperty("NoteToElong")
    private String noteToElong;

    /**
     * 是否需要发票
     * 前台现付订单发票由酒店开具；预付订单的发票有两种开具方式：收款方开具或酒店开具。收款方开具发票的，艺龙可提供代开服务，需联系商务开通相关权限。
     */
    @JsonProperty("IsNeedInvoice")
    private Boolean isNeedInvoice;

    /**
     * 客人信息
     */
    @JsonProperty("OrderRooms")
    private List<OrderRoom> orderRooms;

    /**
     * 发票信息
     * 当IsNeedInvoice为true时填写。
     * 将由普通快递投递
     */
    @JsonProperty("Invoice")
    private Invoice invoice;

    /**
     * 联系人
     */
    @JsonProperty("Contact")
    private ELongHotelOrderDetailResp.Contact contact;

    /**
     * 信用卡
     * 担保订单和预付订单才须传信用卡。
     * 如果IsCreateOrderOnly或IsGuaranteeOrCharged为true，则一定不能传信用卡信息。
     */
    @JsonProperty("CreditCard")
    private CreditCard creditCard;

    /**
     * 第三方支付
     * 担保订单和预付订单才须传第三方支付信息。
     * 如果IsCreateOrderOnly为true或IsGuaranteeOrCharged为false，则一定不能传第三方支付信息。
     */
    @JsonProperty("DoveCorpCard")
    private DoveCorpCard doveCorpCard;

    /**
     * 扩展字段
     */
    @JsonProperty("ExtendInfo")
    private ExtendInfo extendInfo;

    /**
     * 仅创建订单
     * true - 表示本次请求只创建订单，不提供支付信息；订单创建成功后，请求者再通过hotel.order.pay提供支付信息；
     * false - 订单信息和支付一起传入
     */
    @JsonProperty("IsCreateOrderOnly")
    private Boolean isCreateOrderOnly;

    /**
     * 销售给客人的最终价格
     * 需透传用户真实卖价，酒店开票产品或结算价分销商的限价产品需将卖价传入
     */
    @JsonProperty("CustomerPrice")
    private BigDecimal customerPrice;

    /**
     * 订单数据校验
     */
    @JsonProperty("OrderValidation")
    private OrderValidation orderValidation;

    /**
     * 促销信息
     */
    @JsonProperty("Coupon")
    private CouponInfo coupon;

    /**
     * 马甲Id
     * 从hotel.detail接口获取
     * 搜索模式所有产品必传
     */
    @JsonProperty("LittleMajiaId")
    private String littleMajiaId;

    /**
     * 商品唯一标示
     * 从hotel.detail接口获取
     * 搜索模式所有产品必传
     */
    @JsonProperty("GoodsUniqId")
    private String goodsUniqId;

    /**
     * 特殊要求
     * 国际特有字段
     */
    @JsonProperty("SpecificRemark")
    private String specificRemark;

    /**
     * 儿童年龄
     * 国际特有字段
     */
    @JsonProperty("ChildAges")
    private List<Integer> childAges;

    /**
     * 成人数
     * 国际特有字段
     */
    @JsonProperty("NumberOfAdults")
    private Integer numberOfAdults;

    /**
     * 酒店code
     * 国际特有字段
     */
    @JsonProperty("HotelCode")
    private String hotelCode;

    /**
     * 供应商id
     * 国际特有字段
     */
    @JsonProperty("SupplierId")
    private String supplierId;

    /**
     * 二级供应商id
     * 国际特有字段
     */
    @JsonProperty("SubSupplierId")
    private String subSupplierId;

    /**
     * 商品库shopperid
     * 国际特有字段
     */
    @JsonProperty("ShopperProductId")
    private String shopperProductId;

    /**
     * 用户信息加密选项
     * 0：无需加密，1：对称加密
     * 传1时，需要对入参中的以下字段进行加密:
     * Customers节点Name字段
     * Contact节点Name，Mobile，Email
     * Invoice节点的Title，TaxPayerNum，TaxRegisterBank，RegisterBankNum，RegisterAddress，RegisterPhoneNum
     * Recipient节点：Province，City，District，Street，Name，phone，Email
     * 加密方式同信用卡
     */
    @JsonProperty("EncryptOption")
    private Integer encryptOption;

    /**
     * 每日价
     * 每日价透传：用于每日金额校验，避免出现订单部分退艺龙与合作方退款金额不一致现象发生。
     * DayPriceList节点里每个DayPrice里的Price之和 * NumberOfRooms = TotalPrice
     */
    @JsonProperty("DayPriceList")
    private List<DayPrice> dayPriceList;

    /**
     * OrderRoom节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OrderRoom {

        /**
         * 入住人信息
         * 每个房间支持传入多个入住人信息，
         */
        @NotNull
        @JsonProperty("Customers")
        private List<Customer> customers;

        /**
         * Customer
         */
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class Customer {

            /**
             * 姓名
             * 禁止使用“小姐”、“先生”、“女士”、名人姓名、污秽词语，中文姓名不得出现任何汉字外的字符,
             * 当客人输入拼音或英文姓名时，应给与明确的提示，在下方展示红色提示文案，
             * 类似：英文姓和名用“/”隔开。并且要做强校验，没有斜杠时，无法提交订单。
             */
            @NotNull
            @JsonProperty("Name")
            private String name;

            /**
             * 性别
             * Female  女，Maile 男, Unknown 保密
             */
            @NotNull
            @JsonProperty("Gender")
            private Gender gender = Gender.Unknown;

            /**
             * 国籍
             * 有些酒店在预订规则BookingRule中规定需要提供国籍；填写具体的国籍,如中国、日本、美国、USA等
             */
            @NotNull
            @JsonProperty("Nationality")
            private String nationality;

            /**
             * 证件号
             * 需判断产品中【Identification】字段属性，按校验要求传对应身份信息；可加密，加密方式和信用卡节点证件号码字段相同
             */
            @Nullable
            @JsonProperty("IdCardNo")
            private String idCardNo;

            /**
             * 证件类型
             * IdentityCard 身份证，
             * Passport 护照，
             * HomeVisitingCertificate 回乡证，
             * TaiwanCompatriotCertificate 台胞证，
             * HongkongMacaoTaiwanResidencePermit 港澳台居民居住证
             */
            @Nullable
            @JsonProperty("IdCardType")
            private IdCardType idCardType;

            /**
             * 名
             * 国际特有字段
             */
            @Nullable
            @JsonProperty("FirstName")
            private String firstName;

            /**
             * 姓
             * 国际特有字段
             */
            @Nullable
            @JsonProperty("LastName")
            private String lastName;
        }
    }

    /**
     * Invoice节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Invoice {

        /**
         * 发票类型
         */
        @NotNull
        @JsonProperty("InvoiceType")
        private InvoiceType invoiceType;

        /**
         * 抬头类型
         */
        @NotNull
        @JsonProperty("TitleType")
        private TitleType titleType;

        /**
         * 抬头
         */
        @NotNull
        @JsonProperty("Title")
        private String title;

        /**
         * 纳税人识别号/统一社会信用代码
         * 字符长度是15、18或20位，是数字和字母的组合或纯数字
         */
        @NotNull
        @JsonProperty("ITIN")
        private String ITIN;

        /**
         * 发票内容
         * 代订房费或代订住宿费
         */
        @NotNull
        @JsonProperty("ItemName")
        private String itemName;

        /**
         * 金额
         * 人民币价格
         */
        @NotNull
        @JsonProperty("Amount")
        private BigDecimal amount;

        /**
         * 收件人
         */
        @NotNull
        @JsonProperty("Recipient")
        private Recipient recipient;

        /**
         * 是否添加发票备注
         * true-在发票备注栏中添加酒店预订信息（酒店名称、入住日期、离店日期、房间数），false-不添加，默认值
         */
        @Nullable
        @JsonProperty("IsNeedRelationOrder")
        private Boolean isNeedRelationOrder;

        /**
         * 纳税人识别号
         * 专票必传
         */
        @Nullable
        @JsonProperty("TaxPayerNum")
        private String taxPayerNum;

        /**
         * 开户银行
         * 专票必传
         */
        @Nullable
        @JsonProperty("TaxRegisterBank")
        private String taxRegisterBank;

        /**
         * 银行账号
         * 专票必传
         */
        @Nullable
        @JsonProperty("RegisterBankNum")
        private String registerBankNum;

        /**
         * 注册地址
         * 专票必传
         */
        @Nullable
        @JsonProperty("RegisterAddress")
        private String registerAddress;

        /**
         * 电话
         * 专票必传
         */
        @Nullable
        @JsonProperty("RegisterPhoneNum")
        private String registerPhoneNum;

        /**
         * Recipient节点
         */
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class Recipient {

            /**
             * 省份
             * 接口中无规范定义，按照国家邮政规范填写即可。电子发票无需填写。
             */
            @NotNull
            @JsonProperty("Province")
            private String province;

            /**
             * 城市
             * 同上
             */
            @NotNull
            @JsonProperty("City")
            private String city;

            /**
             * 行政区
             * 同上
             */
            @NotNull
            @JsonProperty("District")
            private String district;

            /**
             * 街道
             * 同上
             */
            @NotNull
            @JsonProperty("Street")
            private String street;

            /**
             * 邮编
             * 同上
             */
            @Nullable
            @JsonProperty("PostalCode")
            private String postalCode;

            /**
             * 收件人
             * 同上
             */
            @NotNull
            @JsonProperty("Name")
            private String name;

            /**
             * 电话
             */
            @NotNull
            @JsonProperty("Phone")
            private String phone;

            /**
             * Email
             * 电子发票时该字段必须填写
             */
            @Nullable
            @JsonProperty("Email")
            private String email;
        }
    }

    /**
     * CreditCard节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CreditCard {

        /**
         * 卡号
         * 注意：CreditCard节点下的字段对大小写敏感
         * 1、如果是担保订单，则Creditcard下的所有字段都必须赋有效值
         * 2、测试环境信用卡卡号可填****************
         * 3、如是身份证号码，请填写有效身份证信息，艺龙系统有对应身份证号的校验
         * 4、当需要VerifyCode（CVV）需要先调用信用卡验证接口确认该信用卡是否需要提供CVV；如果卡需要CVV没有提供CVV会拒绝成单
         * 5、卡号加密方式（必须）
         * 加密方法：使用DES对称加密算法（cbc模式，key值和iv值一致）
         * 加密内容=当前时间戳+#+信用卡号
         * 加密密钥：账号appkey的后8位
         * 例如：
         * $CreditCardNO=  des_encrypt(time(). '#****************', substr($appkey,-8));
         * 测试加密方法 Xcrypt::encrypt( '12345#6789012345', '12345678') == 8e519cf90bf4240f7f653af ff4f6d658f5e402a4ff2581a7
         * Java版加密算法参考Java信用卡加密
         * 注：信用卡仅支持国内开通的银联卡，具体以接口返回为准。
         */
        @NotNull
        @JsonProperty("Number")
        private String number;

        /**
         * CVV
         * 需加密，加密方式同卡号
         * 注：信用卡支付新流程CVV必传，不需要根据common.creditcard.validate方法返回IsNeedVerifyCode字段判定是否需要CVV。
         */
        @Nullable
        @JsonProperty("CVV")
        private String cvv;

        /**
         * 有效期-年
         */
        @Nullable
        @JsonProperty("ExpirationYear")
        private Integer expirationYear;

        /**
         * 有效期-月
         */
        @Nullable
        @JsonProperty("ExpirationMonth")
        private Integer expirationMonth;

        /**
         * 持卡人
         * 中文姓名，不含空格，需加密，加密方式同卡号
         */
        @Nullable
        @JsonProperty("HolderName")
        private String holderName;

        /**
         * 证件类型
         * Enum
         * 身份证 IdentityCard，
         * 护照 Passport，
         * 其他 Other
         * 注：当前接口只支持身份证，需要身份证开户的银行卡保持一致。
         */
        @Nullable
        @JsonProperty("IdType")
        private String idType;

        /**
         * 证件号码
         * 需加密，加密方式同卡号
         */
        @Nullable
        @JsonProperty("IdNo")
        private String idNo;

        /**
         * 手机
         * 需加密，加密方式同卡号
         * 信用卡支付新流程使用且是必填项。
         * 需要与信用卡绑卡手机号一致，否则会订单支付失败。
         */
        @Nullable
        @JsonProperty("Mobile")
        private String mobile;
    }

    /**
     * DoveCorpCard节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DoveCorpCard {

        /**
         * 卡号
         * 注意：DoveCorpCard 节点下的字段对大小写敏感
         * 卡号加密方式（必须）
         * 加密方法：使用 DES 对称加密算法（cbc 模式，md5Key 值和 iv 值一致）
         * 加密内容 = 当前时间戳 + "#" + 信用卡号
         * 加密密钥：账号 appkey 的后 8 位
         * 例如：
         * $DoveCorpCardNO = des_encrypt(time(). '#****************', substr($appkey,-8));
         * 测试加密方法 Xcrypt::encrypt( '12345#6789012345', '12345678') == 8e519cf90bf4240f7f653af ff4f6d658f5e402a4ff2581a7
         * Java 版加密算法参考第三方支付加密同 Java 信用卡加密
         */
        @NotNull
        @JsonProperty("Number")
        private String number;

        /**
         * 有效期
         * 格式 MMYY
         */
        @NotNull
        @JsonProperty("ExpirationDate")
        private String expirationDate;
    }

    /**
     * ExtendInfo节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExtendInfo {

        /**
         * 扩展字段
         * 可以存放合作伙伴自己的一些信息(Api不会改变这里的信息)，有利于获取订单后进行一些渠道分析等
         */
        @Nullable
        @JsonProperty("String1")
        private String string1;

        /**
         * 扩展字段
         */
        @Nullable
        @JsonProperty("String2")
        private String string2;

        /**
         * 扩展字段
         */
        @Nullable
        @JsonProperty("String3")
        private String string3;

        /**
         * 扩展字段
         */
        @Nullable
        @JsonProperty("Int1")
        private Integer int1;

        /**
         * 扩展字段
         */
        @Nullable
        @JsonProperty("Int2")
        private Integer int2;

        /**
         * 扩展字段
         */
        @Nullable
        @JsonProperty("Int3")
        private Integer int3;
    }

    /**
     * OrderValidation节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OrderValidation {

        /**
         * 校验类型
         * 逗号分隔的数字：
         * 0:不进行校验
         * 1:早餐数量校验
         * 2：取消时间校验
         * 3：担保金额校验
         * 如果校验所有：Type=1,2,3
         */
        @NotNull
        @JsonProperty("Type")
        private String type;

        /**
         * 担保金额
         * 预付金额或担保金额
         */
        @Nullable
        @JsonProperty("GuaranteeAmount")
        private BigDecimal guaranteeAmount;

        /**
         * 最晚取消时间
         * 担保预付最晚取消时间
         */
        @Nullable
        @JsonProperty("CancelTime")
        private LocalDateTime cancelTime;

        /**
         * 每日早餐数量列表
         */
        @Nullable
        @JsonProperty("DateBreakFastList")
        private List<DateBreakFast> dateBreakFastList;

        /**
         * DateBreakFast节点
         */
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class DateBreakFast {

            /**
             * 日期
             */
            @NotNull
            @JsonProperty("Date")
            private LocalDateTime date;

            /**
             * 包含的早餐份数
             */
            @NotNull
            @JsonProperty("BreakFastCount")
            private String breakfastCount;
        }
    }

    /**
     * CouponInfo节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CouponInfo {

        /**
         * 优惠总金额
         */
        @NotNull
        @JsonProperty("Amount")
        private BigDecimal amount;

        /**
         * 每日促销详情
         */
        @Nullable
        @JsonProperty("NightlyCoupons")
        private List<NightlyCoupon> nightlyCoupons;

        /**
         * NightlyCoupon节点
         */
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class NightlyCoupon {
            /**
             * 在店日期
             */
            @Nullable
            @JsonProperty("Date")
            private LocalDateTime date;

            /**
             * 每间夜优惠详情
             */
            @NotNull
            @JsonProperty("PromotionRoomNights")
            private List<PromotionRoomNight> promotionRoomNights;

            /**
             * PromotionRoomNight节点
             */
            @Data
            @NoArgsConstructor
            @AllArgsConstructor
            public static class PromotionRoomNight {

                /**
                 * 优惠金额
                 */
                @Nullable
                @JsonProperty("Amount")
                private LocalDateTime amount;
            }
        }
    }

    /**
     * DayPrice节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DayPrice {

        /**
         * 每日价
         */
        @NotNull
        @JsonProperty("Price")
        private BigDecimal price;

        /**
         * 日期
         * 价格对应的日期
         */
        @NotNull
        @JsonProperty("Date")
        private LocalDate date;

        /**
         * 税后价
         * 国际必传、国内不允许传。对应于NightRate里MinRate
         */
        @Nullable
        @JsonProperty("MinRate")
        private BigDecimal minRate;
    }
}
