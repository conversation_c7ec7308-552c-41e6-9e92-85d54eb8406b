package com.somytrip.entity.hotel.WEBBEDS;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName: WEBBEDSQueryOrderResp
 * @Description: WEBBEDS查询订单响应
 * @Author: shadow
 * @Date: 2024/3/19 4:33
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WEBBEDSQueryOrderResp {

    /**
     * 取消费用
     */
    @JsonProperty("cancel_fee")
    private BigDecimal cancelFee;

    /**
     * 订单号
     */
    @JsonProperty("booking_no")
    private String bookingNo;

    /**
     * 币种
     */
    private String currency;

    /**
     * 酒店确认代码
     */
    @JsonProperty("hotel_confirm_code")
    private String hotelConfirmCode;

    /**
     * 订单状态
     */
    @JsonProperty("order_status")
    private Integer orderStatus;

    /**
     * 订单金额
     */
    @JsonProperty("total_amount")
    private BigDecimal totalAmount;

    /**
     * 注意事项
     */
    private String notices;

    /**
     * 取消政策
     */
    @JsonProperty("cancel_policies")
    private List<WEBBEDSBookResp.CancelPolicy> cancelPolicies;
}
