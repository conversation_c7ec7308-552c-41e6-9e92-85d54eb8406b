package com.somytrip.entity.hotel.WEBBEDS;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName: WEBBEDSPrebookReq
 * @Description: WEBBEDS验价请求参数
 * @Author: shadow
 * @Date: 2024/3/19 4:37
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WEBBEDSPrebookReq extends WEBBEDSBaseReq {

    /**
     * 酒店ID
     */
    @JsonProperty("hotel_id")
    private Integer hotelId;

    /**
     * 房型ID
     */
    @JsonProperty("room_type_id")
    private Integer roomTypeId;

    /**
     * 价格计划ID
     */
    @JsonProperty("room_id")
    private String roomId;

    /**
     * 房间数量
     */
    @JsonProperty("number_of_rooms")
    private Integer numberOfRooms;

    /**
     * 成人数量
     */
    @JsonProperty("number_of_adults")
    private Integer numberOfAdults;

    /**
     * 儿童数量
     */
    @JsonProperty("number_of_children")
    private Integer numberOfChildren;

    /**
     * 儿童年龄数组
     */
    @JsonProperty("children_ages")
    private List<Integer> childrenAges;

    /**
     * 入住日期
     */
    @JsonProperty("check_in_date")
    private String checkInDate;

    /**
     * 离店日期
     */
    @JsonProperty("check_out_date")
    private String checkOutDate;

    /**
     * 最后到达日期
     */
    @JsonProperty("late_arrival_time")
    private String lateArrivalTime;

    /**
     * ISO国籍代码 客人国籍
     */
    @JsonProperty("client_nationality")
    private String clientNationality;

    /**
     * 币种
     */
    private String currency;

    /**
     * 查价接口返回的日志ID
     */
    private String searchLogId;

    /**
     * 查价接口返回的总价
     */
    private BigDecimal searchPrice;
}
