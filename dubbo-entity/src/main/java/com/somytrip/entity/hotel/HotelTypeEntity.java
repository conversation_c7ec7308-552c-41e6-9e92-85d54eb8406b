package com.somytrip.entity.hotel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.somytrip.entity.enums.hotel.HotelOrigin;
import com.somytrip.entity.hotel.ELong.offline.ELongStaticHotelInfoResp;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Map;

/**
 * (HotelTypes)实体类
 *
 * <AUTHOR>
 * @since 2024-01-31 16:09:34
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "hotel_types")
public class HotelTypeEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 718869333749707948L;

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 酒店来源
     */
    private HotelOrigin hotelOrigin;
    /**
     * 酒店类型ID
     */
    private String hotelTypeId;
    /**
     * 酒店类型中文名称
     */
    private String hotelTypeName;
    /**
     * 酒店类型英文名称
     */
    private String hotelTypeNameEn;
    /**
     * 国际化字典
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Map<String, String>> localeDict;

    public HotelTypeEntity(ELongStaticHotelInfoResp.Detail.HotelType hotelType) {
        this.hotelOrigin = HotelOrigin.ELong;
        this.hotelTypeId = hotelType.getHotelTypeId();
        this.hotelTypeName = hotelType.getHotelTypeName();
        this.hotelTypeNameEn = hotelType.getHotelTypeNameEn();
    }
}

