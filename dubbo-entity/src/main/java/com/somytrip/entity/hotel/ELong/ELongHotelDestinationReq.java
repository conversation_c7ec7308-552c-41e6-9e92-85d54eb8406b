package com.somytrip.entity.hotel.ELong;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/**
 * @ClassName: ELongHotelDestinationReq
 * @Description: 同程艺龙目的地补全请求参数
 * @Author: shadow
 * @Date: 2024/3/1 10:27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ELongHotelDestinationReq {

    /**
     * 关键字
     * 用户输入的关键字
     */
    @Nullable
    @JsonProperty("QueryText")
    private String queryText;

    /**
     * 用户设备ID
     * 用户设备的唯一识别号，可以是mac地址，手机也可以是IMEI，一般不需要传入
     */
    @Nullable
    @JsonProperty("DeviceId")
    private String deviceId;

    /**
     * 用户系统类型
     * 可传入Android、IOS、PC、Other中的任意一个
     */
    @Nullable
    @JsonProperty("OSType")
    private String osType;

    /**
     * 目的地范围
     * 在哪个范围进行目的地匹配检索。
     * 0-国内
     * 1-国际
     * 2-国内国际混排，国内优先
     * 3-国内国际混排，国际优先
     */
    @NotNull
    @JsonProperty("SugOrientation")
    private Integer sugOrientation;
}
