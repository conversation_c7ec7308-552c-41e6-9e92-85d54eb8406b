package com.somytrip.entity.hotel.ELong;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.somytrip.entity.dto.hotel.QueryHotelListDtoV2;
import com.somytrip.entity.enums.hotel.HotelSortType;
import com.somytrip.entity.enums.hotel.PaymentType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * @ClassName: ELongHotelListReq
 * @Description: 同程艺龙酒店列表搜索请求参数
 * @Author: shadow
 * @Date: 2024/3/1 11:16
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ELongHotelListReq {

    /**
     * 入住日期
     * 大于等于昨天。如果入住的时间为凌晨，那么传入前一天的日期。
     */
    @NotNull
    @JsonProperty("ArrivalDate")
    private LocalDate arrivalDate;

    /**
     * 离店日期
     * 至少晚于到店时间1天，不多于30天
     */
    @NotNull
    @JsonProperty("DepartureDate")
    private LocalDate departureDate;

    /**
     * 城市编码
     * 可以从GEO数据静态文件中获取，不可为空
     */
    @NotNull
    @JsonProperty("CityId")
    private String cityId;

    /**
     * 查询关键词
     * 全文检索，可以是酒店名、位置或品牌等。使用本参数的时候，需要输入CityId或DistrictId
     */
    @Nullable
    @JsonProperty("QueryText")
    private String queryText;

    /**
     * 查询类型
     * Intelligent    智能搜索 （默认）
     * HotelName  酒店名称
     * LocationName 位置名称
     */
    @Nullable
    @JsonProperty("QueryType")
    private String queryType;

    /**
     * 支付方式
     * All-全部（默认）
     * SelfPay-现付
     * Prepay-预付
     * 具体信息查看使用说明
     */
    @Nullable
    @JsonProperty("PaymentType")
    private PaymentType paymentType;

    /**
     * 产品类型
     * 支持多个选项同时传入，以逗号分隔。本参数是筛选包含指定条件的酒店，结果中的酒店可能包含其他属性的产品请自行过滤。可选值为：
     * All =全部,
     * LastMinuteSale =今日特价,
     * LimitedTimeSale =限时抢购,
     * WithoutGuarantee =免担保
     * AdvanceBooking=早订优惠
     * LongStayBooking=连住优惠
     */
    @Nullable
    @JsonProperty("ProductProperties")
    private String productProperties;

    /**
     * 设施
     * 可以逗号分隔的组合，建议最多3个。
     * 以上是本接口使用的简略设施信息，与静态信息中获取的设施id并不对应。
     * 目前设施id不再固定，而是不同城市拥有各自不同的设施和对应的id，请定期通过静态信息接口：https://api.elongstatic.com/xml/v2.0/hotel/facilities_cn.xml，来获取最新的设施和对应的id，一般建议每天获取一次。
     */
    @Nullable
    @JsonProperty("Facilities")
    private String facilities;

    /**
     * 主题
     * 可以逗号分隔的组合，建议最多3个（建议不使用该参数，推荐使用FilterList参数）。
     * 目前主题id不再固定，而是不同城市拥有各自不同的主题和对应的id，请定期通过静态信息接口：https://api.elongstatic.com/xml/v2.0/hotel/theme_cn.xml，来获取最新的主题和对应的id，一般建议每天获取一次。
     */
    @Nullable
    @JsonProperty("ThemeIds")
    private String themeIds;

    /**
     * 过滤项列表
     * 包含多个HotelFilterType节点
     */
    @Nullable
    @JsonProperty("FilterList")
    private List<HotelFilterType> filterList;

    /**
     * 推荐星级
     * 对应酒店详情静态文件中的Category。搜索多个星级以逗号分隔
     * 可选值：0,1,2：客栈；3：舒适；4：高档；5：豪华；A：公寓
     */
    @Nullable
    @JsonProperty("StarRate")
    private String starRate;

    /**
     * 品牌编码
     * 可以从酒店品牌静态文件中获取
     */
    @Nullable
    @JsonProperty("BrandId")
    private String brandId;

    /**
     * 酒店集团编码
     */
    @Nullable
    @JsonProperty("GroupId")
    private Integer groupId;

    /**
     * 最小价格
     * 过滤的是酒店中的产品,如果酒店的产品有一个满足这个区间这个酒店就算满足这个条件。注意不是按酒店最低价格来过滤的。
     */
    @Nullable
    @JsonProperty("LowRate")
    private Integer lowRate;

    /**
     * 最大价格
     */
    @Nullable
    @JsonProperty("HighRate")
    private Integer highRate;

    /**
     * 地区编码
     * 可以从GEO数据静态文件中获取
     */
    @Nullable
    @JsonProperty("DistrictId")
    private String districtId;

    /**
     * 商圈编码
     * 可以从GEO数据静态文件中获取
     */
    @Nullable
    @JsonProperty("BusinessZoneId")
    private String businessZoneId;

    /**
     * 位置查询
     * 点选位置搜索，坐标采用百度的坐标体系，字段参考Position节点
     * <p>
     * 注意：
     * 1.本字段不为空时，其中所有的属性（Longitude、Latitude、Radius）都不能为空，且Longitude、Latitude、Radius三个属性的首字母必须大写。
     * 2.本字段不为空时，搜索类型是周边搜索，返回的酒店经纬度会在指定半径范围，但如果传入了QueryText且这边解析为一个poi, 则按照poi类型搜索，不会按照周边搜索，所以可能返回的经纬度超出指定范围。
     * 3.如果本字段为空，默认搜索类型为地区搜索，如果传入了QueryText，且这边解析为poi, 则按照poi类型搜索
     */
    @Nullable
    @JsonProperty("Position")
    private Position position;

    /**
     * 预付发票模式
     * Elong-艺龙开发票、Hotel-酒店开发票
     * 不传该字段表示不限制发票类型
     * 前台自付产品都是酒店开发票，这里的过滤是针对预付产品。
     * 需要注意Elong艺龙开发票其实是艺龙可以提供代开发票服务，如果需要开通，请联系商务
     */
    @Nullable
    @JsonProperty("InvoiceMode")
    private String invoiceMode;

    /**
     * 排序类型
     * Default艺龙默认排序
     * StarRankDesc推荐星级降序
     * RateAsc价格升序
     * RateDesc价格降序
     * DistanceAsc距离升序
     */
    @Nullable
    @JsonProperty("Sort")
    private HotelSortType sort;

    /**
     * 页码
     * 从1开始
     */
    @Nullable
    @JsonProperty("PageIndex")
    private Integer pageIndex;

    /**
     * 每页记录数
     * 取值范围：1-20，默认为10
     */
    @Nullable
    @JsonProperty("PageSize")
    private Integer pageSize;

    /**
     * 宾客类型
     * None=全部;  （默认）
     * All=统一价；
     * Chinese =内宾价，需提示客人“须持大陆身份证入住”；
     * OtherForeign =外宾价，需提示客人“须持国外护照入住”；
     * HongKong   =港澳台客人价，需提示客人“须持港澳台身份证入住”；
     * ChinaGuest =中宾价，需提示客人“须持中国身份证、护照入住”；
     * 搜索时传All表示搜索对所有宾客售价一致的产品，此时搜索结果中不包含Chinese、OtherForeigh等类型的产品。如果需要查询所有产品，那么传None。
     */
    @Nullable
    @JsonProperty("CustomerType")
    private String customerType;

    /**
     * 房间入住人数
     * 默认为0，结果返回的酒店中将包含至少一个房间的可容纳人数大于等于该值，小于该值的房型将会过滤掉
     */
    @Nullable
    @JsonProperty("CheckInPersonAmount")
    private Integer checkInPersonAmount;

    /**
     * 返回信息类型
     * 可以是逗号分隔的组合。
     * 1.可销售价格信息(房间、RP、促销、增值)
     * 2.规则信息(预订、Drr、担保规则、预付规则)
     * 3.酒店基本信息，即返回信息中的Detail字段
     * 4.当前不可销售的rp（产品计划）不出现在结果里
     * 5.不返回Rooms、GuaranteeeRules、PrepayRules、AddValues等和产品有关系的对象
     * 7.返回汇率信息ExchangeRateList
     * 8.经纬度返回百度坐标
     * 9.仅返回钟点房
     * 11.返回新担保、新预付规则
     * 一般情况下传入1,2,3,4
     */
    @Nullable
    @JsonProperty("ResultType")
    private String resultType;

    /**
     * HotelFilterType 节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HotelFilterType {

        /**
         * 筛选TypeId
         * 通过hotel.filter接口获取到的最小子节点的typeId
         */
        @NotNull
        @JsonProperty("FilterType")
        private Integer filterType;

        /**
         * 筛选项Id
         * 通过hotel.filter接口获取到的最小子节点的Id
         */
        @NotNull
        @JsonProperty("FilterId")
        private Integer filterId;

        public HotelFilterType(QueryHotelListDtoV2.QueryHotelListFilter queryHotelListFilter) {
            if (ObjectUtils.isNull(queryHotelListFilter.getTypeCode(), queryHotelListFilter.getFilterCode())) {
                return;
            }
            this.filterType = Integer.valueOf(queryHotelListFilter.getTypeCode());
            this.filterId = Integer.valueOf(queryHotelListFilter.getFilterCode());
        }
    }

    /**
     * Position 节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Position {

        /**
         * 经度
         * 百度坐标系
         */
        @NotNull
        @JsonProperty("Longitude")
        private BigDecimal longitude;

        /**
         * 维度
         * 百度坐标系
         */
        @NotNull
        @JsonProperty("Latitude")
        private BigDecimal latitude;

        /**
         * 半径
         * 单位：米,最大20000m
         */
        @NotNull
        @JsonProperty("Radius")
        private Integer radius;
    }
}
