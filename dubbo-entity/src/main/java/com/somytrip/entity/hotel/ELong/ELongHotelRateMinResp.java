package com.somytrip.entity.hotel.ELong;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.math.BigDecimal;
import java.util.List;

/**
 * @projectName: hotel-service
 * @package: com.somytrip.entity.hotel.ELong
 * @className: ELongHotelRateMinResp
 * @author: shadow
 * @description: 同程艺龙酒店最小价响应
 * @date: 2024/4/1 15:24
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ELongHotelRateMinResp {

    /**
     * 酒店结果集
     */
    @Nullable
    @JsonProperty("Hotels")
    private List<Hotel> hotels;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Hotel {

        /**
         * 酒店编号
         */
        @NotNull
        @JsonProperty("HotelId")
        private String hotelId;

        /**
         * 最小价格
         * 酒店的最小价格信息
         */
        @NotNull
        @JsonProperty("MinRates")
        private List<MinRate> minRates;

        /**
         * 房型列表
         * Options包含2时返回
         */
        @NotNull
        @JsonProperty("Rooms")
        private List<Room> rooms;

        // 其他属性后续需要时补充...
        // https://open.elong.com/doc/info/cn-api-search-hotel_rate_min

        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class MinRate {

            /**
             * 最小价类型
             * 1.全日房最小卖价
             * 2.钟点房最小卖价
             * 3.限价的全日房最小卖价，结算价模式时给出
             * 4.限价的钟点房最小卖价，结算价模式时给出
             * 5.非限价的全日房最小结算价，结算价模式时给出
             * 6.非限价的钟点房最小结算价，结算价模式时给出
             * 使用注意：
             * 卖价模式分销商，只需关注1和2；
             * 结算价模式分销商，如果按照艺龙建议卖价进行售卖，只需关注1和2；
             * 结算价模式分销商，有自主定价策略，可按照非限价的最小结算价加价后对比限价的最小卖价，得出当前酒店最小卖价，需要关注3、4、5、6；
             * 结算价模式分销商，有自主定价策略，无限价产品，可按照结算价加价后，得出当前酒店最小卖价，需要关注5、6；
             */
            @NotNull
            @JsonProperty("Type")
            private Integer type;

            /**
             * 原始价
             * 未经过DRR计算过的原始价格
             */
            @Nullable
            @JsonProperty("Basis")
            private BigDecimal basis;

            /**
             * 会员价
             */
            @NotNull
            @JsonProperty("Member")
            private BigDecimal member;

            /**
             * 结算价
             */
            @NotNull
            @JsonProperty("Cost")
            private BigDecimal cost;

            /**
             * 币种
             */
            @NotNull
            @JsonProperty("CurrencyCode")
            private String currencyCode;

            /**
             * 关联房型编号集合
             * MinRate节点为酒店下最小价时返回，关联最小价对应房型商品信息，不同房型多个商品可能对应同一个最小价b
             */
            @Nullable
            @JsonProperty("RoomIds")
            private List<RoomId> roomIds;

            @Data
            @NoArgsConstructor
            @AllArgsConstructor
            public static class RoomId {

                /**
                 * 关联房型编号
                 */
                @NotNull
                @JsonProperty("RoomId")
                private String roomId;

                /**
                 * 关联商品唯一标识集合
                 * 多个商品可能对应同一个最小价
                 */
                @NotNull
                @JsonProperty("GoodsUniqIds")
                private List<String> goodsUniqIds;
            }
        }

        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class Room {

            /**
             * 房型编号
             */
            @NotNull
            @JsonProperty("RoomId")
            private String roomId;

            /**
             * 房型名称
             */
            @NotNull
            @JsonProperty("Name")
            private String name;

            /**
             * 最小价格
             * 房型的最小价格信息
             */
            @NotNull
            @JsonProperty("MinRates")
            private List<MinRate> minRates;
        }
    }
}
