package com.somytrip.entity.hotel.ELong;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.somytrip.entity.enums.hotel.*;
import com.somytrip.entity.enums.hotel.idcardtype.IdCardType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * @ClassName: ELongHotelOrderDetailResp
 * @Description: 同程艺龙酒店订单详情响应
 * @Author: shadow
 * @Date: 2024/2/24 11:31
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ELongHotelOrderDetailResp {

    /**
     * 订单编号
     * 如果OrderId不为0，以OrderId为主
     */
    @NotNull
    @JsonProperty("OrderId")
    private Long orderId;

    /**
     * 酒店编号
     */
    @NotNull
    @JsonProperty("HotelId")
    private String hotelId;

    /**
     * 酒店名称
     */
    @Nullable
    @JsonProperty("HotelName")
    private String hotelName;

    /**
     * 酒店code
     */
    @Nullable
    @JsonProperty("HotelCode")
    private String hotelCode;

    /**
     * 房型编号
     */
    @Nullable
    @JsonProperty("RoomId")
    private String roomId;

    /**
     * 房型名称
     * 展示给客人的房型名称
     */
    @NotNull
    @JsonProperty("RoomName")
    private String roomName;

    /**
     * 销售房型编号
     */
    @NotNull
    @JsonProperty("RoomTypeId")
    private String roomTypeId;

    /**
     * 销售房型名称
     * 此名称仅作为参考，不应展示给客人
     */
    @Nullable
    @JsonProperty("RoomTypeName")
    private String roomTypeName;

    /**
     * 房间类型描述
     */
    @Nullable
    @JsonProperty("RoomTypeDescription")
    private String roomTypeDescription;

    /**
     * 产品编号
     */
    @NotNull
    @JsonProperty("RatePlanId")
    private Integer ratePlanId;

    /**
     * 产品名称
     */
    @Nullable
    @JsonProperty("RatePlanName")
    private String ratePlanName;

    /**
     * 入住日期
     */
    @NotNull
    @JsonProperty("ArrivalDate")
    private LocalDateTime arrivalDate;

    /**
     * 离店日期
     */
    @NotNull
    @JsonProperty("DepartureDate")
    private LocalDateTime departureDate;

    /**
     * 订单状态
     * A-已确认
     * B-NO SHOW
     * B1-有预定未查到
     * B2-待查
     * B3-暂不确定
     * C-已结帐
     * D-删除
     * E-取消
     * F-已入住
     * G-变价
     * H-变更
     * N-新单
     * O-满房
     * S-特殊
     * U-特殊满房
     * V-已审
     * Z-删除,另换酒店
     */
    @NotNull
    @JsonProperty("Status")
    private String status;

    /**
     * 对用户展示的订单状态
     * 请将这个状态展示给客人；某些状态下的详细信息会出现在NoteToGuest。
     * 0 -- 没有获取到订单展示状态，需要重新获取
     * 1 -- 担保失败
     * 2 -- 等待担保
     * 4 -- 等待确认
     * 8 -- 等待支付
     * 16 -- 等待核实入住
     * 32 -- 酒店拒绝订单
     * 64 -- 未入住
     * 128 -- 已经离店
     * 256 -- 已经取消
     * 512 -- 已经确认
     * 1024 -- 已经入住
     * 2048 -- 正在担保-处理中
     * 4096 -- 正在支付-处理中
     * 8192 - 支付失败
     */
    @Nullable
    @JsonProperty("ShowStatus")
    private Long showStatus;

    /**
     * 下一次确认反馈时间点
     * 当ShowStatus=4等待确认的时候，系统会在这个时间点前更新确认相关的内容.如果感觉这个时间点太长，可以调用hotel.order.promote进行催确认
     */
    @Nullable
    @JsonProperty("ConfirmPoint")
    private LocalDateTime confirmPoint;

    /**
     * 客人类型
     * All=统一价；
     * Chinese =内宾价，需提示客人“须持大陆身份证入住”；
     * OtherForeign =外宾价，需提示客人“须持国外护照入住”；
     * HongKong   =港澳台客人价，需提示客人“须持港澳台身份证入住”；
     * ChinaGuest =中宾价，需提示客人“须持中国身份证、护照入住”；
     */
    @NotNull
    @JsonProperty("CustomerType")
    private CustomerType customerType;

    /**
     * 付款类型
     * SelfPay-前台现付、Prepay-预付
     */
    @NotNull
    @JsonProperty("PaymentType")
    private PaymentType paymentType;

    /**
     * 房间数量
     */
    @NotNull
    @JsonProperty("NumberOfRooms")
    private Integer numberOfRooms;

    /**
     * 客人数量
     */
    @NotNull
    @JsonProperty("NumberOfCustomers")
    private Integer numberOfCustomers;

    /**
     * 最低顾客年龄
     */
    @Nullable
    @JsonProperty("MinGuestAge")
    private Integer minGuestAge;

    /**
     * 最早到店时间
     */
    @NotNull
    @JsonProperty("EarliestArrivalTime")
    private LocalDateTime earliestArrivalTime;

    /**
     * 最晚到店时间
     */
    @NotNull
    @JsonProperty("LatestArrivalTime")
    private LocalDateTime latestArrivalTime;

    /**
     * 货币类型
     * RMB, HKD, MOP, TWD，USD
     */
    @NotNull
    @JsonProperty("CurrencyCode")
    private String currencyCode;

    /**
     * 总价
     * 货币类型查看CurrencyCode字段
     */
    @NotNull
    @JsonProperty("TotalPrice")
    private BigDecimal totalPrice;

    /**
     * 艺龙会员卡号
     */
    @Nullable
    @JsonProperty("ElongCardNo")
    private String elongCardNo;

    /**
     * 确认类型
     * NotAllowedConfirm   不允许确认(合作伙伴自查订单状态后自行联系客人)
     * SMS_cn  ----艺龙发短信给客人,出现订单问题的时候会主动联系
     * NoNeed -- 艺龙发短信给客人,出现订单问题的时候不主动联系
     * 注：除了NotAllowedConfirm，其余的选项艺龙都会发送短信，下单时如果输入了邮箱那么都会发送邮件
     */
    @NotNull
    @JsonProperty("ConfirmationType")
    private OrderConfirmationType confirmationType;

    /**
     * 给酒店备注
     */
    @Nullable
    @JsonProperty("NoteToHotel")
    private String noteToHotel;

    /**
     * 给艺龙备注
     */
    @Nullable
    @JsonProperty("NoteToElong")
    private String noteToElong;

    /**
     * 给客人的备注
     */
    @Nullable
    @JsonProperty("NoteToGuest")
    private String noteToGuest;

    /**
     * 订单产生的罚金
     */
    @Nullable
    @JsonProperty("PenaltyToCustomer")
    private BigDecimal penaltyToCustomer;

    /**
     * 罚金货币类型
     */
    @Nullable
    @JsonProperty("PenaltyCurrencyCode")
    private String penaltyCurrencyCode;

    /**
     * 是否可退款
     * 在E状态吐出该字段，标识E状态代理是否可以退款给客人，为false时需要等到订单状态变为删除才可退款
     */
    @Nullable
    @JsonProperty("IsRefund")
    private Boolean isRefund;

    /**
     * 预订时间
     */
    @Nullable
    @JsonProperty("CreationDate")
    private LocalDateTime creationDate;

    /**
     * 当前是否可以取消
     * 当为true，且CancelTime大于当前时间时，才对用户展示CancelTime。
     */
    @Nullable
    @JsonProperty("IsCancelable")
    private Boolean isCancelable;

    /**
     * 最晚取消时间
     * CancelTime指最晚可以取消的时间，有时会返回一些很久远的时间，比如1970-01-01或者2001-01-01之类的时间，这种情况就是不可取消，正常判断即可，当前时间肯定大于这个。这个不一定是免费取消的时间，具体免费取消时间要看预付规则。
     */
    @Nullable
    @JsonProperty("CancelTime")
    private LocalDateTime cancelTime;

    /**
     * 是否有发票信息
     */
    @Nullable
    @JsonProperty("HasInvoice")
    private Boolean hasInvoice;

    /**
     * 产品是否支持开专票
     * 0：产品不支持开专票；1：产品支持开专票
     */
    @Nullable
    @JsonProperty("SupportAnticipation")
    private String supportAnticipation;

    /**
     * 发票
     */
    @Nullable
    @JsonProperty("Invoice")
    private Invoice invoice;

    /**
     * 臻选特惠促销信息
     */
    @Nullable
    @JsonProperty("DayPromotions")
    private List<DayPromotion> dayPromotions;

    /**
     * 同程促销促销信息
     */
    @Nullable
    @JsonProperty("UsedPromotionDayRoomValues")
    private List<OrderDetailDayRoomPromotion> usedPromotionDayRoomValues;

    /**
     * 联系人
     */
    @NotNull
    @JsonProperty("Contact")
    private Contact contact;

    /**
     * 信用卡
     */
    @Nullable
    @JsonProperty("CreditCard")
    private CreditCard creditCard;

    /**
     * 每夜价格
     */
    @Nullable
    @JsonProperty("NightlyRates")
    private List<NightlyRate> nightlyRates;

    /**
     * 扩展信息
     */
    @Nullable
    @JsonProperty("ExtendInfo")
    private ExtendInfo extendInfo;

    /**
     * 房间信息
     */
    @NotNull
    @JsonProperty("OrderRooms")
    private List<OrderRoom> orderRooms;

    /**
     * 担保规则
     */
    @Nullable
    @JsonProperty("GuaranteeRule")
    private GuaranteeRule guaranteeRule;

    /**
     * 预付规则
     */
    @Nullable
    @JsonProperty("PrepayRule")
    private PrepayRule prepayRule;

    /**
     * 预付结果
     */
    @Nullable
    @JsonProperty("PrepayResult")
    private PrepayResult prepayResult;

    /**
     * 担保结果
     */
    @Nullable
    @JsonProperty("GuaranteeResult")
    private GuaranteeResult guaranteeResult;

    /**
     * 多次退款详情
     */
    @Nullable
    @JsonProperty("refundDetail")
    private RefundDetail refundDetail;

    /**
     * 增值服务
     */
    @Nullable
    @JsonProperty("ValueAdds")
    private List<String> valueAdds;

    /**
     * 礼包套餐
     */
    @Nullable
    @JsonProperty("GiftPackages")
    private List<GiftPackage> giftPackages;

    /**
     * 预付订单的发票开具模式
     * Elong-艺龙开发票、Hotel-酒店开发票
     * 前台自付产品都是酒店开发票，这里的过滤是针对预付产品。
     * 如果结果中的InvoiceMode为空表示不开发票。
     */
    @Nullable
    @JsonProperty("InvoiceMode")
    private String invoiceMode;

    /**
     * 换算为人民币的订单总卖价
     */
    @Nullable
    @JsonProperty("TotalPriceExchanged")
    private BigDecimal totalPriceExchanged;

    /**
     * 换算为人民币的订单总底价
     */
    @Nullable
    @JsonProperty("TotalCostPriceExchanged")
    private BigDecimal totalCostPriceExchanged;

    /**
     * 是否及时确认
     */
    @Nullable
    @JsonProperty("IsInstantConfirm")
    private Boolean isInstantConfirm;

    /**
     * 代理自己的订单号
     */
    @Nullable
    @JsonProperty("AffiliateConfirmationId")
    private String affiliateConfirmationId;

    /**
     * 订单关联的酒店信息
     */
    @Nullable
    @JsonProperty("OrderHotel")
    private OrderHotel orderHotel;

    /**
     * 预付订单线下退款金额
     */
    @Nullable
    @JsonProperty("RefundAmount")
    private BigDecimal refundAmount;

    /**
     * 销售给客人的最终价格
     */
    @Nullable
    @JsonProperty("CustomerPrice")
    private BigDecimal customerPrice;

    /**
     * 实际支付金额
     */
    @Nullable
    @JsonProperty("PayAmount")
    private BigDecimal payAmount;

    /**
     * 是否为钟点房
     * 小时房产品返回true
     */
    @Nullable
    @JsonProperty("hourRoom")
    private Boolean hourRoom;

    /**
     * 钟点房入住开始时间
     * 当HourRoom=true时返回，字段值为成单时传入且校验通过的客人入住开始及结束时间，格式为yyyy-MM-dd HH:mm:ss
     */
    @Nullable
    @JsonProperty("hourRoomStartTime")
    private LocalDateTime hourRoomStartTime;

    /**
     * 钟点房入住结束时间
     */
    @Nullable
    @JsonProperty("hourRoomEndTime")
    private LocalDateTime hourRoomEndTime;

    /**
     * 餐食信息
     */
    @Nullable
    @JsonProperty("Meals")
    private List<DayMeal> meals;

    /**
     * 特殊取消申请
     * 当为true时表示此时订单可以发起工单特殊取消
     */
    @NotNull
    @JsonProperty("SpecialCancelApply")
    private Boolean specialCancelApply;

    /**
     * 早餐
     * 国际特有字段
     */
    @Nullable
    @JsonProperty("Breakfast")
    private String breakfast;

    /**
     * 基础预付规则
     * 国际特有字段
     */
    @Nullable
    @JsonProperty("BasePrepayRule")
    private BasePrepayRule basePrepayRule;

    /**
     * 供应商id
     * 国际特有字段
     */
    @Nullable
    @JsonProperty("SupplierID")
    private String supplierId;

    /**
     * 二级供应商id
     * 国际特有字段
     */
    @Nullable
    @JsonProperty("SubSupplierID")
    private String subSupplierId;

    /**
     * 特殊要求
     * 国际特有字段
     */
    @Nullable
    @JsonProperty("SpecificRemark")
    private String specificRemark;

    /**
     * 税和服务费原币种
     * 国际特有字段
     */
    @Nullable
    @JsonProperty("TaxAndService")
    private BigDecimal taxAndService;

    /**
     * 税和服务费人民币币种
     * 国际特有字段
     */
    @Nullable
    @JsonProperty("TaxAndServiceRMB")
    private BigDecimal taxAndServiceRMB;

    /**
     * 额外人员费用(附加费人民币)
     * 国际特有字段
     */
    @Nullable
    @JsonProperty("ExtraPersonFeeRMB")
    private BigDecimal extraPersonFeeRMB;

    /**
     * 入住需知
     * 国际特有字段
     */
    @Nullable
    @JsonProperty("CheckInInstructions")
    private String checkInInstructions;

    /**
     * 床型描述
     * 国际特有字段
     */
    @Nullable
    @JsonProperty("BedDescription")
    private String bedDescription;

    /**
     * 膳食信息
     * 国际特有字段
     */
    @Nullable
    @JsonProperty("BoardInfo")
    private BoardInfo boardInfo;

    /**
     * 房间最大可住成人数
     * 国际特有字段
     */
    @Nullable
    @JsonProperty("OccupancyPerRoom")
    private Integer occupancyPerRoom;

    /**
     * 房间最大可住儿童数
     * 国际特有字段
     */
    @Nullable
    @JsonProperty("ChildrenOccupancyPerRoom")
    private Integer childrenOccupancyPerRoom;

    /**
     * 成人数
     * 国际特有字段
     */
    @Nullable
    @JsonProperty("NumberOfAdults")
    private Integer numberOfAdults;

    /**
     * 儿童年龄
     * 国际特有字段
     */
    @Nullable
    @JsonProperty("ChildAges")
    private List<Integer> childAges;

    /**
     * 是否经历过确认前取消
     * 国际特有字段
     */
    @Nullable
    @JsonProperty("IsCancelBeforeConfirmation")
    private Boolean isCancelBeforeConfirmation;

    /**
     * 网络类型
     * 国际特有字段
     */
    @Nullable
    @JsonProperty("InternetType")
    private String internetType;

    /**
     * 上网描述
     * 国际特有字段
     */
    @Nullable
    @JsonProperty("InternetDesc")
    private String internetDesc;

    /**
     * 床型id
     * 国际特有字段
     */
    @Nullable
    @JsonProperty("BedTypeId")
    private String bedTypeId;

    /**
     * 网络
     * 国际特有字段
     */
    @Nullable
    @JsonProperty("Internet")
    private String internet;

    /**
     * 另付税和服务费
     * 国际特有字段
     */
    @Nullable
    @JsonProperty("AdditionalTax")
    private AdditionalTax additionalTax;

    /**
     * Invoice节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Invoice {

        /**
         * 发票类型
         * Paper-纸质发票，Electronic-电子发票，SpecialPaper-专用发票
         */
        @NotNull
        @JsonProperty("InvoiceType")
        private InvoiceType invoiceType;

        /**
         * 抬头类型
         * Personally-个人，Enterprise-企业，Government-政府机关行政单位，默认为企业
         * Personally时，不需填写抬头Title（抬头默认为个人）和纳税人识别号/统一社会信用代码ITIN
         * Enterprise时，必须填写抬头Title和纳税人识别号/统一社会信用代码ITIN
         * Government时，必须填写抬头Title
         */
        @NotNull
        @JsonProperty("TitleType")
        private TitleType titleType;

        /**
         * 抬头
         */
        @Nullable
        @JsonProperty("Title")
        private String title;

        /**
         * 纳税人识别号/统一社会信用代码
         * 字符长度是15、18或20位，是数字和字母的组合或纯数字
         */
        @Nullable
        @JsonProperty("ITIN")
        private String itin;

        /**
         * 发票内容
         */
        @Nullable
        @JsonProperty("ItemName")
        private String itemName;

        /**
         * 金额
         */
        @NotNull
        @JsonProperty("Amount")
        private BigDecimal amount;

        /**
         * 收件人
         */
        @NotNull
        @JsonProperty("Recipient")
        private Recipient recipient;

        /**
         * 是否添加发票备注
         * true-在发票备注栏中添加酒店预订信息（酒店名称、入住日期、离店日期、房间数）
         * false-不添加，默认值
         */
        @Nullable
        @JsonProperty("IsNeedRelationOrder")
        private Boolean isNeedRelationOrder;

        /**
         * 发票备注内容
         */
        @Nullable
        @JsonProperty("MemoInfo")
        private String memoInfo;

        /**
         * 纸质发票状态
         * false--未处理、true--已开票
         */
        @NotNull
        @JsonProperty("Status")
        private Boolean status;

        /**
         * 纸质发票邮寄状态
         * false--未邮寄、true--已邮寄
         */
        @NotNull
        @JsonProperty("DeliveryStatus")
        private Boolean deliveryStatus;

        /**
         * 电子发票处理类型
         * 0 开票，1 红冲，2 修改
         */
        @Nullable
        @JsonProperty("ProcessType")
        private Integer processType;

        /**
         * 电子发票处理状态
         * 0 未处理，1 处理中，2 成功，3 失败
         */
        @Nullable
        @JsonProperty("ProcessStatus")
        private Integer processStatus;

        /**
         * 电子发票pdf下载url
         */
        @Nullable
        @JsonProperty("UrlForPDF")
        private String urlForPDF;

        /**
         * 电子发票加入微信卡券的链接url
         */
        @Nullable
        @JsonProperty("UrlForWeixinCard")
        private String urlForWeixinCard;

        /**
         * 电子发票发票代码
         */
        @Nullable
        @JsonProperty("InvCode")
        private String invCode;

        /**
         * 电子发票发票号
         */
        @Nullable
        @JsonProperty("InvNumber")
        private String invNumber;

        /**
         * 电子发票单据号
         */
        @Nullable
        @JsonProperty("BillNumber")
        private String billNumber;

        /**
         * 纳税人识别号
         * 专票必传
         */
        @Nullable
        @JsonProperty("TaxPayerNum")
        private String taxPayerNum;

        /**
         * 开户银行
         * 专票必传
         */
        @Nullable
        @JsonProperty("TaxRegisterBank")
        private String taxRegisterBank;

        /**
         * 银行账号
         * 专票必传
         */
        @Nullable
        @JsonProperty("RegisterBankNum")
        private String registerBankNum;

        /**
         * 注册地址
         * 专票必传
         */
        @Nullable
        @JsonProperty("RegisterAddress")
        private String registerAddress;

        /**
         * 电话
         * 专票必传
         */
        @Nullable
        @JsonProperty("RegisterPhoneNum")
        private String registerPhoneNum;

        /**
         * Recipient节点
         */
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class Recipient {
            /**
             * 省份
             */
            @NotNull
            @JsonProperty("Province")
            private String province;

            /**
             * 城市
             */
            @NotNull
            @JsonProperty("City")
            private String city;

            /**
             * 行政区
             */
            @NotNull
            @JsonProperty("District")
            private String district;

            /**
             * 街道
             */
            @NotNull
            @JsonProperty("Street")
            private String street;

            /**
             * 邮编
             */
            @Nullable
            @JsonProperty("PostalCode")
            private String postalCode;

            /**
             * 收件人姓名
             */
            @NotNull
            @JsonProperty("Name")
            private String name;

            /**
             * 电话
             */
            @NotNull
            @JsonProperty("Phone")
            private String phone;

            /**
             * Email
             */
            @Nullable
            @JsonProperty("Email")
            private String email;
        }
    }

    /**
     * DayPromotion节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DayPromotion {
        /**
         * 日期
         */
        @NotNull
        @JsonProperty("Date")
        private String date;

        /**
         * 促销信息
         */
        @NotNull
        @JsonProperty("Promotions")
        private List<Promotion> promotions;

        /**
         * Promotion节点
         */
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class Promotion {
            /**
             * 卖价优惠的金额
             */
            @Nullable
            @JsonProperty("PriceDiscountValue")
            private BigDecimal priceDiscountValue;

            /**
             * 促销名称
             */
            @Nullable
            @JsonProperty("PromotionTag")
            private String promotionTag;

            /**
             * ID
             */
            @Nullable
            @JsonProperty("PromotionId")
            private Long promotionId;

            /**
             * 促销类型
             * 0:未定义
             * 1:天天特价
             * 2:门店新客
             * 3:优享会
             * 4:其他促销
             * 5:权益云
             */
            @Nullable
            @JsonProperty("PromotionType")
            private Integer promotionType;
        }
    }


    /**
     * OrderDetailDayRoomPromotion节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OrderDetailDayRoomPromotion {

        /**
         * 促销日期
         */
        @NotNull
        @JsonProperty("StayDate")
        private String stayDate;

        /**
         * 促销日明细
         */
        @NotNull
        @JsonProperty("PromotionDayRoom")
        private List<PromotionDayRoom> promotionDayRoom;

        /**
         * PromotionDayRoom节点
         */
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class PromotionDayRoom {
            /**
             * 房间编号
             */
            @NotNull
            @JsonProperty("RoomNumber")
            private String roomNumber;

            /**
             * 促销明细
             */
            @NotNull
            @JsonProperty("PromotionDetailList")
            private List<PromotionDayRoomItem> promotionDetailList;

            /**
             * PromotionDayRoomItem节点
             */
            @Data
            @NoArgsConstructor
            @AllArgsConstructor
            public static class PromotionDayRoomItem {

                /**
                 * 促销金额
                 */
                @NotNull
                @JsonProperty("Amount")
                private BigDecimal amount;

                /**
                 * 促销类型
                 * 9-立减
                 * 11-红包
                 */
                @NotNull
                @JsonProperty("PromotionType")
                private Integer promotionType;
            }
        }
    }

    /**
     * Contact节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Contact {
        /**
         * 姓名
         */
        @NotNull
        @JsonProperty("Name")
        private String name;

        /**
         * Email
         */
        @Nullable
        @JsonProperty("Email")
        private String email;

        /**
         * 手机号区号
         */
        @Nullable
        @JsonProperty("MobileAreaCode")
        private String mobileAreaCode;

        /**
         * 手机
         */
        @Nullable
        @JsonProperty("Mobile")
        private String mobile;

        /**
         * 电话
         */
        @Nullable
        @JsonProperty("Phone")
        private String phone;

        /**
         * 传真
         */
        @Nullable
        @JsonProperty("Fax")
        private String fax;

        /**
         * 性别
         * Female: 女
         * Male: 男
         * Unknown: 保密
         */
        @Nullable
        @JsonProperty("Gender")
        private Gender gender;

        /**
         * 证件类型
         * IdentityCard: 身份证
         * Passport: 护照
         * Other: 其他
         */
        @Nullable
        @JsonProperty("IdType")
        private IdCardType idType;

        /**
         * 证件号码
         */
        @Nullable
        @JsonProperty("IdNo")
        private String idNo;

        /**
         * 名
         * 国际特有字段
         */
        @Nullable
        @JsonProperty("FirstName")
        private String firstName;

        /**
         * 姓
         * 国际特有字段
         */
        @Nullable
        @JsonProperty("LastName")
        private String lastName;
    }

    /**
     * CreditCard节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CreditCard {

        /**
         * 交易类型
         * Auth: 授权
         * CancelAuth: 取消授权
         * Charge: 授权后扣款
         * Refund: 退款
         * DirectCharge: 直接扣款
         */
        @Nullable
        @JsonProperty("ProcessType")
        private ProcessType processType;

        /**
         * 交易状态
         * UnProcess: 未处理
         * Succeed: 成功
         * Processing: 处理中
         * Fail: 失败
         */
        @Nullable
        @JsonProperty("Status")
        private Status status;

        /**
         * 交易金额
         * 注意：预付订单在ProcessType为Refund时，此处金额不一定是实际退款金额，还需要结合RefundAmount字段查看，
         * 具体逻辑请参考：http://open.elong.com/faq/detail?plt=2&id=122
         */
        @Nullable
        @JsonProperty("Amount")
        private BigDecimal amount;

        /**
         * 备注或失败原因
         */
        @Nullable
        @JsonProperty("Notes")
        private String notes;

        /**
         * 是否可以继续支付
         * 如果可以继续支付，请使用hotel.order.pay 继续完成支付
         */
        @Nullable
        @JsonProperty("IsPayable")
        private Boolean isPayable;

        /**
         * 最晚支付时间
         * 过了最晚支付时间订单将自动取消
         */
        @Nullable
        @JsonProperty("LatestPayTime")
        private LocalDateTime latestPayTime;

        /**
         * 有效年份
         * 已弃用，仅有默认值0
         */
        @Nullable
        @JsonProperty("ExpirationYear")
        private Integer expirationYear;

        /**
         * 有效月份
         * 已弃用，仅有默认值0
         */
        @Nullable
        @JsonProperty("ExpirationMonth")
        private Integer expirationMonth;

        /**
         * 证件类型
         * 已弃用，仅有默认值IdentifyCard
         */
        @Nullable
        @JsonProperty("IdType")
        private String idType;

        /**
         * 交易类型枚举
         */
        public enum ProcessType {
            // 授权
            Auth,
            // 取消授权
            CancelAuth,
            // 授权后扣款
            Charge,
            // 退款
            Refund,
            // 直接扣款
            DirectCharge
        }

        /**
         * 交易状态枚举
         */
        public enum Status {
            // 未处理
            UnProcess,
            // 成功
            Succeed,
            // 处理中
            Processing,
            // 失败
            Fail
        }
    }

    /**
     * NightlyRate节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NightlyRate {
        /**
         * 日期
         */
        @NotNull
        @JsonProperty("Date")
        private LocalDateTime date;

        /**
         * 会员卖价
         */
        @NotNull
        @JsonProperty("Member")
        private BigDecimal member;

        /**
         * 结算价
         * 仅结算价模式下有值
         */
        @Nullable
        @JsonProperty("Cost")
        private BigDecimal cost;

        /**
         * 早餐数量
         */
        @Nullable
        @JsonProperty("BreakfastCount")
        private Integer breakfastCount;

        /**
         * 加床价
         * 已弃用，仅有默认值0
         */
        @Nullable
        @JsonProperty("AddBed")
        private BigDecimal addBed;

        /**
         * 状态
         * 已弃用，仅有默认值false
         */
        @NotNull
        @JsonProperty("Status")
        private Boolean status;

        /**
         * 税和服务费原币种
         * 国际特有字段
         */
        @Nullable
        @JsonProperty("TaxAndServiceFee")
        private BigDecimal taxAndServiceFee;

        /**
         * 税和服务费人民币币种
         * 国际特有字段
         */
        @Nullable
        @JsonProperty("TaxAndServiceFeeRMB")
        private BigDecimal taxAndServiceFeeRMB;

        /**
         * 房价
         * 国际特有字段
         */
        @Nullable
        @JsonProperty("roomRate")
        private BigDecimal roomRate;
    }

    /**
     * ExtendInfo节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExtendInfo {
        /**
         * 可以存放合作伙伴自己的一些信息(Api不会改变这里的信息)，有利于获取订单后进行一些渠道分析等
         */
        @Nullable
        @JsonProperty("String1")
        private String string1;

        @Nullable
        @JsonProperty("String2")
        private String string2;

        @Nullable
        @JsonProperty("String3")
        private String string3;

        @Nullable
        @JsonProperty("Int1")
        private Integer int1;

        @Nullable
        @JsonProperty("Int2")
        private Integer int2;

        @Nullable
        @JsonProperty("Int3")
        private Integer int3;

        /**
         * V1.13新增：Web传入的参数
         */
        @Nullable
        @JsonProperty("PartnerParameter")
        private String partnerParameter;
    }

    /**
     * OrderRoom节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OrderRoom {

        /**
         * 客人信息
         * 参考Customer节点
         */
        @NotNull
        @JsonProperty("Customers")
        private Customer[] customers;

        /**
         * 入住房间号
         * v1.20新增
         */
        @Nullable
        @JsonProperty("RoomNo")
        private String roomNo;

        /**
         * Customer节点
         */
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class Customer {

            /**
             * 姓名
             */
            @NotNull
            @JsonProperty("Name")
            private String name;

            /**
             * 名
             */
            @NotNull
            @JsonProperty("FirstName")
            private String firstName;

            /**
             * 姓
             */
            @NotNull
            @JsonProperty("LastName")
            private String lastName;

            /**
             * Email
             * 已不再使用
             */
            @Nullable
            @JsonProperty("Email")
            private String email;

            /**
             * 手机
             * 已不再使用
             */
            @Nullable
            @JsonProperty("Mobile")
            private String mobile;

            /**
             * 电话
             * 已不再使用
             */
            @Nullable
            @JsonProperty("Phone")
            private String phone;

            /**
             * 传真
             * 已不再使用
             */
            @Nullable
            @JsonProperty("Fax")
            private String fax;

            /**
             * 性别
             */
            @Nullable
            @JsonProperty("Gender")
            private Gender gender;

            /**
             * 证件类型
             */
            @Nullable
            @JsonProperty("IdCardType")
            private IdCardType idCardType;

            /**
             * 证件类型
             * 已不再使用
             */
            @Nullable
            @JsonProperty("IdType")
            private Integer idType;

            /**
             * 证件号码
             * 已不再使用
             */
            @Nullable
            @JsonProperty("IdNo")
            private String idNo;

            /**
             * 国籍
             */
            @Nullable
            @JsonProperty("Nationality")
            private String nationality;

            /**
             * 酒店确认号
             */
            @Nullable
            @JsonProperty("ConfirmationNumber")
            private String confirmationNumber;
        }
    }

    /**
     * GuaranteeRule节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GuaranteeRule {

        /**
         * 担保规则编号
         */
        @NotNull
        @JsonProperty("GuranteeRuleId")
        private Integer guaranteeRuleId;

        /**
         * 描述
         * 如果担保规则存在，将此字段展示给用户
         */
        @NotNull
        @JsonProperty("Description")
        private String description;

        /**
         * 日期类型
         * BookDay-预定日期
         * CheckInDay-入住日期
         * StayDay-在店日期
         */
        @NotNull
        @JsonProperty("DateType")
        private DateType dateType;

        /**
         * 开始日期
         */
        @Nullable
        @JsonProperty("StartDate")
        private LocalDateTime startDate;

        /**
         * 结束日期
         */
        @Nullable
        @JsonProperty("EndDate")
        private LocalDateTime endDate;

        /**
         * 周有效天数
         */
        @Nullable
        @JsonProperty("WeekSet")
        private String weekSet;

        /**
         * 是否到店时间担保
         * False:为不校验到店时间
         * True:为需要校验到店时间
         */
        @NotNull
        @JsonProperty("IsTimeGuarantee")
        private Boolean isTimeGuarantee;

        /**
         * 到店担保开始时间
         * 用于IsTimeGuarantee == true 进行检查
         */
        @Nullable
        @JsonProperty("StartTime")
        private LocalTime startTime;

        /**
         * 到店担保结束时间
         * [补充]当EndTime小于StartTime的时候，默认从StartTime到次日6点都需要担保
         */
        @Nullable
        @JsonProperty("EndTime")
        private LocalTime endTime;

        /**
         * 到店担保的结束时间是否为第二天
         * false为当天，true为次日
         */
        @Nullable
        @JsonProperty("IsTomorrow")
        private Boolean isTomorrow;

        /**
         * 是否房量担保
         * False:为不校验房量条件
         * True:为校验房量条件
         * 如果IsTimeGuarantee和IsAmountGuarantee都为false则为强制担保
         */
        @NotNull
        @JsonProperty("IsAmountGuarantee")
        private Boolean isAmountGuarantee;

        /**
         * 担保的房间数,预定几间房及以上要担保
         * 用于IsAmountGuarantee == true 进行检查
         */
        @Nullable
        @JsonProperty("Amount")
        private Integer amount;

        /**
         * 担保类型
         * FirstNightCost为首晚房费担保
         * FullNightCost为全额房费担保
         */
        @Nullable
        @JsonProperty("GuaranteeType")
        private String guaranteeType;

        /**
         * 变更规则
         * 担保规则取消变更规则：
         * NoChange、不允许变更取消
         * NeedSomeDay、允许变更/取消,需在XX日YY时之前通知
         * NeedCheckinTime、允许变更/取消,需在最早到店时间之前几小时通知
         * NeedCheckin24hour、允许变更/取消,需在到店日期的24点之前几小时通知
         */
        @Nullable
        @JsonProperty("ChangeRule")
        private GuaranteeChangeRule changeRule;

        /**
         * 日期参数
         * ChangeRule = NeedSomeDay时，对应规则2描述中 “允许变更/取消,需在XX日YY时之前通知” 中的XX日，YY时
         */
        @Nullable
        @JsonProperty("Day")
        private LocalDateTime day;

        /**
         * 时间参数
         */
        @Nullable
        @JsonProperty("Time")
        private LocalTime time;

        /**
         * 小时参数
         * ChangeRule = NeedCheckinTime时，对应规则3描述中 “允许变更/取消,需在最早到店时间之前几小时通知” 中的几小时
         * ChangeRule = NeedCheckin24hour时，对应规则4描述中“允许变更/取消,需在到店日期的24点之前几小时通知” 中的几小时
         */
        @Nullable
        @JsonProperty("Hour")
        private Integer hour;
    }


    /**
     * PrepayRule节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PrepayRule {
        /**
         * 规则编号
         */
        @NotNull
        @JsonProperty("PrepayRuleId")
        private Integer prepayRuleId;

        /**
         * 描述
         */
        @NotNull
        @JsonProperty("Description")
        private String description;

        /**
         * 日期类型
         * BookDay:预定日期
         * CheckInDay：入住日期
         * StayDay:在店日期
         */
        @NotNull
        @JsonProperty("DateType")
        private DateType dateType;

        /**
         * 开始日期
         */
        @Nullable
        @JsonProperty("StartDate")
        private LocalDateTime startDate;

        /**
         * 结束日期
         */
        @Nullable
        @JsonProperty("EndDate")
        private LocalDateTime endDate;

        /**
         * 周有效设置
         */
        @Nullable
        @JsonProperty("WeekSet")
        private String weekSet;

        /**
         * 变更规则
         * PrepayNoChange：不允许变更取消；
         * PrepayNeedSomeDay：在到店当日24点前Hour小时前按规则看是否可以免费变更取消（一般是不收罚金），在Hour和Hour2之间按规则存在罚金，Hour2之后不能变更取消；
         * PrepayNeedOneTime：在约定日期时间点(DateNum + Time)前可以免费变更取消
         */
        @NotNull
        @JsonProperty("ChangeRule")
        private PrepayChangeRule changeRule;

        /**
         * 第一阶段提前的几小时
         * 用于PrepayNeedSomeDay
         */
        @Nullable
        @JsonProperty("Hour")
        private Integer hour;

        /**
         * 第二阶段提前的几小时
         */
        @Nullable
        @JsonProperty("Hour2")
        private Integer hour2;

        /**
         * 具体取消时间日期部分
         * 用于PrepayNeedOneTime
         */
        @Nullable
        @JsonProperty("DateNum")
        private LocalDateTime dateNum;

        /**
         * 具体取消时间小时部分
         */
        @Nullable
        @JsonProperty("Time")
        private LocalTime time;

        /**
         * 在变更时间点前是否扣费
         * 用于 PrepayNeedSomeDay的Hour前扣款类型（一般不收罚金）。
         * DeductFeesBefore为1表示扣费，0表示不扣费。
         */
        @Nullable
        @JsonProperty("DeductFeesBefore")
        private Integer deductFeesBefore;

        /**
         * 时间点前扣费的金额或比例
         */
        @Nullable
        @JsonProperty("DeductNumBefore")
        private BigDecimal deductNumBefore;

        /**
         * 时间点后扣款类型
         * Money：金额
         * Percent：比例
         * FristNight：首晚
         * 用于PrepayNeedOneTime
         */
        @Nullable
        @JsonProperty("CashScaleFirstAfter")
        private CashScale cashScaleFirstAfter;

        /**
         * 在变更时间点后是否扣费
         * 用于 PrepayNeedSomeDay的Hour到Hour2之间的扣款类型。
         * DeductFeesAfter为1表示扣费，0表示不扣费。
         */
        @Nullable
        @JsonProperty("DeductFeesAfter")
        private Integer deductFeesAfter;

        /**
         * 时间点后扣费的金额或比例
         */
        @Nullable
        @JsonProperty("DeductNumAfter")
        private BigDecimal deductNumAfter;

        /**
         * 时间点前扣款类型
         * Money：金额
         * Percent：比例
         * FristNight：首晚
         */
        @Nullable
        @JsonProperty("CashScaleFirstBefore")
        private CashScale cashScaleFirstBefore;
    }

    /**
     * PrepayResult节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PrepayResult {

        /**
         * 取消规则
         */
        @NotNull
        @JsonProperty("CancelDescription")
        private String cancelDescription;

        /**
         * 取消规则明细
         * 参考LadderParse节点
         */
        @NotNull
        @JsonProperty("LadderParseList")
        private List<ELongHotelListResp.Hotel.Room.RatePlan.LadderParse> ladderParseList;
    }

    /**
     * GuaranteeResult节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GuaranteeResult {

        /**
         * 取消规则
         */
        @NotNull
        @JsonProperty("CancelDescription")
        private String cancelDescription;

        /**
         * 是否需要担保
         */
        @NotNull
        @JsonProperty("NeedGuarantee")
        private Boolean needGuarantee;

        /**
         * 担保的金额
         */
        @Nullable
        @JsonProperty("GuaranteeMoney")
        private Double guaranteeMoney;

        /**
         * 取消规则明细
         * 参考LadderParse节点
         */
        @Nullable
        @JsonProperty("LadderParseList")
        private List<ELongHotelListResp.Hotel.Room.RatePlan.LadderParse> ladderParseList;
    }

    /**
     * RefundDetail节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RefundDetail {
        /**
         * 总的退款金额
         */
        @NotNull
        @JsonProperty("refundAmount")
        private Double refundAmount;

        /**
         * 每笔退款明细
         * 参考Refund节点
         */
        @NotNull
        @JsonProperty("refundDetails")
        private List<Refund> refundDetails;

        /**
         * Refund节点
         */
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class Refund {
            /**
             * 每笔退款金额
             */
            @NotNull
            @JsonProperty("refundAmount")
            private BigDecimal refundAmount;

            /**
             * 每笔退款的时间
             */
            @NotNull
            @JsonProperty("refundTime")
            private LocalDateTime refundTime;
        }
    }

    /**
     * GiftPackage节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GiftPackage {

        /**
         * 礼包套餐ID
         * 关联RatePlan.PkgProductids
         */
        @NotNull
        @JsonProperty("PkgProductId")
        private Long pkgProductId;

        /**
         * 礼包套餐类型
         * 0：礼包，1：套餐
         */
        @NotNull
        @JsonProperty("PkgType")
        private Integer pkgType;

        /**
         * 礼包套餐名字
         */
        @Nullable
        @JsonProperty("PkgProductName")
        private String pkgProductName;

        /**
         * 礼包套餐特别说明
         */
        @Nullable
        @JsonProperty("RuleDescriptionAdditional")
        private String ruleDescriptionAdditional;

        /**
         * 礼包套餐图片
         * 参考Picture节点
         */
        @Nullable
        @JsonProperty("Pictures")
        private List<Picture> pictures;

        /**
         * X产品列表
         * 参考XProduct节点
         */
        @Nullable
        @JsonProperty("XProducts")
        private List<XProduct> xProducts;

        /**
         * Picture节点
         */
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class Picture {

            /**
             * 礼包图片顺序
             */
            @NotNull
            @JsonProperty("ImgIndex")
            private Integer imgIndex;

            /**
             * 礼包图片链接
             */
            @NotNull
            @JsonProperty("ImgUrl")
            private String imgUrl;
        }

        /**
         * XProduct节点
         */
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class XProduct {
            /**
             * X产品ID
             * X产品即一个具体的礼包
             */
            @NotNull
            @JsonProperty("XProductId")
            private Long xProductId;

            /**
             * X产品名字
             */
            @NotNull
            @JsonProperty("XProductName")
            private String xProductName;

            /**
             * X产品类型
             */
            @Nullable
            @JsonProperty("TypeName")
            private String typeName;

            /**
             * X产品数量
             */
            @Nullable
            @JsonProperty("Quantity")
            private String quantity;

            /**
             * X产品接待时间
             */
            @Nullable
            @JsonProperty("ReceptionTimes")
            private String receptionTimes;

            /**
             * X产品适用人数
             */
            @Nullable
            @JsonProperty("Capacity")
            private String capacity;

            /**
             * X产品预订电话
             */
            @Nullable
            @JsonProperty("BookingPhone")
            private String bookingPhone;

            /**
             * X产品预订规则
             */
            @Nullable
            @JsonProperty("AppointPolicy")
            private String appointPolicy;
        }
    }

    /**
     * OrderHotel节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OrderHotel {

        /**
         * 酒店ID
         */
        @NotNull
        @JsonProperty("HotelId")
        private String hotelId;

        /**
         * 酒店名称
         */
        @NotNull
        @JsonProperty("Name")
        private String name;

        /**
         * 酒店地址
         */
        @NotNull
        @JsonProperty("Address")
        private String address;

        /**
         * 酒店电话
         */
        @Nullable
        @JsonProperty("Phone")
        private String phone;

        /**
         * 城市名称
         */
        @NotNull
        @JsonProperty("CityName")
        private String cityName;

        /**
         * 酒店英文名
         */
        @Nullable
        @JsonProperty("HotelEnglishName")
        private String hotelEnglishName;

        /**
         * 国家名称
         */
        @Nullable
        @JsonProperty("HotelCountryName")
        private String hotelCountryName;

        /**
         * 维度
         */
        @Nullable
        @JsonProperty("HotelLatitude")
        private BigDecimal hotelLatitude;

        /**
         * 经度
         */
        @Nullable
        @JsonProperty("HotelLongitude")
        private BigDecimal hotelLongitude;
    }

    /**
     * DayMeal节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DayMeal {

        /**
         * 餐食的日期，格式为yyyy-MM-dd，例如2021-08-12
         */
        @NotNull
        @JsonProperty("date")
        private String date;

        /**
         * 是否使用动态餐食，为true取dynamicMealDesc；为false取breakfastDesc、lunchDesc、dinnerDesc
         */
        @NotNull
        @JsonProperty("useDynamicMeal")
        private Boolean useDynamicMeal;

        /**
         * 动态餐食描述
         */
        @Nullable
        @JsonProperty("dynamicMealDesc")
        private String dynamicMealDesc;

        /**
         * 早餐数量
         */
        @Nullable
        @JsonProperty("breakfastShare")
        private Integer breakfastShare;

        /**
         * 早餐描述
         */
        @Nullable
        @JsonProperty("breakfastDesc")
        private String breakfastDesc;

        /**
         * 午餐数量
         */
        @Nullable
        @JsonProperty("lunchShare")
        private Integer lunchShare;

        /**
         * 午餐描述
         */
        @Nullable
        @JsonProperty("lunchDesc")
        private String lunchDesc;

        /**
         * 晚餐数量
         */
        @Nullable
        @JsonProperty("dinnerShare")
        private Integer dinnerShare;

        /**
         * 晚餐描述
         */
        @Nullable
        @JsonProperty("dinnerDesc")
        private String dinnerDesc;

        /**
         * 当天餐食描述
         */
        @Nullable
        @JsonProperty("dayMealDesc")
        private String dayMealDesc;
    }

    /**
     * BasePrepayRule节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BasePrepayRule {

        /**
         * 描述
         * 国际特有字段
         */
        @Nullable
        @JsonProperty("Description")
        private String description;
    }

    /**
     * BoardInfo节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BoardInfo {

        /**
         * 是否包含早餐
         * 国际特有字段
         */
        @Nullable
        @JsonProperty("IsBreakfastIncluded")
        private Boolean isBreakfastIncluded;

        /**
         * 是否半膳
         * 国际特有字段
         */
        @Nullable
        @JsonProperty("halfBoardIncluded")
        private Boolean halfBoardIncluded;

        /**
         * 是否全膳
         * 国际特有字段
         */
        @Nullable
        @JsonProperty("fullBoardIncluded")
        private Boolean fullBoardIncluded;

        /**
         * 膳食描述
         * 国际特有字段
         */
        @Nullable
        @JsonProperty("BoardDesc")
        private String boardDesc;

        /**
         * 膳食明细
         * 国际特有字段
         */
        @Nullable
        @JsonProperty("boardDetails")
        private List<BoardDetail> boardDetails;

        /**
         * BoardDetail节点
         */
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class BoardDetail {

            /**
             * 描述
             * 国际特有字段
             */
            @Nullable
            @JsonProperty("Description")
            private String description;

            /**
             * 膳食数量
             * 国际特有字段
             */
            @Nullable
            @JsonProperty("Count")
            private Integer count;

            /**
             * 膳食类型
             * 1：早餐；2：午餐；3：晚餐；0：未知餐型
             * 国际特有字段
             */
            @Nullable
            @JsonProperty("type")
            private Integer type;
        }
    }

    /**
     * AdditionalTax节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AdditionalTax {

        /**
         * 另付税和服务费总额
         */
        @Nullable
        @JsonProperty("TotalAmountRmb")
        private BigDecimal totalAmountRmb;

        /**
         * 另付税和服务费明细
         */
        @Nullable
        @JsonProperty("AdditionalTaxItems")
        private List<AdditionalTaxItem> additionalTaxItems;

        /**
         * AdditionalTaxItem节点
         */
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class AdditionalTaxItem {

            /**
             * 另付税和服务费明细描述
             */
            @Nullable
            @JsonProperty("Description")
            private String description;

            /**
             * 另付税和服务费明细金额
             */
            @Nullable
            @JsonProperty("Amount")
            private BigDecimal amount;
        }
    }
}
