package com.somytrip.entity.hotel.ELong.offline;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.Nullable;

import java.util.List;

/**
 * @ClassName: ELongHotelStaticGradeResp
 * @Description: 同程艺龙酒店静态点评响应体
 * @Author: shadow
 * @Date: 2024/1/27 17:07
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ELongStaticHotelGradeResp {

    /**
     * 点评
     */
    @Nullable
    @JsonProperty("Review")
    private Review review;

    /**
     * 评分
     */
    @Nullable
    @JsonProperty("Grade")
    private Grade grade;

    /**
     * 点评标签
     */
    @Nullable
    @JsonProperty("CommentTags")
    private List<CommentTag> commentTags;

    /**
     * 点评节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Review {

        /**
         * 点评总数
         */
        @JsonProperty("ReviewCount")
        private Integer reviewCount;

        /**
         * 点评好评数
         */
        @JsonProperty("ReviewGoodsCount")
        private Integer reviewGoodsCount;

        /**
         * 差评数
         */
        @JsonProperty("ReviewPoorCount")
        private Integer reviewPoorCount;

        /**
         * 评分
         * 已经计算为百分比。周边酒店的评价评分可以自行通过酒店经纬度计算周边酒店，再计算平均评分
         */
        @JsonProperty("ReviewScore")
        private String reviewScore;
    }

    /**
     * 评分节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Grade {

        /**
         * 订单30分钟内确认率
         */
        @JsonProperty("ConfirmRateOf30Minute")
        private Integer confirmRateOf30Minute;

        /**
         * 分销过去90天产量得分
         */
        @JsonProperty("GradeOfOrderCountB90")
        private Integer gradeOfOrderCountB90;

        /**
         * C端过去90天产量得分
         */
        @JsonProperty("GradeOfOrderCountC90")
        private Integer gradeOfOrderCountC90;

        /**
         * 产品过去30天可卖得分
         */
        @JsonProperty("GradeOfProd30")
        private Integer gradeOfProd30;

        /**
         * 分销过去90天可定成功率
         */
        @JsonProperty("ValRateOf90")
        private Integer valRateOf90;
    }

    /**
     * 点评标签节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CommentTag {

        /**
         * 标签名称
         */
        @JsonProperty("TagName")
        private String tagName;

        /**
         * 标签热度
         */
        @JsonProperty("HeatValue")
        private Integer heatValue;
    }
}
