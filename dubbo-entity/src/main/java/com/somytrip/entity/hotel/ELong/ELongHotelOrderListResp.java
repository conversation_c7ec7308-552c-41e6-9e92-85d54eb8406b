package com.somytrip.entity.hotel.ELong;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.somytrip.entity.enums.hotel.CustomerType;
import com.somytrip.entity.enums.hotel.OrderConfirmationType;
import com.somytrip.entity.enums.hotel.PaymentType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName: ELongHotelOrderListResp
 * @Description: 同程艺龙酒店订单列表响应
 * @Author: shadow
 * @Date: 2024/2/24 11:19
 */
@Slf4j
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ELongHotelOrderListResp {

    /**
     * 总订单数
     */
    @NotNull
    @JsonProperty("Count")
    private Integer count;

    /**
     * 订单数组
     */
    @Nullable
    @JsonProperty("Orders")
    private List<Order> orders;

    /**
     * Order节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Order {
        /**
         * 订单编号
         */
        @NotNull
        @JsonProperty("OrderId")
        private Long orderId;

        /**
         * 状态
         */
        @NotNull
        @JsonProperty("Status")
        private String status;

        /**
         * 总价
         */
        @NotNull
        @JsonProperty("TotalPrice")
        private BigDecimal totalPrice;

        /**
         * 货币类型
         */
        @NotNull
        @JsonProperty("CurrencyCode")
        private String currencyCode;

        /**
         * 酒店编号
         */
        @NotNull
        @JsonProperty("HotelId")
        private String hotelId;

        /**
         * 酒店名称
         */
        @Nullable
        @JsonProperty("HotelName")
        private String hotelName;

        /**
         * 房型编号
         */
        @NotNull
        @JsonProperty("RoomTypeId")
        private String roomTypeId;

        /**
         * 房型名称
         */
        @Nullable
        @JsonProperty("RoomTypeName")
        private String roomTypeName;

        /**
         * 产品编号
         */
        @NotNull
        @JsonProperty("RatePlanId")
        private Integer ratePlanId;

        /**
         * 产品名称
         */
        @Nullable
        @JsonProperty("RatePlanName")
        private String ratePlanName;

        /**
         * 入住日期
         */
        @NotNull
//        @JSONField(format = "yyyy-MM-dd")
        @JsonProperty("ArrivalDate")
        private LocalDateTime arrivalDate;

        /**
         * 离店日期
         */
        @NotNull
//        @JSONField(format = "yyyy-MM-dd")
        @JsonProperty("DepartureDate")
        private LocalDateTime departureDate;

        /**
         * 客人类型
         * All = 统一价；
         * Chinese = 内宾价，需提示客人“须持大陆身份证入住”；
         * OtherForeign =外宾价，需提示客人“须持国外护照入住”；
         * HongKong = 港澳台客人价，需提示客人“须持港澳台身份证入住”；
         * ChinaGuest = 中宾价，需提示客人“须持中国身份证、护照入住”；
         */
        @NotNull
        @JsonProperty("CustomerType")
        private CustomerType customerType;

        /**
         * 房间数量
         */
        @NotNull
        @JsonProperty("NumberOfRooms")
        private Integer numberOfRooms;

        /**
         * 客人数量
         */
        @NotNull
        @JsonProperty("NumberOfCustomers")
        private Integer numberOfCustomers;

        /**
         * 付款类型
         * SelfPay-前台现付、Prepay-预付
         */
        @NotNull
        @JsonProperty("PaymentType")
        private PaymentType paymentType;

        /**
         * 最早到店时间
         */
        @NotNull
        @JsonProperty("EarliestArrivalTime")
        private LocalDateTime earliestArrivalTime;

        /**
         * 最晚到店时间
         */
        @NotNull
        @JsonProperty("LatestArrivalTime")
        private LocalDateTime latestArrivalTime;

        /**
         * 确认类型
         * NotAllowedConfirm -- 不允许确认(合作伙伴自查订单状态后自行联系客人)
         * SMS_cn -- 艺龙发短信给客人,出现订单问题的时候会主动联系
         * NoNeed -- 艺龙发短信给客人,出现订单问题的时候不主动联系
         * 注：除了NotAllowedConfirm，其余的选项艺龙都会发送短信，下单时如果输入了邮箱那么都会发送邮件
         */
        @NotNull
        @JsonProperty("ConfirmationType")
        private OrderConfirmationType confirmationType;

        /**
         * 给酒店备注
         */
        @Nullable
        @JsonProperty("NoteToHotel")
        private String noteToHotel;

        /**
         * 给艺龙备注
         */
        @Nullable
        @JsonProperty("NoteToElong")
        private String noteToElong;
    }
}
