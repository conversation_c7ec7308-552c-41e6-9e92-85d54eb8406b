package com.somytrip.entity.hotel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.somytrip.entity.enums.hotel.HotelOrigin;
import com.somytrip.entity.vo.hotel.CancelPolicyVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @projectName: hotel-service
 * @package: com.somytrip.entity.hotel
 * @className: HotelMinRateEntity
 * @author: shadow
 * @description: 酒店每日最低价Entity
 * @date: 2024/4/1 16:08
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "hotel_min_rate", autoResultMap = true)
public class HotelMinRateEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 561738783943468068L;

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 酒店主键ID
     */
    private Long hotelGeneralId;

    /**
     * 酒店来源
     */
    private HotelOrigin hotelOrigin;

    /**
     * 酒店ID
     */
    private String hotelId;

    /**
     * 城市code
     */
    private String cityCode;

    /**
     * 当日(date)最低价
     */
    private BigDecimal minRate;

    /**
     * 取消政策
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private CancelPolicyVo cancelPolicy;

    /**
     * 日期
     */
    private LocalDate date;

    /**
     * 价格时间
     */
    private LocalDateTime rateTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
