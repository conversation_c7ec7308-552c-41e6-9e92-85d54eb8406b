package com.somytrip.entity.hotel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.somytrip.entity.hotel.ELong.offline.ELongStaticCityResp;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 同程艺龙城市映射表(CityMappingElong)实体类
 *
 * <AUTHOR>
 * @since 2024-02-05 14:40:37
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "city_mapping_elong", autoResultMap = true)
public class CityMappingELongEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 561738783943468018L;

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 统一城市唯一标识
     */
    private String cityCode;
    /**
     * 城市ID
     */
    private String cityId;
    /**
     * 城市名称
     */
    private String cityName;
    /**
     * 城市英文名称
     */
    private String cityNameEn;
    /**
     * 城市经度，高德坐标系经纬度
     */
    private String cityLongitude;
    /**
     * 城市纬度，高德坐标系经纬度
     */
    private String cityLatitude;
    /**
     * 城市父节点ID
     */
    private String cityParentId;
    /**
     * 省份ID
     */
    private String provinceId;
    /**
     * 省份名称
     */
    private String provinceName;
    /**
     * 省份英文名称
     */
    private String provinceNameEn;
    /**
     * 国家ID
     */
    private String countryId;
    /**
     * 国家编码
     */
    private String countryCode;
    /**
     * 国家名称
     */
    private String countryName;
    /**
     * 国家英文名称
     */
    private String countryNameEn;
    /**
     * 地标，包含地标ID、地标名称、地标英文名称、地标类型
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<CityLocationEntity> locations;
    /**
     * 是否可用
     */
    private boolean isActive;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public CityMappingELongEntity(ELongStaticCityResp.City elongCity) {
        this.cityId = elongCity.getCityId();
        this.cityName = elongCity.getCityName();
        this.cityNameEn = elongCity.getCityNameEn();
        this.cityLongitude = elongCity.getCityLongitude();
        this.cityLatitude = elongCity.getCityLatitude();
        this.cityParentId = elongCity.getCityParentId();
        this.provinceId = elongCity.getProvinceId();
        this.provinceName = elongCity.getProvinceName();
        this.provinceNameEn = elongCity.getProvinceNameEn();
        this.countryId = elongCity.getCountryId();
        this.countryCode = elongCity.getCountryCode();
        this.countryName = elongCity.getCountryName();
        this.countryNameEn = elongCity.getCountryNameEn();
        if (elongCity.getLocations() != null) {
            this.locations = elongCity.getLocations().stream().map(CityLocationEntity::new).collect(Collectors.toList());
        }
    }

    public boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(boolean active) {
        isActive = active;
    }

    @Data
    public static class CityLocationEntity {

        /**
         * LocationID
         */
        private String locationId;

        /**
         * Location中文名称
         */
        private String locationName;

        /**
         * Location英文名称
         */
        private String locationNameEn;

        /**
         * Location类型(1:行政区 2:商圈 3:标示物)
         */
        private Integer locationType;

        public CityLocationEntity(ELongStaticCityResp.Location elongLocation) {
            this.locationId = elongLocation.getLocationId();
            this.locationName = elongLocation.getLocationName();
            this.locationNameEn = elongLocation.getLocationNameEn();
            this.locationType = elongLocation.getLocationType();
        }
    }
}

