package com.somytrip.entity.hotel.WEBBEDS;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName: WEBBEDSPricesResp
 * @Description: WEBBEDS酒店价格响应
 * @Author: shadow
 * @Date: 2024/3/19 3:50
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WEBBEDSPricesResp {

    /**
     * 价格列表
     */
    private List<Price> prices;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Price {

        /**
         * 房型ID
         */
        private Long id;

        /**
         * 房型名称
         */
        private String name;

        /**
         * 有价价格计划列表
         */
        @JsonProperty("price_items")
        private List<PriceItem> priceItems;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PriceItem {

        /**
         * 价格计划ID
         */
        @JsonProperty("room_id")
        private Long roomId;

        /**
         * 总价
         */
        private BigDecimal price;

        /**
         * 每日价格列表
         */
        @JsonProperty("daily_prices")
        private List<DailyPrice> dailyPrices;

        /**
         * 取消政策列表
         */
        private List<Policy> policies;

        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class DailyPrice {

            /**
             * 日期
             */
            private String date;

            /**
             * 价格
             */
            private BigDecimal price;

            /**
             * 币种
             */
            private String currency;

            /**
             * 餐食类型
             */
            @JsonProperty("meal_type")
            private Long mealType;

            /**
             * 餐食数量
             */
            @JsonProperty("meal_count")
            private Long mealCount;

            /**
             * 餐食名称
             */
            @JsonProperty("meal_name")
            private String mealName;
        }

        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class Policy {

            /**
             * 本条取消政策开始时间
             */
            @JsonProperty("from_date")
            private String fromDate;

            /**
             * 本条取消政策结束时间
             */
            @JsonProperty("to_date")
            private String toDate;

            /**
             * 取消费用
             */
            @JsonProperty("cancel_charge")
            private Long cancelCharge;
        }
    }
}
