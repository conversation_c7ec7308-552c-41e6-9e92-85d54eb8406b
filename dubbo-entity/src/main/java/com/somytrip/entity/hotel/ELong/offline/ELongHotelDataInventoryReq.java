package com.somytrip.entity.hotel.ELong.offline;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.time.LocalDate;

/**
 * @ClassName: ELongHotelDataInventory
 * @Description: 同程艺龙静态酒店库存信息请求参数
 * @Author: shadow
 * @Date: 2024/2/20 9:59
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ELongHotelDataInventoryReq {

    /**
     * 酒店编号
     * 最多10个,逗号分隔
     */
    @NotNull
    @JsonProperty("HotelIds")
    private String hotelIds;

    /**
     * 酒店编码
     * 最多10个,逗号分隔
     */
    @Nullable
    @JsonProperty("HotelCodes")
    private String hotelCodes;

    /**
     * 供应商房型编号
     * 加入了RoomTypeId的过滤返回结果可能对应多个HotelCode的还需要核对使用哪个HotelCode。
     */
    @Nullable
    @JsonProperty("RoomTypeId")
    private String roomTypeId;

    /**
     * 开始日期
     * 返回的结果中开始日期和结束日期间只要有一天在此范围内就会返回, 使用yyyy-MM-dd格式，例如:2022-12-09
     */
    @NotNull
    @JsonProperty("StartDate")
    private LocalDate startDate;

    /**
     * 结束日期
     * 使用yyyy-MM-dd格式，例如:2022-12-09
     */
    @NotNull
    @JsonProperty("EndDate")
    private LocalDate endTime;

    /**
     * 是否返回即时确认数据
     * 建议不使用，返回速度会变慢。
     */
    @Nullable
    @JsonProperty("IsNeedInstantConfirm")
    private Boolean isNeedInstantConfirm;
}
