package com.somytrip.entity.hotel.ELong;

import lombok.Data;

/**
 * @ClassName: ELongPublicRequestBody
 * @Description: 同程艺龙公共请求体
 * @Author: shadow
 * @Date: 2024/1/25 14:54
 */
@Data
public class ELongPublicRequestBody {

    /**
     * 账户名
     */
    private String user;

    /**
     * 访问的方法名
     */
    private String method;

    /**
     * Unix时间戳
     */
    private String timestamp;

    /**
     * json
     */
    private String format;

    /**
     * 业务参数，URLEncode
     */
    private String data;

    /**
     * 数据签名 签名方法：md5(timestamp + md5(data + appkey) + secretKey)
     */
    private String signature;
}
