package com.somytrip.entity.hotel.WEBBEDS;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @ClassName: WEBBEDSRoomTypeReq
 * @Description: WEBBEDS房型请求参数
 * @Author: shadow
 * @Date: 2024/3/19 3:28
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WEBBEDSRoomTypesReq extends WEBBEDSBaseReq {

    /**
     * 酒店ID
     */
    @JsonProperty("hotel_id")
    private Integer hotelId;
}
