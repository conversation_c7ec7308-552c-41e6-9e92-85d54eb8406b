package com.somytrip.entity.hotel.WEBBEDS;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName: WEBBEDSRoomTypeResp
 * @Description: WEBBEDS房型响应
 * @Author: shadow
 * @Date: 2024/3/19 3:29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WEBBEDSRoomTypesResp {

    /**
     * 房型列表
     */
    private List<RoomType> roomTypes;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RoomType {

        /**
         * 房型ID
         */
        private Integer id;

        /**
         * 房型名称
         */
        private String name;

        /**
         * 楼层
         */
        private String floor;

        /**
         * 面积
         */
        private String area;

        /**
         * 房间数量
         */
        @JsonProperty("room_quantity")
        private Integer roomQuantity;

        /**
         * 加床类型
         */
        @JsonProperty("extra_bed")
        private Integer extraBed;

        /**
         * 窗户类型ID
         */
        @JsonProperty("window_type")
        private Integer windowType;

        /**
         * 窗户类型名称
         */
        @JsonProperty("window_type_name")
        private String windowTypeName;

        /**
         * 房型图片列表
         */
        private List<String> images;
    }
}
