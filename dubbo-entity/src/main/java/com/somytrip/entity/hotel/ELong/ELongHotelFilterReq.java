package com.somytrip.entity.hotel.ELong;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;

/**
 * @ClassName: ELongFilterReq
 * @Description: 同程艺龙筛选项信息请求参数
 * @Author: shadow
 * @Date: 2024/3/1 10:45
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ELongHotelFilterReq {

    /**
     * 城市ID
     * 城市ID，可以从hotel.destination接口或者GEO静态数据中获取。
     */
    @NotNull
    @JsonProperty("CityId")
    private String cityId;

    /**
     * 获取热门筛选项
     * true-获取热门筛选项
     * <p>
     * false-获取所有筛选项
     */
    @NotNull
    @JsonProperty("HotFilter")
    private Boolean hotFilter;

    /**
     * 获取筛选项类型
     * 指定获取那种类型的筛选项，多个类型时使用逗号隔开。
     * 3-品牌
     * 4-行政区
     * 5-商圈
     * 6-POI（大学、风景区、汽车站、火车站、地铁等）
     * 1007-支付类型
     * 1008-星级
     * 1011-设施
     * 1012-主题
     * 1016-酒店特色
     * 1020-评分
     */
    @NotNull
    @JsonProperty("FilterType")
    private String filterType;
}
