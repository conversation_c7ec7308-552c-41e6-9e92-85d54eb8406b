package com.somytrip.entity.hotel.ELong;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @ClassName: ELongHotelOrderUpdateReq
 * @Description: 同程艺龙酒店修改订单请求参数
 * @Author: shadow
 * @Date: 2024/2/24 15:06
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ELongHotelOrderUpdateReq {

    /**
     * 订单编号
     */
    @NotNull
    @JsonProperty("OrderId")
    private Long orderId;

    /**
     * 房型编号
     */
    @NotNull
    @JsonProperty("RoomTypeId")
    private String roomTypeId;

    /**
     * 产品编号
     * 必须为现付产品。变更RatePlan时只能变更为同一hotelcode下的RatePlan。
     */
    @NotNull
    @JsonProperty("RatePlanId")
    private Integer ratePlanId;

    /**
     * 入住日期
     */
    @NotNull
    @JsonProperty("ArrivalDate")
    private LocalDate arrivalDate;

    /**
     * 离店日期
     */
    @NotNull
    @JsonProperty("DepartureDate")
    private LocalDate departureDate;

    /**
     * 最早到店时间
     */
    @JsonProperty("EarliestArrivalTime")
    private LocalDateTime earliestArrivalTime;

    /**
     * 最晚到店时间
     */
    @JsonProperty("LatestArrivalTime")
    private LocalDateTime latestArrivalTime;

    /**
     * 房间数量
     */
    @NotNull
    @JsonProperty("NumberOfRooms")
    private Integer numberOfRooms;

    /**
     * 客人数量
     */
    @NotNull
    @JsonProperty("NumberOfCustomers")
    private Integer numberOfCustomers;

    /**
     * 订单总价
     * 校验传入的总价和系统修改后价格，若不相等则拒绝本次订单修改
     */
    @NotNull
    @JsonProperty("TotalPrice")
    private BigDecimal totalPrice;

    /**
     * 联系人
     */
    @Nullable
    @JsonProperty("Contact")
    private ELongHotelOrderDetailResp.Contact contact;

    /**
     * 是否强制担保
     * 版本V1.08新增。
     * 强制担保对应的担保金额是首晚，并且不可变更取消。
     * 建议不要使用该字段。一般是产品无担保规则，酒店对某个特定订单要求临时增加担保的时候使用。客服会将设置强制担保属性，代理使用hotel.order.pay接口支付即可。
     */
    @Nullable
    @JsonProperty("IsForceGuarantee")
    private Boolean isForceGuarantee;

    /**
     * 是否已担保或已付款
     * 开通了公司担保业务的合作伙伴才能使用该属性
     */
    @NotNull
    @JsonProperty("IsGuaranteeOrCharged")
    private Boolean isGuaranteeOrCharged;

    /**
     * 信用卡
     */
    @Nullable
    @JsonProperty("CreditCard")
    private ELongHotelOrderCreateReq.CreditCard creditCard;

    /**
     * 第三方支付
     */
    @Nullable
    @JsonProperty("DoveCorpCard")
    private ELongHotelOrderCreateReq.DoveCorpCard doveCorpCard;

    /**
     * 预订的房间数
     */
    @NotNull
    @JsonProperty("OrderRooms")
    private ELongHotelOrderCreateReq.OrderRoom[] orderRooms;

    /**
     * 客人对酒店的特殊要求
     * 更新订单的时候，先看看原来填写了什么内容。这里传入的内容将追加在原内容之后。
     */
    @Nullable
    @JsonProperty("NoteToHotel")
    private String noteToHotel;
}
