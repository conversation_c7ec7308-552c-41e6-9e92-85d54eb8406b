package com.somytrip.entity.hotel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.somytrip.entity.enums.hotel.DateType;
import com.somytrip.entity.enums.hotel.HotelOrigin;
import com.somytrip.entity.enums.hotel.HourType;
import com.somytrip.entity.enums.hotel.WayOfGiving;
import com.somytrip.entity.hotel.ELong.offline.ELongHotelDataRpResp;
import com.somytrip.utils.LocalDateTimeUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.type.JdbcType;
import org.jetbrains.annotations.NotNull;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * (HotelGifts)实体类
 *
 * <AUTHOR>
 * @since 2024-02-19 17:07:38
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "hotel_gifts", autoResultMap = true)
public class HotelGiftEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = -50328465422545970L;

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 酒店来源
     */
    private HotelOrigin hotelOrigin;
    /**
     * 酒店ID
     */
    private String hotelId;
    /**
     * 送礼编号
     */
    private Integer giftId;
    /**
     * 礼包副标题
     */
    private String giftDescription;
    /**
     * 描述
     */
    private String description;
//    /**
//     * 开始时间
//     */
//    private LocalDateTime startDate;
//    /**
//     * 结束时间
//     */
//    private LocalDateTime endDate;
    /**
     * 有效日期列表
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<EffectiveDateEntity> effectiveDates;
    /**
     * 日期类型，CheckinDate:入住日, BookingDate:预订日, StayDate:在店日
     */
    private DateType dateType;
    /**
     * 星期设置
     */
    private String weekSet;
    /**
     * 活动内容
     */
    private String giftContent;
    /**
     * 送礼类型
     */
    private String giftTypes;
    /**
     * 新送礼类型
     */
    @TableField(typeHandler = JacksonTypeHandler.class, jdbcType = JdbcType.JAVA_OBJECT)
    private List<GiftInfoEntity> giftInfos;
    /**
     * 小时数
     */
    private Integer hourNumber;
    /**
     * 小时数的类型
     */
    private HourType hourType;
    /**
     * 送礼方式
     */
    private WayOfGiving wayOfGiving;
    /**
     * 其他送礼具体方式
     */
    private String wayOfGivingOther;
    /**
     * 礼包价值
     */
    private BigDecimal giftValue;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public HotelGiftEntity(ELongHotelDataRpResp.Hotel.Gift elongGift, String hotelId) {
        this.hotelOrigin = HotelOrigin.ELong;
        this.hotelId = hotelId;
        this.giftId = elongGift.getHotelGiftId();
        this.giftDescription = elongGift.getGiftDescription();
        this.description = elongGift.getDescription();
        if (elongGift.getEffectiveDates() != null) {
            List<EffectiveDateEntity> effectiveDateEntityList = new ArrayList<>();
            for (ELongHotelDataRpResp.Hotel.Gift.EffectiveDate effectiveDate : elongGift.getEffectiveDates()) {
                effectiveDateEntityList.add(new EffectiveDateEntity(effectiveDate));
            }
            this.effectiveDates = effectiveDateEntityList;
        }
//        this.startDate = LocalDateTime.parse(elongGift.getStartDate());
//        this.endDate = LocalDateTime.parse(elongGift.getEndDate());
        this.dateType = elongGift.getDateType();
        this.weekSet = elongGift.getWeekSet();
        this.giftContent = elongGift.getGiftContent();
        this.giftTypes = elongGift.getGiftTypes();
        if (elongGift.getGiftInfos() != null) {
            List<GiftInfoEntity> giftInfoEntityList = new ArrayList<>();
            for (ELongHotelDataRpResp.Hotel.Gift.GiftInfo giftInfo : elongGift.getGiftInfos()) {
                giftInfoEntityList.add(new GiftInfoEntity(giftInfo));
            }
            this.giftInfos = giftInfoEntityList;
        }
        this.hourNumber = elongGift.getHourNumber();
        this.hourType = elongGift.getHourType();
        this.wayOfGiving = elongGift.getWayOfGiving();
        this.wayOfGivingOther = elongGift.getWayOfGivingOther();
        this.giftValue = elongGift.getGiftValue();
    }

    /**
     * EffectiveDate节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EffectiveDateEntity {

        /**
         * 开始时间
         */
        @JsonProperty("StartDate")
        private String startDate;

        /**
         * 结束时间
         */
        @JsonProperty("EndDate")
        private String endDate;

        public EffectiveDateEntity(ELongHotelDataRpResp.Hotel.Gift.EffectiveDate elongEffectiveDate) {
//            this.startDate = LocalDateTimeUtil.convertISOStr2LocalDate(elongEffectiveDate.getStartDate());
//            this.endDate = LocalDateTimeUtil.convertISOStr2LocalDate(elongEffectiveDate.getEndDate());
            this.startDate = LocalDateTimeUtil.convertISOStr2LocalDate(elongEffectiveDate.getStartDate()).toString();
            this.endDate = LocalDateTimeUtil.convertISOStr2LocalDate(elongEffectiveDate.getEndDate()).toString();
        }
    }

    /**
     * GiftInfo节点
     */
    @Data
    public static class GiftInfoEntity {

        /**
         * 礼包一级编号
         * 1.含礼品
         * 2.延迟退房
         * 3.含餐饮
         * 4.含旅游门票
         * 5.含折扣/抵扣券
         * 6.含交通
         * 7.其他
         */
        @NotNull
        @JsonProperty("GiftInfo")
        private Integer giftInfo;

        /**
         * 二级礼包内容
         */
        @NotNull
        @JsonProperty("GiftSubInfos")
        private List<GiftSubInfoEntity> giftSubInfos;

        public GiftInfoEntity(ELongHotelDataRpResp.Hotel.Gift.GiftInfo elongGiftInfo) {
            this.giftInfo = elongGiftInfo.getGiftInfo();
            List<GiftSubInfoEntity> giftSubInfoEntityList = new ArrayList<>();
            for (ELongHotelDataRpResp.Hotel.Gift.GiftInfo.GiftSubInfo giftSubInfo : elongGiftInfo.getGiftSubInfos()) {
                giftSubInfoEntityList.add(new GiftSubInfoEntity(giftSubInfo));
            }
            this.giftSubInfos = giftSubInfoEntityList;
        }

        /**
         * GiftSubInfo节点
         */
        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class GiftSubInfoEntity {

            /**
             * 礼包二级编号
             * 1.含果盘
             * 3.含水果
             * 4.含饮品
             * 5.含精美艺品
             * 6.其他
             * 7.延迟至13点退房
             * 8.延迟至14点退房
             * 9.延迟至15点退房
             * 10.其他
             * 11.含午餐
             * 12.含晚餐
             * 13.含下午茶
             * 14.含餐券
             * 15.其他
             * 16.含景点门票
             * 17.含演出门票
             * 18.其他
             * 19.含店内折扣/抵扣券
             * 20.含外部折扣/抵扣券
             * 21.其他
             * 22.含接站
             * 23.含接机
             * 24.含送站
             * 25.含送机
             * 26.含景区直通车
             * 27.其他
             * 28.其他
             * 注意：1-6对应一级编号的1，7-10对应一级编号的2，11-15对应一级编号的3，16-18对应一级编号的4，19-21对应一级编号的5，22-27对应一级编号的6，28对应一级编号的7
             */
            @NotNull
            @JsonProperty("SubInfo")
            private Integer subInfo;

            public GiftSubInfoEntity(ELongHotelDataRpResp.Hotel.Gift.GiftInfo.GiftSubInfo elongGiftSubInfo) {
                this.subInfo = elongGiftSubInfo.getSubInfo();
            }
        }
    }
}

