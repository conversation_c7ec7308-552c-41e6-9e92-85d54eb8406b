package com.somytrip.entity.hotel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.somytrip.entity.enums.hotel.HotelOrigin;
import com.somytrip.entity.hotel.ELong.offline.ELongStaticHotelInfoResp;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * (HotelParkInfo)实体类
 *
 * <AUTHOR>
 * @since 2024-01-31 16:57:21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "hotel_park_info", autoResultMap = true)
public class HotelParkInfoEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 972008249416316011L;

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 酒店来源
     */
    private HotelOrigin hotelOrigin;
    /**
     * 酒店ID
     */
    private String hotelId;
    /**
     * 酒店主键ID
     */
    private Long hotelPid;
    /**
     * 类型
     */
    private String type;
    /**
     * 名称
     */
    private String title;
    /**
     * 描述
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> description;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public HotelParkInfoEntity(ELongStaticHotelInfoResp.Detail.ParkInfo elongParkInfo, String hotelId) {
        this.hotelOrigin = HotelOrigin.ELong;
        this.hotelId = hotelId;
        this.type = elongParkInfo.getType();
        this.title = elongParkInfo.getTitle();
        this.description = elongParkInfo.getDesc();
    }
}

