package com.somytrip.entity.hotel.WEBBEDS;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @ClassName: WEBBEDSCancelReq
 * @Description: WEBBEDS取消订单请求参数
 * @Author: shadow
 * @Date: 2024/3/19 4:28
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class WEBBEDSCancelReq extends WEBBEDSBaseReq {

    /**
     * 外部订单号
     */
    @JsonProperty("outer_order_no")
    private String outerOrderNo;
}
