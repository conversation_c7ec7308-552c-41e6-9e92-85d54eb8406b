package com.somytrip.entity.hotel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.somytrip.entity.enums.hotel.HotelOrigin;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.hotel
 * @className: HotelLocaleDescription
 * @author: shadow
 * @description: 酒店国际化(description)实体
 * @date: 2024/9/12 15:37
 * @version: 1.0
 */
@Data
@TableName("hotel_locale_description")
public class HotelLocaleDescriptionEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Long hotelPid;

    private HotelOrigin hotelOrigin;

    private String hotelId;

    @TableField("description_zh_CN")
    private String description_zh_CN;

    @TableField("description_en_US")
    private String description_en_US;

    @TableField("description_zh_TW")
    private String description_zh_TW;
}
