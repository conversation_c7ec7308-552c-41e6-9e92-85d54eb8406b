package com.somytrip.entity.hotel.ELong.offline;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName: ELongHotelIncrShardingRateResp
 * @Description:
 * @Author: shadow
 * @Date: 2024/2/22 16:48
 */
@Data
public class ELongHotelIncrShardingRateResp {

    /**
     * 价格变化集合
     */
    @Nullable
    @JsonProperty("Rates")
    private List<Rate> rates;

    /**
     * Rate节点
     */
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class Rate extends ELongHotelDataRateResp.Rate {

        /**
         * 增量ID
         */
        @NotNull
        @JsonProperty("LastId")
        private Long lastId;

        /**
         * 变化时间
         */
        @NotNull
        @JsonProperty("Time")
        private LocalDateTime time;
    }
}
