package com.somytrip.entity.hotel.ELong;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.somytrip.entity.enums.hotel.PaymentType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @projectName: hotel-service
 * @package: com.somytrip.entity.hotel.ELong
 * @className: ELongHotelRateMinReq
 * @author: shadow
 * @description: 同程艺龙酒店最小价请求参数
 * @date: 2024/4/1 15:17
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ELongHotelRateMinReq {

    /**
     * 入住日期
     */
    @NotNull
    @JsonProperty("ArrivalDate")
    private LocalDate arrivalDate;

    /**
     * 离店日期
     */
    @NotNull
    @JsonProperty("DepartureDate")
    private LocalDate departureDate;

    /**
     * 酒店ID列表
     * 只能输入一个酒店ID
     */
    @NotNull
    @JsonProperty("HotelIds")
    private String hotelIds;

    /**
     * 房型编号
     */
    @Nullable
    @JsonProperty("RoomId")
    private String roomId;

    /**
     * 支付方式
     * 默认为All，All-不限、SelfPay-现付、Prepay-预付
     */
    @Nullable
    @JsonProperty("PaymentType")
    private PaymentType paymentType;

    /**
     * 成人数
     * 国际特有字段
     */
    @NotNull
    @JsonProperty("NumberOfAdults")
    private Integer numberOfAdults;

    /**
     * 房间数量
     * 国际特有字段,暂未使用
     */
    @Nullable
    @JsonProperty("NumberOfRooms")
    private Integer numberOfRooms;

    /**
     * 儿童年龄
     * 国际特有字段
     */
    @Nullable
    @JsonProperty("ChildAges")
    private List<Integer> childAges;

    /**
     * 预付发票模式
     * Elong-艺龙开发票、Hotel-酒店开发票、NoSense-全部
     * 前台自付产品都是酒店开发票，这里的过滤是针对预付产品。
     * 预付订单的发票有两种开具方式：收款方开具或酒店开具。收款方开具发票的，艺龙可提供代开服务，需联系商务开通相关权限。
     */
    @Nullable
    @JsonProperty("InvoiceMode")
    private String invoiceMode;

    /**
     * 最晚到店时间
     * 用于计算现付产品的担保规则结果（GuaranteeResult），
     * 不传时默认最晚到店时间为入住日期+1Day+6Hour（默认兜底逻辑不适用于所有场景，可酌情考虑传入T+2Hour/NHour或者入住日期+18:00等），
     * 请注意保证搜索时此参数与试单和成单时一致，否则对应担保规则结果会不尽相同；
     * 填单页中现付产品需要根据用户选择的最晚到店时间重新获取接口最新的担保规则结果，到店时间更改，
     * 担保类型为到店时间担保的订单取消规则和担保规则均可能发生变化。
     */
    @Nullable
    @JsonProperty("LatestArrivalTime")
    private LocalDateTime latestArrivalTime;

    /**
     * 其他条件
     * 0.酒店最小价
     * 1.房型最小价
     * 2.房型产品信息
     * 3.返回预付产品规则结果、现付产品规则结果(PrepayResult、GuaranteeResult)，对应hotel.detail接口中的Options:12
     * 4.同时返回钟点房和其他产品，对应hotel.detail接口中的Options:11
     * 5.每日价格数组输出未DRR计算的原始价格，对应hotel.detail接口中的Options:5
     * 6.返回增值服务(ValueAdds)
     * 7.返回预付规则(PrepayRules)
     * 8.返回担保规则GuaranteeRules)
     * 9.返回预订规则(BookingRules)
     * 10.返回促销规则(DrrRules)
     * 11.返回礼包、礼包套餐(Gifts、GiftPackages)
     * 建议仅传入0获取酒店最小价信息
     * 如需获取酒店最小价信息及对应的房型产品信息可传入0,2
     * 如需同时获取全日房最小价信息和钟点房最小价信息可传入0,4
     */
    @NotNull
    @JsonProperty("Options")
    private String options;
}
