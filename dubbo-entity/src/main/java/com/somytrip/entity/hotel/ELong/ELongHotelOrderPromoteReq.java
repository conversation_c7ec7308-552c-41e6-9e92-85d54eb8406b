package com.somytrip.entity.hotel.ELong;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;

/**
 * @ClassName: ELongHotelOrderPromoteReq
 * @Description: 同程艺龙酒店订单催确认请求参数
 * @Author: shadow
 * @Date: 2024/2/24 15:17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ELongHotelOrderPromoteReq {

    /**
     * 订单编号
     */
    @NotNull
    @JsonProperty("OrderId")
    private Long orderId;
}
