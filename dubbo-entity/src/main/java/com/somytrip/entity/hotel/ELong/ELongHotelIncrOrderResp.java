package com.somytrip.entity.hotel.ELong;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName: ELongHotelIncrOrderResp
 * @Description: 同程艺龙酒店订单增量响应
 * @Author: shadow
 * @Date: 2024/2/24 14:55
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ELongHotelIncrOrderResp {

    /**
     * 订单增量
     * 包含多个Order节点
     */
    @JsonProperty("Orders")
    private List<Order> orders;

    /**
     * Order节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Order {
        /**
         * 增长id
         */
        @NotNull
        @JsonProperty("LastId")
        private Long lastId;

        /**
         * 变化时间
         */
        @NotNull
        @JsonProperty("Time")
        private LocalDateTime time;

        /**
         * 订单ID
         */
        @NotNull
        @JsonProperty("OrderId")
        private Long orderId;

        /**
         * 订单状态
         * V1.34增加取消状态增量
         */
        @NotNull
        @JsonProperty("Status")
        private String status;

        /**
         * 入住日期
         */
        @NotNull
        @JsonProperty("ArrivalDate")
        private LocalDateTime arrivalDate;

        /**
         * 离店日期
         */
        @NotNull
        @JsonProperty("DepartureDate")
        private LocalDateTime departureDate;

        /**
         * 总价
         */
        @NotNull
        @JsonProperty("TotalPrice")
        private BigDecimal totalPrice;

        /**
         * 房间数量
         */
        @NotNull
        @JsonProperty("NumberOfRooms")
        private Integer numberOfRooms;

        /**
         * 合作伙伴从成单接口传入的订单号
         * V1.29新增
         */
        @Nullable
        @JsonProperty("AffiliateConfirmationId")
        private String affiliateConfirmationId;

        /**
         * 订单退款
         * V1.29新增
         */
        @Nullable
        @JsonProperty("AllRefundAmount")
        private BigDecimal allRefundAmount;

        /**
         * 支付状态
         * V1.29新增
         * -1 -- 无支付信息
         * 1 -- 等待担保或支付
         * 2 -- 担保或支付中
         * 3 -- 担保或支付（或退款）成功
         * 4 -- 担保或支付（或退款）失败
         * 5 -- 暂缓
         */
        @Nullable
        @JsonProperty("PayStatus")
        private Integer payStatus;

        /**
         * 是否即时确认
         * 订单是否为即时确认，此字段为true时，只要订单状态变成V，就代表着订单被确认了。
         */
        @Nullable
        @JsonProperty("IsInstantConfirm")
        private Boolean isInstantConfirm;
    }
}
