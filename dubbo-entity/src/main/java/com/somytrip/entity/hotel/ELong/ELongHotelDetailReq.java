package com.somytrip.entity.hotel.ELong;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.somytrip.entity.enums.hotel.PaymentType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.time.LocalDate;

/**
 * @ClassName: ELongHotelDetailReq
 * @Description: 同程艺龙酒店详情请求参数
 * @Author: shadow
 * @Date: 2024/3/1 16:23
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ELongHotelDetailReq {

    /**
     * 入住日期
     * 使用 yyyy-MM-dd 格式，例如: 2022-12-09
     */
    @NotNull
    @JsonProperty("ArrivalDate")
    private LocalDate arrivalDate;

    /**
     * 离店日期
     * 使用 yyyy-MM-dd 格式，例如: 2022-12-09
     */
    @NotNull
    @JsonProperty("DepartureDate")
    private LocalDate departureDate;

    /**
     * 酒店ID列表
     * 最多10个，当输入房型和产品条件的时候，只能输入一个酒店ID。多个ID以逗号间隔。
     * 如果对接了酒店静态信息 hotel.static.list 当酒店非有效时无需请请求该接口
     */
    @NotNull
    @JsonProperty("HotelIds")
    private String hotelIds;

    /**
     * 房型编号
     * 当 RatePlanId 传值的时候不能为空。V1.10 后对应销售房型编号
     */
    @Nullable
    @JsonProperty("RoomTypeId")
    private String roomTypeId;

    /**
     * 产品编码
     * 默认请传0
     */
    @Nullable
    @JsonProperty("RatePlanId")
    private Long ratePlanId = 0L;

    /**
     * 支付方式
     * 默认为 All，All-不限、SelfPay-现付、Prepay-预付
     */
    @Nullable
    @JsonProperty("PaymentType")
    private PaymentType paymentType;

    /**
     * 成人数
     * 国际特有字段
     */
    @NotNull
    @JsonProperty("NumberOfAdults")
    private Integer numberOfAdults;

    /**
     * 房间数量
     * 国际特有字段
     */
    @Nullable
    @JsonProperty("NumberOfRooms")
    private Integer numberOfRooms;

    /**
     * 儿童年龄
     * 国际特有字段
     */
    @Nullable
    @JsonProperty("ChildAges")
    private Integer[] childAges;

    /**
     * 预付发票模式
     * Elong-艺龙开发票、Hotel-酒店开发票、NoSense-全部
     * 前台自付产品都是酒店开发票，这里的过滤是针对预付产品。
     * 预付订单的发票有两种开具方式：收款方开具或酒店开具。
     * 收款方开具发票的，艺龙可提供代开服务，需联系商务开通相关权限。
     */
    @Nullable
    @JsonProperty("InvoiceMode")
    private String invoiceMode;

    /**
     * 是否保存 Littlemajiaid
     * 国际特有字段，当需要请求可定时，该字段设置为 true，才能进行可定验证
     */
    @Nullable
    @JsonProperty("SaveMajiaId")
    private Boolean saveMajiaId;

    /**
     * 最晚到店时间
     * 用于计算现付产品的担保规则结果（GuaranteeResult）。
     * 不传时默认最晚到店时间为入住日期+1Day+6Hour（默认兜底逻辑不适用于所有场景，可酌情考虑传入 T+2Hour/NHour 或者入住日期+18:00 等）。
     * 请注意保证搜索时此参数与试单和成单时一致，否则对应担保规则结果会不尽相同。
     * 填单页中现付产品需要根据用户选择的最晚到店时间重新获取接口最新的担保规则结果，到店时间更改，担保类型为到店时间担保的订单取消规则和担保规则均可能发生变化。
     */
    @Nullable
    @JsonProperty("LatestArrivalTime")
    private LocalDate latestArrivalTime;

    /**
     * 其他条件
     * 仅单酒店有效，可逗号分割
     * 1. 酒店详情
     * 2. 房型
     * 3. 图片
     * 4. 当前不可销售的 rp 不出现在结果里（该选项多个酒店也有效）
     * 5. 每日价格数组输出未 DRR 计算的原始价格
     * 7. 返回汇率信息 ExchangeRateList
     * 8. 经纬度返回百度坐标
     * 9. 仅返回钟点房
     * 11. 同时返回钟点房和其他产品
     * 12. 如果是现付产品则返回 GuaranteeResult，如果是预付产品则返回 PrepayResult
     * 13. 支持多间，传入时请保证已兼容多间预定逻辑：从一间变多间时价格、规则等产品信息可能发生变化，当房量变化时可以调用 hotel.room.change 或 hotel.data.booking 获取最新产品相关信息；
     * 不传时某些产品 MaxCheckinRooms 为1，多间预定可能会失败
     * 14. 返回 PrepayRuleExtend、GuaranteeRuleExtend
     * 一般情况下传入 1,2,4
     * 请按需传入，多返回信息会影响报文长度和响应时间，在调用大的情况下，静态信息（1,2,3）建议使用静态相关接口增量落地（hotel.static.list、hotel.static.info）
     */
    @NotNull
    @JsonProperty("Options")
    private String options;
}
