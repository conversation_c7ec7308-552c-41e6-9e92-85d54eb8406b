package com.somytrip.entity.hotel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.somytrip.entity.enums.hotel.HotelOrigin;
import com.somytrip.entity.hotel.ELong.offline.ELongHotelDataRateResp;
import com.somytrip.utils.LocalDateTimeUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * (HotelRate)实体类
 *
 * <AUTHOR>
 * @since 2024-02-20 11:51:11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("hotel_rate")
public class HotelRateEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 344603807584355313L;

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 酒店来源
     */
    private HotelOrigin hotelOrigin;
    /**
     * 酒店ID
     */
    private String hotelId;
    /**
     * 房型ID
     */
    private String roomTypeId;
    /**
     * 产品ID
     */
    private Integer ratePlanId;
    /**
     * 付款类型
     */
    private String paymentType;
    /**
     * 开始时间
     */
    private LocalDate startDate;
    /**
     * 结束时间
     */
    private LocalDate endDate;
    /**
     * 供应商ID
     */
    private String hotelCode;
    /**
     * 状态
     */
    private Boolean status;
    /**
     * 平日卖价
     */
    private BigDecimal member;
    /**
     * 周末卖价
     */
    private BigDecimal weekend;
    /**
     * 平日结算价
     */
    private BigDecimal memberCost;
    /**
     * 周末结算价
     */
    private BigDecimal weekendCost;
    /**
     * 加床价，-1代表不能加床，0-免费加床，大于0表示加床的费用
     */
    private BigDecimal addBed;
    /**
     * 价格ID
     */
    private String priceId;
    /**
     * 货币类型
     */
    private String currencyCode;
    /**
     * 发票模式
     */
    private String invoiceMode;
    /**
     * 是否限价
     */
    private Boolean isPriceLimit;
    /**
     * 限价类型
     */
    private Integer priceLimitedType;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public HotelRateEntity(ELongHotelDataRateResp.Rate elongRate) {
        this.hotelOrigin = HotelOrigin.ELong;
        this.hotelId = elongRate.getHotelId();
        this.roomTypeId = elongRate.getRoomTypeId();
        this.ratePlanId = elongRate.getRatePlanId();
        this.paymentType = elongRate.getPaymentType();
        this.startDate = LocalDateTimeUtil.convertISOStr2LocalDate(elongRate.getStartDate());
        this.endDate = LocalDateTimeUtil.convertISOStr2LocalDate(elongRate.getEndDate());
        this.hotelCode = elongRate.getHotelCode();
        this.status = elongRate.getStatus();
        this.member = elongRate.getMember();
        this.weekend = elongRate.getWeekend();
        this.memberCost = elongRate.getMemberCost();
        this.weekendCost = elongRate.getWeekendCost();
        this.addBed = elongRate.getAddBed();
        this.priceId = elongRate.getPriceId();
        this.currencyCode = elongRate.getCurrencyCode();
        this.invoiceMode = elongRate.getInvoiceMode();
        this.isPriceLimit = elongRate.getIsPriceLimit();
        this.priceLimitedType = elongRate.getPriceLimitedType();
    }
}

