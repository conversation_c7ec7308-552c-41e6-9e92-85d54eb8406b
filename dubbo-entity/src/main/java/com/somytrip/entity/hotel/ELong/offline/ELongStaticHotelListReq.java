package com.somytrip.entity.hotel.ELong.offline;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/**
 * @ClassName: ELongStaticHotelReq
 * @Description: 同程艺龙静态酒店请求体
 * @Author: shadow
 * @Date: 2024/1/26 16:19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ELongStaticHotelListReq {

    /**
     * 开始时间 格式yyyy-MM-dd HH:mm:ss
     */
    @Nullable
    private String startTime;

    /**
     * 结束时间 格式yyyy-MM-dd HH:mm:ss
     */
    @Nullable
    private String endTime;

    /**
     * 城市ID
     */
    @NotNull
    private String cityId;

    /**
     * 每页数据量 默认：2000，每页抓取的数据量
     */
    @Nullable
    private Integer pageSize;

    /**
     * 页码 从1开始
     */
    @Nullable
    private Integer pageIndex;
}
