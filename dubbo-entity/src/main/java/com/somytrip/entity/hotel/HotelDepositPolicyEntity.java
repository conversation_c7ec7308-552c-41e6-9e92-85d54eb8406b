package com.somytrip.entity.hotel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.somytrip.entity.enums.hotel.HotelOrigin;
import com.somytrip.entity.hotel.ELong.offline.ELongStaticHotelInfoResp;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 酒店押金政策表(HotelDepositPolicy)实体类
 *
 * <AUTHOR>
 * @since 2024-02-02 10:13:49
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "hotel_deposit_policy", autoResultMap = true)
public class HotelDepositPolicyEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 234262402007264390L;

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 酒店来源
     */
    private HotelOrigin hotelOrigin;
    /**
     * 酒店ID
     */
    private String hotelId;
    /**
     * 酒店主键ID
     */
    private Long hotelPid;
    /**
     * 是否收取押金 (1:是, 0:否, null:未知)
     */
    private Integer depositSwitch;
    /**
     * 收取频次 (1:固定金额，2:每间，3:每晚)
     */
    private Integer frequency;
    /**
     * 收取金额
     */
    private BigDecimal amount;
    /**
     * 押金支付方式 (1:现金，2:信用卡，3:借记卡，4:第三方平台)
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Integer> payType;
    /**
     * 押金退还方式 (0:不原路退还，1:原路退还)
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Integer> refundType;
    /**
     * 押金退还时间 (0:当日退还，1:一周内退还，2:两周内退还)
     */
    private Integer refundTime;
    /**
     * 押金币种
     */
    private String currency;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public HotelDepositPolicyEntity(ELongStaticHotelInfoResp.Detail.DepositPolicy depositPolicy, String hotelId) {

        if (depositPolicy == null) {
            return;
        }
        this.hotelOrigin = HotelOrigin.ELong;
        this.hotelId = hotelId;
        this.depositSwitch = depositPolicy.getDepositSwitch();
        this.frequency = depositPolicy.getFrequency();
        this.amount = depositPolicy.getAmount();
        this.payType = depositPolicy.getPayType();
        this.refundType = depositPolicy.getRefundType();
        this.refundTime = depositPolicy.getRefundTime();
        this.currency = depositPolicy.getCurrency();
    }
}