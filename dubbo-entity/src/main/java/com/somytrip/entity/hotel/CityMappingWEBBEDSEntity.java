package com.somytrip.entity.hotel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.somytrip.entity.hotel.WEBBEDS.WEBBEDSCitiesResp;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * @ClassName: CityMappingWEBBEDSEntity
 * @Description: WEBBEDS城市映射Entity
 * @Author: shadow
 * @Date: 2024/3/19 17:59
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "city_mapping_webbeds", autoResultMap = true)
public class CityMappingWEBBEDSEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 561738783943468011L;

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 统一城市code
     */
    private String cityCode;

    /**
     * 城市ID
     */
    private Integer cityId;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 城市code
     */
    private String webbedsCityCode;

    /**
     * 省份ID
     */
    private Integer provinceId;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 国家ID
     */
    private Integer countryId;

    /**
     * 国家名称
     */
    private String countryName;

    /**
     * 是否可用
     */
    @TableField("is_active")
    private Boolean isActive;

    /**
     * 行政区列表
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<DistrictEntity> districts;

    public CityMappingWEBBEDSEntity(WEBBEDSCitiesResp.City webbedsCity) {
        this.cityId = webbedsCity.getId();
        this.cityName = webbedsCity.getName();
        this.webbedsCityCode = webbedsCity.getCode();
        this.provinceId = webbedsCity.getProvinceId();
        this.provinceName = webbedsCity.getProvinceName();
        if (webbedsCity.getDistricts() == null) {
            return;
        }
        this.districts = webbedsCity.getDistricts().stream().map(DistrictEntity::new).toList();
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DistrictEntity {

        /**
         * 行政区ID
         */
        private Integer districtId;

        /**
         * 行政区名称
         */
        private String districtName;

        public DistrictEntity(WEBBEDSCitiesResp.City.District webbedsDistrict) {
            this.districtId = webbedsDistrict.getId();
            this.districtName = webbedsDistrict.getName();
        }
    }
}
