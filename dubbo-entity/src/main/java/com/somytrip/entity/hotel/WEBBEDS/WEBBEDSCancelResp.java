package com.somytrip.entity.hotel.WEBBEDS;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName: WEBBEDSCancelResp
 * @Description: WEBBEDS取消订单响应
 * @Author: shadow
 * @Date: 2024/3/19 4:29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WEBBEDSCancelResp {

    /**
     * 订单号
     */
    @JsonProperty("booking_no")
    private String bookingNo;

    /**
     * 取消费用
     */
    @JsonProperty("cancel_fee")
    private BigDecimal cancelFee;

    /**
     * 取消政策
     */
    @JsonProperty("cancel_policies")
    private List<WEBBEDSBookResp.CancelPolicy> cancelPolicies;

    /**
     * 币种
     */
    private String currency;
}
