package com.somytrip.entity.hotel.ELong.offline;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName: ELongStaticInfoReq
 * @Description: 同程艺龙静态酒店信息请求体
 * @Author: shadow
 * @Date: 2024/1/27 11:34
 */
@Data
public class ELongStaticHotelInfoReq {

    /**
     * 酒店ID
     */
    @JsonProperty("HotelId")
    private String hotelId;

    /**
     * 其他条件 英文逗号分割
     * 1、Detail
     * 2、Suppliers
     * 3、Rooms
     * 4、Images
     * 5、TelList
     */
    @JsonProperty("Options")
    private String options;
}
