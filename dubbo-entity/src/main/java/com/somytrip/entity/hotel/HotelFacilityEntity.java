package com.somytrip.entity.hotel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 酒店设施表(HotelFacilities)实体类
 *
 * <AUTHOR>
 * @since 2024-01-31 15:26:38
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "hotel_facilities", autoResultMap = true)
public class HotelFacilityEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = -18123967273415249L;

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 酒店来源
     */
    private String hotelOrigin;
    /**
     * 设施ID
     */
    private String facilityId;
    /**
     * 设施中文名
     */
    private String facilityName;
    /**
     * 设施英文名
     */
    private String facilityNameEn;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}

