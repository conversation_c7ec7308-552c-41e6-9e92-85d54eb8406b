package com.somytrip.entity.xiaoqi;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * GPT聊天Session表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("somytrip_gpt_session")
public class SomytripGptSession implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户外键
     */
    private String uid;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    private Boolean isDel;
}
