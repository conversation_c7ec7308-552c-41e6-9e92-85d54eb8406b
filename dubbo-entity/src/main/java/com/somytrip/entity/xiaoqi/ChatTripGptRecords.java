package com.somytrip.entity.xiaoqi;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.somytrip.entity.enums.xq.AIMessageTypeEnum;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * gpt聊天记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("chat_trip_gpt_records")
@Builder
public class ChatTripGptRecords implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 聊天会话ID
     */
    private String chatId;

    /**
     * 内容
     */
    private String message;

    /**
     * 类型，USER，AI，TOOL_EXECUTION_RESULT
     */
    private AIMessageTypeEnum type;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否删除
     */
    @TableLogic(value = "0", delval = "1")
    private Boolean isDel;
}
