package com.somytrip.entity.xiaoqi;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * GPT session 聊天列表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("gpt_session_message_map")
public class GptSessionMessageMap implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 外键
     */
    private String sessionId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 第一次用户提问具体内容
     */
    private String content;

    /**
     * 收藏标记，1-收藏，2-未收藏
     */
    @TableField("like_flag")
    private Integer likeFlag;

    /**
     * 类型，USER，AI，TOOL_EXECUTION_RESULT
     */
    private String type;

    @TableLogic(value = "0", delval = "1")
    private Boolean isDel;

    private LocalDateTime updateTime;

    private LocalDateTime createTime;
}
