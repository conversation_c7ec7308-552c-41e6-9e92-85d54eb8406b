package com.somytrip.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 地图坐标距离
 *
 * <AUTHOR>
 */
@Data
public class MapDistance {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 高德ID
     */
    private String idV;
    /**
     * 高德ID
     */
    private String mapV;
    /**
     * 城市 深圳
     */
    private String city;
    /**
     * 距离
     */
    private BigDecimal distance;
    /**
     * 修改时间
     */
    private Date updateTime;

    public MapDistance(String idV, String mapV, String city, BigDecimal distance, Date updateTime) {
        super();
        this.idV = idV;
        this.mapV = mapV;
        this.city = city;
        this.distance = distance;
        this.updateTime = updateTime;
    }

    public MapDistance() {
        super();
    }
}
