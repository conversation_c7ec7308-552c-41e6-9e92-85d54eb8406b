package com.somytrip.entity.tourism;

import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: QueryTourismParam
 * @Description:
 * @Author: shadow
 * @Date: 2023/10/10 10:10
 */
@Data
public class QueryTourismParam {

    /**
     * 用户ID
     */
    private Long uid;
    /**
     * 交通数据标识
     */
    private String transportationFlag;
    /**
     * 攻略类型(1: 纯玩游, 2: 本地游, 3: 错峰游)
     */
//    @Range(min = 1, max = 3, message = "攻略类型无效")
    private Integer tourismType = 1;
    /**
     * 出发地ID
     */
    private Integer beginning;
    /**
     * 目的地(JSON ID列表)
     */
    private List<Integer> destination;
    /**
     * 游玩时间(出发时间,返回时间   yyyy-MM-dd,yyyy-MM-dd)
     */
    private String playTime;
    /**
     * 各天游玩时间
     */
    private Map<Integer, List<Integer>> customDayTime;
    /**
     * 风格偏好
     */
    private List<Integer> theme;
    /**
     * 行程节奏
     */
//    @Range(min = 1, max = 3, message = "行程节奏无效")
    private Integer journey = 2;
    /**
     * 年龄
     */
    private String age = "";
    /**
     * 游客组合
     */
    private Map<String, Integer> visitors;
    /**
     * 是否带娃
     */
    private boolean hasChild = false;
    /**
     * 意向景区
     */
    private List<Long> favScenic = new ArrayList<>();
    /**
     * 意向酒店
     */
    private List<String> favHotel = new ArrayList<>();
    /**
     * 预算
     */
    private BigDecimal budget = BigDecimal.ZERO;

    private Integer days;
}
