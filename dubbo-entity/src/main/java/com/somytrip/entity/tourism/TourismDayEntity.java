package com.somytrip.entity.tourism;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.somytrip.entity.dto.city.CityDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: TourismDayEntity
 * @Description: 攻略方案每日数据Entity
 * @Author: shadow
 * @Date: 2023/12/26 11:57
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("tourism_days")
public class TourismDayEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 方案ID
     */
    private long schemeId;

    /**
     * 等级(1: 经济型, 2: 适中型, 3: 高档型)
     */
    private Integer level;

    /**
     * 第几天
     */
    private Integer dayNumber;

    /**
     * 预算
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<TourismSearchingBudgetDto> budget;

    /**
     * 城市列表
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<CityDto> cityList;

    /**
     * 活动内容
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<Integer, List<String>> activityContent;

    /**
     * 当前日期
     */
    private LocalDate date;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
