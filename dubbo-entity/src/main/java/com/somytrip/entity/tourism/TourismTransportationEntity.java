package com.somytrip.entity.tourism;

import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ClassName: TourismTransportationEntity
 * @Description: 攻略交通Entity
 * @Author: shadow
 * @Date: 2023/12/26 12:00
 */
@Data
@TableName("tourism_transportations")
public class TourismTransportationEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 方案ID
     */
    private Long schemeId;

    /**
     * 等级
     */
    private Integer level;

    /**
     * 交通方式(1: 飞机, 2: 火车, 3: 船 ...)
     */
    private Integer transportationMode;

    /**
     * 交通内容
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private JSONArray transportationContent;

    /**
     * 是否已对方案进行调优
     */
    private boolean isTuned;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public TourismTransportationEntity() {
    }

    public TourismTransportationEntity(Long schemeId, Integer level, Integer mode) {
        this.schemeId = schemeId;
        this.level = level;
        this.transportationMode = mode;
    }

    public TourismTransportationEntity(Long id, Long schemeId, Integer level, Integer transportationMode, JSONArray transportationContent, boolean isTuned, LocalDateTime createTime, LocalDateTime updateTime) {
        this.id = id;
        this.schemeId = schemeId;
        this.level = level;
        this.transportationMode = transportationMode;
        this.transportationContent = transportationContent;
        this.isTuned = isTuned;
        this.createTime = createTime;
        this.updateTime = updateTime;
    }

    public boolean getIsTuned() {
        return isTuned;
    }

    public void setIsTuned(boolean tuned) {
        isTuned = tuned;
    }
}
