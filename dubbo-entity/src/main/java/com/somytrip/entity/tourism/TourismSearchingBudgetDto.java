package com.somytrip.entity.tourism;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @ClassName: TourismSearchingBudgetDto
 * @Description: 攻略查询中预算dto
 * @Author: shadow
 * @Date: 2024/1/5 11:19
 */
@Data
public class TourismSearchingBudgetDto {

    /**
     * 类型(1: 景区, 2: 餐厅, 3: 酒店)
     */
    private Integer type;

    /**
     * 预算
     */
    private BigDecimal budget = BigDecimal.ZERO;

    public TourismSearchingBudgetDto() {
    }

    public TourismSearchingBudgetDto(Integer type) {
        this.type = type;
    }

    public TourismSearchingBudgetDto(Integer type, BigDecimal budget) {
        this.type = type;
        this.budget = budget;
    }
}
