package com.somytrip.entity.tourism;

import com.somytrip.entity.dto.city.CityDto;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @ClassName: TourismItem
 * @Description: 攻略对象(一个等级的攻略数据)
 * @Author: shadow
 * @Date: 2023/12/23 17:45
 */
@Data
public class TourismSearchingDayItem {

    /**
     * 第几天
     */
    private Integer dayNumber;

    /**
     * 当天预算
     */
    private List<TourismSearchingBudgetDto> budget;

    /**
     * 活动内容
     */
    private Map<Integer, List<String>> activityContent;

    /**
     * 城市列表
     */
    private List<CityDto> cityList;
}
