package com.somytrip.entity.tourism;

import com.alibaba.fastjson2.JSONArray;
import com.somytrip.entity.enums.tourism.TransportModeEnums;
import lombok.Data;

/**
 * @ClassName: TourismTransportationVo
 * @Description: 攻略交通vo
 * @Author: shadow
 * @Date: 2024/1/6 11:56
 */
@Data
public class TourismTransportationVo {

    private Integer level;

    private Integer mode;

    private String modeName;

    private JSONArray content;

    public TourismTransportationVo(TourismTransportationEntity entity) {
        this.level = entity.getLevel();
        this.mode = entity.getTransportationMode();
        this.modeName = TransportModeEnums.get(entity.getTransportationMode());
        this.content = entity.getTransportationContent();
    }
}
