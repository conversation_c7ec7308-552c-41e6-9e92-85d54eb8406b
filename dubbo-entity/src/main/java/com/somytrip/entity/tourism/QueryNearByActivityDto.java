package com.somytrip.entity.tourism;


import com.somytrip.entity.dto.hotel.PaginationDto;
import com.somytrip.target.AtLeastOneNotNull;
import lombok.Data;

/**
 * @ClassName: QueryNearByActivityDto
 * @Description: 攻略查询附近景区dto
 * @Author: shadow
 * @Date: 2024/1/4 16:32
 */
@Data
@AtLeastOneNotNull(fieldNames = {"cityId", "lon", "lat", "name"}, message = "城市、坐标和名称不能都为空")
public class QueryNearByActivityDto {

    /**
     * 类型(1: 景区， 2: 餐厅, 3: 酒店)
     */
//    @Range(min = 1, max = 3, message = "类型错误")
    private Integer type;

    /**
     * 攻略类型(1: 纯玩游, 2: 本地游, 3: 错峰游)
     */
//    @Range(min = 1, max = 3, message = "攻略类型错误")
    private Integer tourismType = 1;

    /**
     * 等级(1: 经济型, 2: 适中型, 3: 高档型)
     */
    private Integer level;

    /**
     * 城市ID
     */
    private Integer cityId = -1;

    /**
     * 经度
     */
    private String lon;

    /**
     * 纬度
     */
    private String lat;

    /**
     * 活动地点名称
     */
    private String name;

    /**
     * 分页参数
     */
    private PaginationDto pagination;
}
