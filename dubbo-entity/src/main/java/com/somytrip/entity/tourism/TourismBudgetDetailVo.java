package com.somytrip.entity.tourism;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONArray;
import com.somytrip.entity.enums.tourism.ActivityTypeNameEnums;
import com.somytrip.entity.enums.tourism.LevelNameEnums;
import com.somytrip.entity.vo.ScenicVo;
import com.somytrip.entity.vo.TourismHotelVo;
import com.somytrip.entity.vo.hotel.HotelVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.List;

/**
 * @ClassName: TourismBudgetDetailvo
 * @Description: 攻略预算明细vo
 * @Author: shadow
 * @Date: 2024/1/9 15:11
 */
@Deprecated
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TourismBudgetDetailVo {

    /**
     * 总预算
     */
    private String generalBudget;

    /**
     * 等级
     */
    private Integer level;

    /**
     * 等级名称
     */
    private String levelName;

    /**
     * 每日预算列表
     */
    private List<BudgetDetailDayVo> dayList;

    public TourismBudgetDetailVo(Integer level) {
        this.level = level;
        this.levelName = LevelNameEnums.getCnName(level);
    }

    /**
     * 每日预算
     */
    @Deprecated
    @Data
    public static class BudgetDetailDayVo {

        /**
         * 日期
         */
        private LocalDate date;

        /**
         * 预算内容
         */
        private List<BudgetDetailItem> budgetContent;

        /**
         * 总预算
         */
        private String totalBudget;
    }

    /**
     * 预算详情Item
     */
    @Deprecated
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BudgetDetailItem {

        /**
         * 来源
         */
        private String origin;

        /**
         * 类型(1: 景区， 2: 餐厅, 3: 酒店)
         */
        private Integer type;

        /**
         * 类型名称
         */
        private String typeName;

        /**
         * iD
         */
        private String id;

        /**
         * 名称
         */
        private String name;

        /**
         * 封面图
         */
        private String coverImage;

        /**
         * 预算
         */
        private String budget;

        public BudgetDetailItem(Integer type) {
            this.type = type;
        }

        public BudgetDetailItem(ScenicVo scenicVo) {
            this.origin = "SMT";
            this.type = 1;
            this.typeName = ActivityTypeNameEnums.get(this.type);
            this.id = String.valueOf(scenicVo.getId());
            this.name = scenicVo.getName();
            if (scenicVo.getPic() != null && StrUtil.isNotBlank(scenicVo.getPic())) {
                JSONArray arr = JSONArray.parseArray(scenicVo.getPic());
                if (!arr.isEmpty()) {
                    this.coverImage = arr.getString(0);
                }
            }
            this.budget = halfAdjust2Str(scenicVo.getEstimatedPrice());
        }

        public BudgetDetailItem(TourismHotelVo hotelVo) {
            if (hotelVo != null) {
                this.origin = hotelVo.getHotelOrigin();
                this.type = 3;
                this.typeName = ActivityTypeNameEnums.get(this.type);
                this.id = String.valueOf(hotelVo.getId());
                this.name = hotelVo.getTitle();
                this.coverImage = hotelVo.getCover();
                this.budget = hotelVo.getAdvicePrice();
            }
        }

        public BudgetDetailItem(HotelVo hotelVo) {
            if (hotelVo != null) {
                this.origin = hotelVo.getHotelOrigin();
                this.type = 3;
                this.typeName = ActivityTypeNameEnums.get(this.type);
                this.id = String.valueOf(hotelVo.getId());
                this.name = hotelVo.getHotelName();
                if (hotelVo.getHotelImages() != null && !hotelVo.getHotelImages().isEmpty()) {
                    this.coverImage = hotelVo.getHotelImages().get(0).getImageUrl();
                }
//                this.budget = StringUtils.isNotBlank(hotelVo.getStartPrice()) ? hotelVo.getStartPrice() : "暂无价格";
                this.budget = hotelVo.getStartPrice();
            }
        }

        private String halfAdjust2Str(BigDecimal bigDecimal) {
            if (bigDecimal == null) {
                return "";
            }
            return bigDecimal.setScale(2, RoundingMode.HALF_UP).toString();
        }
    }
}
