package com.somytrip.entity.tourism;

import lombok.Data;

import java.util.List;

/**
 * @ClassName: TourismSchemeDetail
 * @Description: 攻略方案详情
 * @Author: shadow
 * @Date: 2023/12/29 9:50
 */
@Data
public class TourismSchemeDetailVo {

    /**
     * 等级列表
     */
    private List<TourismSchemeLevelVo> levels;

    /**
     * 紧急联络列表
     */
    private List<TourismEmergencyContactVo> emergencyContact;

    /**
     * 方案基本信息
     */
    private SchemeInfo schemeInfo;

    @Data
    public static class SchemeInfo {

        /**
         * 方案序列号
         */
        private String schemeSn;

        /**
         * 方案标题
         */
        private String schemeTitle;

        public SchemeInfo(TourismSchemeEntity entity) {
            this.schemeSn = entity.getSchemeSn();
            this.schemeTitle = entity.getSchemeTitle();
        }
    }
}
