package com.somytrip.entity.tourism;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ClassName: TourismThemeEntity
 * @Description: 攻略风格偏好Entity
 * @Author: shadow
 * @Date: 2023/12/8 10:38
 */
@Data
@TableName("tourism_themes")
public class TourismThemeEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键ID
     */
    private Long id;

    /**
     * 排序号
     */
    private Integer sortNum;

    /**
     * 主题名称
     */
    private String name;

    /**
     * 是否可用
     */
    private boolean isActive;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    public boolean getIsActive() {
        return this.isActive;
    }

    public void setIsActive(boolean isActive) {
        this.isActive = isActive;
    }
}
