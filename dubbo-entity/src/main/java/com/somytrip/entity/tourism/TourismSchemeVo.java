package com.somytrip.entity.tourism;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.somytrip.entity.dto.FileDto;
import com.somytrip.entity.dto.city.CityDto;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName: TourismSchemeVo
 * @Description: 攻略方案Vo
 * @Author: shadow
 * @Date: 2023/12/27 17:52
 */
@Data
public class TourismSchemeVo {

    /**
     * 方案序列号
     */
    private String schemeSn;

    /**
     * 用户ID
     */
    private String uid;

    /**
     * 攻略类型(1: 纯玩游, 2: 本地游, 3: 错峰游)
     */
    private Integer tourismType;

    /**
     * 方案标题
     */
    private String schemeTitle;

    /**
     * 出发时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime goTime;

    /**
     * 返程时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime backTime;

//    /**
//     * 出发城市ID
//     */
//    private Integer departureCityId;

    /**
     * 出发城市
     */
    private CityDto departureCity;

//    /**
//     * 目的地城市ID列表
//     */
//    private Item<Integer> destinationCityIds;

    /**
     * 目的地城市列表
     */
    private List<CityDto> destinationCityList;

    /**
     * 封面图片
     */
    private FileDto coverImage;

    public TourismSchemeVo(TourismSchemeEntity entity) {
        this.schemeSn = entity.getSchemeSn();
        this.uid = entity.getUid();
        this.tourismType = entity.getTourismType();
        this.schemeTitle = entity.getSchemeTitle();
        this.goTime = entity.getGoTime();
        this.backTime = entity.getBackTime();
//        setDepartureCityId(entity.getDepartureCityId());
//        setDestinationCityIds(entity.getDestinationCityIds());
        this.coverImage = entity.getCoverImage();
    }
}
