package com.somytrip.entity.tourism;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName(value = "planning")
public class Planning {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 出发时间
     */
    private String time1;
    /**
     * 返回时间
     */
    private String time2;
    /**
     * 花费
     */
    private String spend;
    /**
     * 定制攻略的游客组合，成人1，小孩2等
     */
    private String visitors;
    /**
     * 主题
     */
    private String theme;
    /**
     * 年龄列表 0-18,18-35等
     */
    private String ageCls;
    /**
     * 出发城市
     */
    @TableField(value = "starting_city")
    private String startingCity;
    /**
     * 目的地城市列表
     */
    private String destination;
    /**
     * 目的地城市ID列表
     */
    private String destinationIds;
    /**
     * 暴走模式、悠闲模式... 技术债
     */
    private String journey;
    /**
     * 希望去的景区
     */
    private String wantSces;
    /**
     * 希望去的酒店
     */
    private String wantHotels;
    /**
     * 用户表ID 外键
     */
    private Integer userId;
    /**
     * 四个表关联的ID 外键
     */
    private Integer schemeId;
    /**
     * 创建时间
     */
    private Date createTime;

    public Planning(String time1, String time2, String spend, String visitors, String theme, String ageCls, String startingCity, String destination,
                    String journey, String wantSces, String wantHotels, int userId) {
        super();
        this.time1 = time1;
        this.time2 = time2;
        this.spend = spend;
        this.visitors = visitors;
        this.theme = theme;
        this.ageCls = ageCls;
        this.startingCity = startingCity;
        this.destination = destination;
        this.journey = journey;
        this.wantSces = wantSces;
        this.wantHotels = wantHotels;
        this.userId = userId;
        this.createTime = new Date();
    }

    // 用于小程序
    public Planning(String time1, String time2, String startingCity, String destination, int userId, Integer schemeId) {
        super();
        this.time1 = time1;
        this.time2 = time2;
        this.startingCity = startingCity;
        this.destination = destination;
        this.userId = userId;
        this.schemeId = schemeId;
        this.createTime = new Date();
    }

    public Planning(String time1, String time2, String visitors, String theme, String ageCls, String startingCity,
                    String destination, String journey, String wantSces, String wantHotels, Integer userId,
                    Integer schemeId) {
        this.time1 = time1;
        this.time2 = time2;
        this.visitors = visitors;
        this.theme = theme;
        this.ageCls = ageCls;
        this.startingCity = startingCity;
        this.destination = destination;
        this.journey = journey;
        this.wantSces = wantSces;
        this.wantHotels = wantHotels;
        this.userId = userId;
        this.schemeId = schemeId;
    }

    public Planning() {
        super();
    }
}
