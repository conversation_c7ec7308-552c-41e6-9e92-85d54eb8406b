package com.somytrip.entity.tourism.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.somytrip.entity.itinerary.ItineraryDayEntity;
import com.somytrip.entity.itinerary.ItineraryScenicEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.tourism.excel
 * @className: ItineraryExcelActivityVo
 * @author: shadow
 * @description: 攻略excel活动vo
 * @date: 2024/6/6 15:55
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ItineraryExcelActivityVo {

    protected static int INTRO_MAX_LENGTH = 80;
    /**
     * 时间段
     */
    @Excel(name = "停留时间", width = 10)
    private String stayTimePeriod;
    /**
     * 活动名称
     */
    @Excel(name = "活动", width = 22)
    private String activityName;
    /**
     * 营业时间
     */
    @Excel(name = "开放时间", width = 10)
    private String businessTime;
    /**
     * 门票
     */
    @Excel(name = "门票", width = 10)
    private String ticket;
    /**
     * 简介
     */
    @Excel(name = "简介与说明", width = 35)
    private String intro;

    public ItineraryExcelActivityVo(ItineraryScenicEntity itineraryScenicEntity) {
        this.activityName = itineraryScenicEntity.getName();
        this.businessTime = itineraryScenicEntity.getBusinessHours();
        this.ticket = itineraryScenicEntity.getPriceDesc();
        String brief = itineraryScenicEntity.getBrief();
        if (StringUtils.isNotBlank(brief)) {
            this.intro = brief;
        }
    }

    public ItineraryExcelActivityVo(ItineraryDayEntity.ActivityEntity activityEntity) {
        this.activityName = activityEntity.getActivityName();
        this.stayTimePeriod = activityEntity.getStayTimePeriod();
    }
}
