package com.somytrip.entity.tourism;

import com.somytrip.entity.resp.ListResp;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @ClassName: TourismSchemeListResp
 * @Description: 方案列表Resp
 * @Author: shadow
 * @Date: 2024/1/18 11:24
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TourismSchemeListResp extends ListResp<TourismSchemeVo> {

    /**
     * 真实总数
     */
    private Long realTotal;
}
