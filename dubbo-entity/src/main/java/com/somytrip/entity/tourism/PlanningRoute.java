package com.somytrip.entity.tourism;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@TableName(value = "planning_route")
public class PlanningRoute {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 计划表ID 外键
     */
    @TableField(value = "plan_id")
    private Integer planId;
    /**
     * 价格总和
     */
    @TableField(value = "total_price")
    private BigDecimal totalPrice;
    /**
     * 航班ID
     */
    private String flightId;
    /**
     * 航班参数
     */
    private String flightLegs;
    /**
     * 航班参数
     */
    private String flightFrom;
    /**
     * 航班数据
     */
    @TableField(value = "flight_content1")
    private String flightContent1;
    /**
     * 航班url
     */
    @TableField(value = "flight_url1")
    private String flightUrl1;
    /**
     * 火车数据
     */
    @TableField(value = "train_content1")
    private String trainContent1;
    /**
     * 航班数据
     */
    @TableField(value = "flight_content2")
    private String flightContent2;
    /**
     * 航班url
     */
    @TableField(value = "flight_url2")
    private String flightUrl2;
    /**
     * 火车数据
     */
    @TableField(value = "train_content2")
    private String trainContent2;
    /**
     * 经济、适中、奢华级别
     */
    @TableField(value = "route_level")
    private int routeLevel;

    public PlanningRoute(int planId, BigDecimal totalPrice, String flightContent1, String flightUrl1,
                         String trainContent1, String flightContent2, String flightUrl2, String trainContent2, int routeLevel) {
        super();
        this.planId = planId;
        this.totalPrice = totalPrice;
        this.flightContent1 = flightContent1;
        this.flightUrl1 = flightUrl1;
        this.trainContent1 = trainContent1;
        this.flightContent2 = flightContent2;
        this.flightUrl2 = flightUrl2;
        this.trainContent2 = trainContent2;
        this.routeLevel = routeLevel;
    }

    // 用于小程序
    public PlanningRoute(int planId, String flightContent1, String flightContent2, int routeLevel) {
        super();
        this.planId = planId;
        this.flightContent1 = flightContent1;
        this.flightContent2 = flightContent2;
        this.routeLevel = routeLevel;
    }

    public PlanningRoute(Integer planId, BigDecimal totalPrice, int routeLevel) {
        this.planId = planId;
        this.totalPrice = totalPrice;
        this.routeLevel = routeLevel;
    }
}
