package com.somytrip.entity.tourism;

import com.alibaba.fastjson2.JSONArray;
import com.somytrip.entity.enums.tourism.ActivityTypeNameEnums;
import com.somytrip.entity.vo.ScenicVo;
import com.somytrip.entity.vo.hotel.HotelVo;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * @ClassName: TourismNearByActivityVo
 * @Description: 攻略附近活动地点vo
 * @Author: shadow
 * @Date: 2024/1/4 16:29
 */
@Data
public class TourismNearByActivityVo {

    /**
     * 类型(1: 景区, 2: 餐厅, 3: 酒店)
     */
    private Integer type;

    /**
     * 类型名称(景区, 餐厅, 酒店)
     */
    private String typeName;

    /**
     * 来源
     */
    private String origin;

    /**
     * ID
     */
    private String id;

    /**
     * 名称
     */
    private String name;

    /**
     * 城市ID
     */
    private Integer cityId;

    /**
     * 经度
     */
    private String lon;

    /**
     * 纬度
     */
    private String lat;

    /**
     * 与目标位置距离
     */
    private String distance;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 价格
     */
    private String price;

    /**
     * 封面图url
     */
    private String coverImage;

    /**
     * 评分(好玩指数)
     */
    private String score;

    public TourismNearByActivityVo(ScenicVo scenic) {

        this.type = 1;
        this.typeName = ActivityTypeNameEnums.get(this.type);
        this.origin = "SMT";
        this.id = String.valueOf(scenic.getId());
        this.name = scenic.getName();
        this.cityId = Math.toIntExact(scenic.getCityId());
        this.lon = scenic.getLng();
        this.lat = scenic.getLat();
        this.address = scenic.getAddress();
        this.price = halfAdjust2Str(scenic.getEstimatedPrice(), 2);
        if (scenic.getPic() != null) {
            JSONArray picArr = JSONArray.parseArray(scenic.getPic());
            if (!picArr.isEmpty()) {
                this.coverImage = picArr.getString(0);
            }
        }
        this.score = halfAdjust2Str(scenic.getThiIndex(), 2);
    }

    public TourismNearByActivityVo(HotelVo hotelVo) {

        this.type = 3;
        this.typeName = ActivityTypeNameEnums.get(this.type);
        this.origin = hotelVo.getHotelOrigin();
        this.id = hotelVo.getId();
        this.name = hotelVo.getHotelName();
        this.cityId = Integer.valueOf(hotelVo.getCityId());
        this.lon = hotelVo.getLon();
        this.lat = hotelVo.getLat();
        this.address = hotelVo.getAddress();
        this.price = hotelVo.getStartPrice();
        if (hotelVo.getHotelImages() != null && !hotelVo.getHotelImages().isEmpty()) {
            this.coverImage = hotelVo.getHotelImages().get(0).getImageUrl();
        }
        this.score = hotelVo.getScore();
    }

    private String halfAdjust2Str(BigDecimal bigDecimal, Integer num) {
        if (bigDecimal == null) {
            return "";
        }
        return bigDecimal.setScale(num, RoundingMode.HALF_UP).toString();
    }
}
