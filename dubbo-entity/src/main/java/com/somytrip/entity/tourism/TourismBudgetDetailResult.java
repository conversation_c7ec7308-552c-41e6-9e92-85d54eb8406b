package com.somytrip.entity.tourism;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName: TourismBudgetDetailResult
 * @Description: 攻略预算详情Result
 * @Author: shadow
 * @Date: 2024/1/17 17:40
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TourismBudgetDetailResult {

    /**
     * 预算明细
     */
    private TourismBudgetDetailVo budgetDetail;

    /**
     * 简略预算明细
     */
    private TourismBudgetDetailSimpleVo budgetDetailSimple;

    public TourismBudgetDetailResult(TourismBudgetDetailVo budgetDetail) {
        this.budgetDetail = budgetDetail;
    }

    public TourismBudgetDetailResult(TourismBudgetDetailSimpleVo budgetDetailSimple) {
        this.budgetDetailSimple = budgetDetailSimple;
    }
}
