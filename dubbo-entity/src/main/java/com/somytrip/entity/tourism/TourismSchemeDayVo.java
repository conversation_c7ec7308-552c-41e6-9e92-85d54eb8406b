package com.somytrip.entity.tourism;

import com.somytrip.entity.dto.city.CityDto;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * @ClassName: TourismSchemeDayVo
 * @Description: 攻略方案每日数据vo
 * @Author: shadow
 * @Date: 2023/12/29 15:06
 */
@Data
public class TourismSchemeDayVo {

    /**
     * 日期
     */
    private LocalDate date;

    /**
     * 预算
     */
    private List<TourismBudgetVo> dayBudget;

    /**
     * 当天途径城市列表
     */
    private List<CityDto> cityList;
//    private Item<String> cityList;

    /**
     * 时间列表(早上、下午、晚上)
     */
    private List<TimeVo> timeList;

    /**
     * 时间对象
     */
    @Data
    public static class TimeVo {

        /**
         * 时间名称(早上、下午、晚上)
         */
        private String timeName;

        /**
         * 时间(1: 早上, 2: 下午, 3: 晚上)
         */
        private Integer time;

        /**
         * 该时间段活动列表
         */
        private List<TourismActivityVo> activityList;
    }
}
