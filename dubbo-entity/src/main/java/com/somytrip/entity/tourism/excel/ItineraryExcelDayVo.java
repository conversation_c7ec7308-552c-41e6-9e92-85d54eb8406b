package com.somytrip.entity.tourism.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelCollection;
import lombok.Data;

import java.util.List;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.tourism.excel
 * @className: ItineraryExcelDayVo
 * @author: shadow
 * @description: 攻略excel每日vo
 * @date: 2024/6/6 15:52
 * @version: 1.0
 */
@Data
public class ItineraryExcelDayVo {

    /**
     * 第几天
     */
    @Excel(name = "Day", width = 8, needMerge = true)
    private Integer dayNum;

    /**
     * 日期
     */
    @Excel(name = "日期", width = 8, needMerge = true)
    private String date;

    /**
     * 城市名
     */
    @Excel(name = "城市", width = 8, needMerge = true)
    private String city;

    /**
     * 交通
     */
    @Excel(name = "交通", width = 8, needMerge = true)
    private String tp;

    /**
     * 活动列表
     */
    @ExcelCollection(name = "活动列表")
    private List<ItineraryExcelActivityVo> activities;

    /**
     * 酒店
     */
    @Excel(name = "住宿", width = 15, needMerge = true)
    private String hotel;
}

