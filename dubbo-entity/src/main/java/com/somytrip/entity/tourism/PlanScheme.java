package com.somytrip.entity.tourism;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Slf4j
@Data
@NoArgsConstructor
@TableName(value = "plan_scheme")
public class PlanScheme {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 攻略标题
     */
    private String title;
    /**
     * 攻略简介
     */
    private String brief;
    /**
     * 封面图列表
     */
    private String coverPic;
    /**
     * 标签
     */
    private String tags;
    /**
     * 用户ID
     */
    private Integer userId;
    /**
     * 创建时间
     */
    private Date createTime;

    public PlanScheme(String title, String brief, String coverPic, String tags, Integer userId) {
        super();
        this.title = title;
        this.brief = brief;
        this.coverPic = coverPic;
        this.tags = tags;
        this.userId = userId;
    }

    public static PlanScheme getPlanScheme_byjson(JSONObject schemeJSON, int uid) {
        PlanScheme p = new PlanScheme();
        p.setId(schemeJSON.getInteger("id"));
        p.setTitle(schemeJSON.getString("titleName"));
        p.setBrief(schemeJSON.getString("brief"));
        p.setTags(schemeJSON.getString("tagList"));
        p.setUserId(uid);
        p.setCreateTime(new Date());
        return p;
    }
}
