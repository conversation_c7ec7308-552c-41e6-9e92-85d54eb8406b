package com.somytrip.entity.tourism;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @ClassName: TourismEmergencyContactEntity
 * @Description: 攻略紧急联络Entity
 * @Author: shadow
 * @Date: 2024/1/4 11:47
 */
@Data
@TableName("tourism_emergency_contact")
public class TourismEmergencyContactEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 自增逐渐ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 城市ID
     */
    private Integer cityId;

    /**
     * 城市中文名
     */
    private String cityCn;

    /**
     * 城市英文名
     */
    private String cityEn;

    /**
     * 国家ID
     */
    private Integer countryId;

    /**
     * 国家中文名
     */
    private String countryCn;

    /**
     * 国家英文名
     */
    private String countryEn;

    /**
     * 地区(洲)
     */
    private String area;

    /**
     * 报警电话
     */
    private String alarmTel;

    /**
     * 旅游局电话
     */
    private String tourismAdTel;

    /**
     * 中国大使馆电话
     */
    private String chinaEmbassyTel;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
