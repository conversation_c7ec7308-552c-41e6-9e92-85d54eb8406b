package com.somytrip.entity.tourism;

import com.alibaba.fastjson2.JSONObject;
import lombok.Data;

/**
 * @ClassName: TourismTransportationDto
 * @Description: 攻略交通dto
 * @Author: shadow
 * @Date: 2024/1/9 10:41
 */
@Data
public class TourismTransportationDto {

    /**
     * 交通方式(1: 飞机, 2: 火车, 3: 船 ...)
     */
    private Integer transportationMode;

    /**
     * 交通数据
     */
    private JSONObject transportationData;

    /**
     * 错误提示
     */
    private String errMsg;

    /**
     * 错误code
     */
    private String errCode;
}
