package com.somytrip.entity.tourism;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.somytrip.entity.dto.FileDto;
import com.somytrip.handler.JsonToListTypeHandler;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName: TourismSchemeEntity
 * @Description: 攻略方案Entity
 * @Author: shadow
 * @Date: 2023/12/26 11:44
 */
@Data
@TableName("tourism_schemes")
public class TourismSchemeEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 方案序列号
     */
    private String schemeSn;

    /**
     * 用户ID
     */
    private String uid;

    /**
     * 攻略类型
     */
    private Integer tourismType;

    /**
     * 方案标题
     */
    private String schemeTitle;

    /**
     * 出发时间
     */
    private LocalDateTime goTime;

    /**
     * 返程时间
     */
    private LocalDateTime backTime;

    /**
     * 出发城市ID
     */
    private Integer departureCityId;

    /**
     * 目的地城市ID列表
     */
    @TableField(typeHandler = JsonToListTypeHandler.class)
    private List<Integer> destinationCityIds;

    /**
     * 预算
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Budget> budget;

    /**
     * 主题ID列表/风格偏好ID列表(tourism_themes表ID)
     */
    @TableField(typeHandler = JsonToListTypeHandler.class)
    private List<Integer> themeIds;

    /**
     * 行程节奏
     */
    private Integer journey;

    /**
     * 封面图片
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private FileDto coverImage;

    /**
     * 标签
     */
    private String tags;

    /**
     * 导出文件url
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private FileDto export;

    /**
     * 评分
     */
    private BigDecimal score;

    /**
     * 点赞数
     */
    private long likesNumber;

    /**
     * 访问量
     */
    private long visitsNumber;

    /**
     * 是否删除
     */
    private boolean isDel;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public boolean getIsDel() {
        return isDel;
    }

    @Data
    public static class Level {

        private Integer levelValue;

        private List<Long> dayIds;
    }

    @Data
    public static class Budget {

        private Integer levelValue;

        private String budget;
    }
}
