package com.somytrip.entity.special;

import com.somytrip.entity.dto.user.UserTripInfoVo;
import com.somytrip.entity.enums.user.CreditTypeEnum;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description: 创建特色游订单
 * @author: pigeon
 * @created: 2024-04-10 10:01
 */
@Data
public class CreateSpecialOrder {
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Dto {
        /**
         * 特色游-商品
         */
        private SpecialProduct.Vo specialProduct;
        /**
         * 总价 specialProduct.price * guestList.size()
         * 分为单位
         */
        private Integer totalPrice;
        /**
         * 下单用户
         */
        private String uid;
        /**
         * 旅客列表
         */
        private List<Guest> guestList;
        /**
         * 备注
         */
        private String remark;
        /**
         * 是否选中保险
         */
        private Boolean checkedInsure;

        /**
         * 保险序列号
         */
        private String insuranceSequenceNo;

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Valid
    public static class Guest {
        /**
         * 旅客信息系统ID
         */
        private Integer id;
        /**
         * 旅客名称
         */
        private String name;
        /**
         * 出生日期
         */
        private String birthday;
        /**
         * 手机号
         */
        private String mobile;
        /**
         * 证件号码
         */
        private String creditNo;
        /**
         * 证件类型
         */
        private CreditTypeEnum creditType;

        public Guest(UserTripInfoVo vo) {
            this.id = vo.getId();
            this.name = vo.getName();
            this.birthday = vo.getDate();
            this.mobile = vo.getIphone();
            this.creditNo = vo.getIdentityNumber();
            this.creditType = CreditTypeEnum.toCreditTypeEnum(vo.getIdentity());
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Valid
    public static class Vo {
        /**
         * 特色游对应ID
         */
        @NotNull(message = "{api.special.non-id}")
        private Integer specialId;
        /**
         * 用户ID
         */
        private String uid;
        /**
         * 旅客信息系统ID列表
         */
        @NotNull(message = "{api.special.non-trip-info}")
        private List<Integer> tripInfoIds;
        /**
         * 备注
         */
        private String remark;
        /**
         * 是否选中保险
         */
        private Boolean checkedInsure;

        /**
         * 保险序列号
         */
        private String insuranceSequenceNo;
    }
}
