package com.somytrip.entity.special;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.somytrip.entity.dto.city.GlobalCityEntity;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * @Description: 特色游 商品
 * @author: pigeon
 * @created: 2024-04-09 16:01
 */
@Data
public class SpecialProduct {
    @Data
    @AllArgsConstructor
    public static class Insure {
        private Integer id;
        private String code;
        private String price;

        public Insure() {
            this.id = 1;
            this.code = "test-code";
            this.price = "100.00";
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @TableName(value = "somytrip_special_products", autoResultMap = true)
    @Valid
    public static class Dto {
        /**
         * 主键ID
         */
        @TableId(type = IdType.AUTO)
        private Integer id;
        @TableField(typeHandler = Fastjson2TypeHandler.class)
        private List<String> cityCodes;
        /**
         * 特色游-名称
         */
        private String name;
        /**
         * 特色游-描述
         */
        private String description;
        /**
         * 结算价格
         */
        @Pattern(regexp = "\\d+.\\d(2)")
        private String price;
        /**
         * 结算货币
         */
        private String currency;
        /**
         * 供应商
         */
        private String origin;
        /**
         * 活动开始时间
         */
        private Date startTime;
        /**
         * 活动结束时间
         */
        private Date endTime;
        /**
         * 创建时间
         */
        @TableField(fill = FieldFill.INSERT)
        private Date createTime;
        /**
         * 修改时间
         */
        @TableField(fill = FieldFill.INSERT_UPDATE)
        private Date updateTime;
        @TableLogic(value = "0", delval = "1")
        @TableField(value = "is_del")
        private Boolean isDel;
        /**
         * 评分
         */
        private String score;
    }


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Vo {
        /**
         * 主键ID
         */
        private Integer id;
        /**
         * 特色游-名称
         */
        private String name;
        /**
         * 特色游-描述
         */
        private String description;
        /**
         * 结算价格
         */
        @Pattern(regexp = "\\d+.\\d(2)")
        private String price;
        /**
         * 结算货币
         */
        private String currency;
        /**
         * 供应商
         */
        private String origin;
        /**
         * 活动开始时间
         */
        @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss")
        private Date startTime;
        /**
         * 活动结束时间
         */
        @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss")
        private Date endTime;
        /**
         * 评分
         */
        private String score;

        public Vo(Dto dto) {
            this.id = dto.getId();
            this.name = dto.getName();
            this.description = dto.getDescription();
            this.price = dto.getPrice();
            this.currency = dto.getCurrency();
            this.origin = dto.getOrigin();
            this.startTime = dto.getStartTime();
            this.endTime = dto.getEndTime();
            this.score = dto.getScore();
        }
    }


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Detail {
        /**
         * 主键ID
         */
        private Integer id;
        /**
         * 特色游-名称
         */
        private String name;
        /**
         * 特色游-描述
         */
        private String description;
        /**
         * 结算价格
         */
        @Pattern(regexp = "\\d+.\\d(2)")
        private String price;
        /**
         * 结算货币
         */
        private String currency;
        /**
         * 供应商
         */
        private String origin;
        /**
         * 活动开始时间
         */
        @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss")
        private Date startTime;
        /**
         * 活动结束时间
         */
        @JsonFormat(pattern = "yyyy/MM/dd HH:mm:ss")
        private Date endTime;
        /**
         * 途径城市列表
         */
        private List<GlobalCityEntity.Vo> cities;
        /**
         * 评分
         */
        private String score;
        /**
         * 保险Object
         */
        private Insure insure;


        public Detail(Dto dto, List<GlobalCityEntity.Vo> cities) {
            this.id = dto.getId();
            this.name = dto.getName();
            this.description = dto.getDescription();
            this.price = dto.getPrice();
            this.currency = dto.getCurrency();
            this.origin = dto.getOrigin();
            this.startTime = dto.getStartTime();
            this.endTime = dto.getEndTime();
            this.cities = cities;
            this.score = dto.getScore();
            this.insure = new Insure();
        }
    }
}
