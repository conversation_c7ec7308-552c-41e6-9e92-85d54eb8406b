package com.somytrip.entity;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.Serial;
import java.io.Serializable;
import java.util.Locale;
import java.util.Map;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity
 * @className: LocaleDict
 * @author: shadow
 * @description: 国际化字典
 * @date: 2024/9/14 10:31
 * @version: 1.0
 */
//@Data
@Slf4j
@AllArgsConstructor
public class LocaleDict implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    // 自定义国际化字典map
    private Map<String, Map<Locale, String>> map;

    public String get(String name, Locale locale) {
        try {
            return this.map.get(name).get(locale);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }
}
