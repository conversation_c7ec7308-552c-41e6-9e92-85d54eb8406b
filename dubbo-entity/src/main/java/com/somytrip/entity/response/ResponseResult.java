package com.somytrip.entity.response;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * @Description: 统一响应
 * @author: pigeon
 * @created: 2024-01-26 11:13
 */
@Data
public class ResponseResult<T> implements Serializable {
    @Serial
    private static final long serialVersionUID = -1L;
    private int code = 200;
    private boolean success = true;
    private String message = "";
    private T data = null;

    public ResponseResult() {
    }

    public ResponseResult(int status, boolean success, String message, T data) {
        this.code = status;
        this.success = success;
        this.message = message;
        this.data = data;
    }

    public ResponseResult(int status, String message, boolean success, T data) {
        this.code = status;
        this.success = success;
        this.message = message;
        this.data = data;
    }

    public static ResponseResult<Object> success(Object data) {
        return new ResponseResult<>(200, true, "api.common.request-success", data);
    }

    public static ResponseResult<Object> success(String message) {
        return new ResponseResult<>(200, true, message, null);
    }

    public static <T> ResponseResult<T> ok(T data) {
        return new ResponseResult<>(200, true, "api.common.request-success", data);
    }

    public static <T> ResponseResult<T> ok(String message, T data) {
        return new ResponseResult<>(200, true, message, data);
    }

    public static <T> ResponseResult<T> fail(int code, String message) {
        return new ResponseResult<>(code, false, message, null);
    }

    public static <T> ResponseResult<T> fail(int code, String message, T data) {
        return new ResponseResult<>(code, false, message, data);
    }

    public static <T> ResponseResult<T> fail(String message) {
        return new ResponseResult<>(201, false, message, null);
    }

    public static <T> ResponseResult<T> fail400() {
        return new ResponseResult<>(400, false, "api.common.params.error", null);
    }

    public static <T> ResponseResult<T> fail401() {
        return new ResponseResult<>(401, false, "api.user.token.token-not-resolve-user", null);
    }

    public ResponseResult<Map<String, Object>> put(String key, Object value) {
        Map<String, Object> map = new HashMap<>(16);
        if (this.data != null) {
            try {
                map = (Map<String, Object>) this.data;
            } catch (Exception ignored) {
            }
        }
        map.put(key, value);
        return new ResponseResult<>(this.code, this.success, this.message, map);
    }
}
