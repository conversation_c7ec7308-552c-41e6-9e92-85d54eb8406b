package com.somytrip.entity.response;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;

/**
 * @Description: 统一响应
 * @author: pigeon
 * @created: 2024-01-26 11:13
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class Response<T> extends ResponseResult<T> implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
}
