package com.somytrip.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-03-23 15:56
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EncryptData {
    /**
     * 加密后的数据
     */
    private String content;
    /**
     * 加密信息
     */
    private Encrypt encryptInfo;

    public enum SignType {
        /**
         * 国密4
         */
        SM4
    }

    @Data
    @AllArgsConstructor
    public static class Encrypt {
        /**
         * 加盐
         */
        private String iv;
        /**
         * md5Key
         */
        private String key;
        /**
         * 加密类型
         */
        private SignType signType;
    }
}
