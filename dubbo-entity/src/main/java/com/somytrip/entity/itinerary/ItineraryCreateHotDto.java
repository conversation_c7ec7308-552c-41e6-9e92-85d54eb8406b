package com.somytrip.entity.itinerary;

import lombok.Data;

import java.util.List;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.itinerary
 * @className: ItineraryCreateHotDto
 * @author: shadow
 * @description: 创建热门攻略参数dto
 * @date: 2024/6/28 11:40
 * @version: 1.0
 */
@Data
public class ItineraryCreateHotDto {

    /**
     * 用户ID
     */
    private String uid;

    /**
     * 目的地城市code列表
     */
    private List<String> destinationCityCodes;

    /**
     * 天数
     */
    private Integer days;

    /**
     * 旅行伴侣
     */
    private Integer travelMate;

    /**
     * 行程节奏
     */
    private Integer travelRhythm = 2;

    /**
     * 旅行预算(1: 经济型, 2: 适中型, 3: 高档型)
     */
    private Integer travelBudget;
}
