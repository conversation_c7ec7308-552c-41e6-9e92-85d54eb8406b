package com.somytrip.entity.itinerary;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.tourism
 * @className: ItineraryPreparedScenicSearchParam
 * @author: shadow
 * @description: 攻略景区预备数据查询参数dto
 * @date: 2024/4/13 15:07
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ItineraryPreparedScenicSearchParam {

    /**
     * 目的地城市ID
     */
    private Integer destinationCityId;

    /**
     * 旅行伴侣
     */
    private Integer travelMate;

    /**
     * 预算等级
     */
    private Integer travelBudget;

    public ItineraryPreparedScenicSearchParam(ItineraryPrepareDataParam preparedDataSearchParam) {
        this.travelMate = preparedDataSearchParam.getTravelMate();
        this.travelBudget = preparedDataSearchParam.getTravelBudget();
    }
}
