package com.somytrip.entity.itinerary;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.itinerary
 * @className: ItineraryTimeRange
 * @author: shadow
 * @description: 攻略时间区间
 * @date: 2025/3/26 11:44
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ItineraryTimeRange {

    /**
     * 开始time
     */
    @JsonFormat(pattern = "HH:mm")
    private LocalTime startTime;

    /**
     * 结束time
     */
    @JsonFormat(pattern = "HH:mm")
    private LocalTime endTime;

    /**
     * 开始datetime
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime startDateTime;

    /**
     * 结束datetime
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime endDateTime;

    /**
     * 停留时间(分钟)
     */
    private Integer stayMinutes;

    public ItineraryTimeRange(LocalTime startTime, LocalTime endTime) {
        this.startTime = startTime;
        this.endTime = endTime;
    }

    public ItineraryTimeRange(LocalDateTime startDateTime, LocalDateTime endDateTime) {
        this.startDateTime = startDateTime;
        this.endDateTime = endDateTime;
    }

    public void setStartDateTime(LocalDateTime startDateTime) {
        this.startDateTime = startDateTime;
        if (startDateTime != null) {
            this.startTime = startDateTime.toLocalTime();
        }
    }

    public void setEndDateTime(LocalDateTime endDateTime) {
        this.endDateTime = endDateTime;
        if (endDateTime != null) {
            this.endTime = endDateTime.toLocalTime();
        }
    }
}
