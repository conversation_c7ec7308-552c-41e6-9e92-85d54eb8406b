package com.somytrip.entity.itinerary;

import com.somytrip.entity.resp.ListResp;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Year;
import java.util.List;
import java.util.Objects;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.tourism
 * @className: ItineraryListResultVo
 * @author: shadow
 * @description: 攻略列表结果vo
 * @date: 2024/4/26 15:38
 * @version: 1.0
 */
@Data
public class ItineraryListResultVo {

    /**
     * 年份筛选
     * 第1页时返回
     */
    private List<YearFilter> yearFilters;

    /**
     * 攻略列表
     */
    private ListResp<ItineraryListVo> itineraryList;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class YearFilter {

        /**
         * 年份值(如: 2024)
         */
        private Integer value;

        /**
         * 年份名称(如: 2024年)
         */
        private String name;

        /**
         * 是否是今年
         */
        private boolean thisYear = false;

        public YearFilter(String yearValue) {
            this.value = Integer.parseInt(yearValue);
            this.name = yearValue;
            if (Objects.equals(this.value, Year.now().getValue())) {
                this.thisYear = true;
            }
        }
    }
}
