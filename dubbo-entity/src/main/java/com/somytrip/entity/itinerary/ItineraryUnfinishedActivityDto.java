package com.somytrip.entity.itinerary;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.tourism
 * @className: ItineraryUnfinishedActivityDto
 * @author: shadow
 * @description: 攻略未完成活动dto
 * @date: 2024/4/10 16:09
 * @version: 1.0
 */
@Data
public class ItineraryUnfinishedActivityDto {

    /**
     * 未完成的活动
     */
    private ItineraryActivityVo unfinishedActivity = null;

    /**
     * 剩余未完成的时间(小时)
     */
    private BigDecimal unfinishedHour = null;
}
