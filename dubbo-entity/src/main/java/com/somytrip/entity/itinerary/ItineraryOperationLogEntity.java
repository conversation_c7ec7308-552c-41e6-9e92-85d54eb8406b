package com.somytrip.entity.itinerary;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.somytrip.entity.enums.itinerary.ItineraryOperationType;
import com.somytrip.entity.enums.itinerary.ItinerarySubOperationType;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.itinerary
 * @className: ItineraryOperationLogEntity
 * @author: shadow
 * @description: 攻略操作日志Entity
 * @date: 2024/9/24 16:30
 * @version: 1.0
 */
@Data
@TableName(value = "itinerary_operation_log")
public class ItineraryOperationLogEntity implements Serializable {

    @Serial
    private final static long serialVersionUID = 1L;

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private String uid;

    /**
     * 攻略主键ID
     */
    private Long itineraryId;

    /**
     * 操作类型
     */
    private ItineraryOperationType operationType;

    /**
     * 子操作类型
     */
    private String subOperationType;

    /**
     * 参数
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private JSONObject params;

    /**
     * 创建时间(操作时间)
     */
    private LocalDateTime createTime;

    public ItineraryOperationLogEntity(ItineraryOperationLogDto dto) {
        this.uid = dto.getUid();
        this.itineraryId = dto.getItineraryId();
        this.operationType = dto.getOperationType();
        ItinerarySubOperationType subOperationType = dto.getSubOperationType();
        if (subOperationType != null) {
            this.subOperationType = dto.getSubOperationType().toString();
        }
        this.params = dto.getParams();
    }
}
