package com.somytrip.entity.itinerary;

import com.somytrip.entity.enums.itinerary.ItineraryBudgetLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.tourism
 * @className: ItineraryPreparedDataDto
 * @author: shadow
 * @description: 攻略预备数据
 * @date: 2024/3/27 16:16
 * @version: 1.0
 */
@Data
@AllArgsConstructor
public class ItineraryPreparedDataDto {

    /**
     * 预备数据Map
     * md5Key - cityId
     * value - preparedData
     */
    private Map<String, PreparedDataDetail> preparedDataMap;

    /**
     * 多城市交通数据
     */
    private Map<ItineraryBudgetLevel, Map<LocalDate, List<ItineraryTransportationVo>>> preparedTpMultipleCity;

    public ItineraryPreparedDataDto() {
        preparedDataMap = new HashMap<>();
    }

    public static ItineraryPreparedDataDto emptyData(List<String> cityCodes) {
        ItineraryPreparedDataDto dto = new ItineraryPreparedDataDto();
        for (String cityCode : cityCodes) {
            dto.put(cityCode, PreparedDataDetail.emptyDetail());
        }
        return dto;
    }

    public void setPreparedTpMultipleCity(Map<ItineraryBudgetLevel, Map<LocalDate, List<ItineraryTransportationVo>>> preparedTpMultipleCity) {
        this.preparedTpMultipleCity = preparedTpMultipleCity;
    }

    public void put(String cityCode, PreparedDataDetail preparedDataDetail) {
        this.preparedDataMap.put(cityCode, preparedDataDetail);
    }

    public PreparedDataDetail get(String cityCode) {
        return this.preparedDataMap.get(cityCode);
    }

    /**
     * 获取城市列表
     *
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2024/8/3 11:50
     */
    public List<String> keys() {
        return new ArrayList<>(this.preparedDataMap.keySet());
    }

    /**
     * 合并所有城市的预备数据
     *
     * @return com.somytrip.entity.itinerary.ItineraryPreparedDataDto.PreparedDataDetail
     * <AUTHOR>
     * @date 2024/8/3 11:49
     */
    public PreparedDataDetail mergeAll() {

        PreparedDataDetail newDetail = new PreparedDataDetail();
        newDetail.setPreparedScenicList(new ArrayList<>());
        newDetail.setPreparedHotelList(new ArrayList<>());

        Map<String, PreparedDataDetail> preparedDataMap = this.preparedDataMap;
        if (preparedDataMap == null || preparedDataMap.isEmpty()) {
            return null;
        }
        List<String> keys = keys();
        if (keys.size() == 1) {
            return preparedDataMap.get(keys.get(0));
        }
        for (String key : keys) {
            PreparedDataDetail detail = preparedDataMap.get(key);
            newDetail.getPreparedScenicList().addAll(detail.getPreparedScenicList());
            newDetail.getPreparedHotelList().addAll(detail.getPreparedHotelList());
        }

        return newDetail;
    }

    /**
     * 预备数据详情 节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PreparedDataDetail {

        /**
         * 景区预备数据列表
         */
//        private List<ItineraryActivityVo> preparedScenicList;
        private List<ItineraryActivityDto> preparedScenicList;

        /**
         * 酒店预备数据
         */
        private List<ItineraryActivityDto> preparedHotelList;

        /**
         * 预备交通数据列表
         */
        private List<ItineraryTransportationVo> preparedTransportations;

        /**
         * 预备交通数据
         * key -> 预算等级
         * value -> 该预算等级对应交通数据
         */
        private Map<ItineraryBudgetLevel, List<ItineraryTransportationVo>> preparedTransportationData;

        public static PreparedDataDetail emptyDetail() {
            PreparedDataDetail detail = new PreparedDataDetail();
            detail.setPreparedScenicList(new ArrayList<>());
            detail.setPreparedHotelList(new ArrayList<>());
            return detail;
        }
    }
}
