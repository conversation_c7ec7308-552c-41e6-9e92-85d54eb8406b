package com.somytrip.entity.itinerary;

import com.somytrip.entity.enums.itinerary.ItineraryActivityType;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.itinerary
 * @className: ItineraryComprehensiveSortDto
 * @author: shadow
 * @description: 攻略活动综合排序参数dto
 * @date: 2024/7/10 16:15
 * @version: 1.0
 */
@Data
public class ItineraryComprehensiveSortDto {

    /**
     * 预备活动列表
     */
    private List<ItineraryActivityDto> preparedList;

    /**
     * 已选择活动列表
     */
    private List<ItineraryActivityDto> selectedList;

    /**
     * 必去景点列表
     */
    private List<ItineraryActivityDto> mustScenicList = new ArrayList<>();

    /**
     * 必去酒店列表
     */
    private List<ItineraryActivityDto> mustHotelList = new ArrayList<>();

    /**
     * 上一个活动
     */
    private ItineraryActivityDto lastActivity;

    /**
     * 游玩偏好map
     */
    private Map<Integer, List<Integer>> travelPreferenceMap;

    /**
     * 已选择主题map
     */
    private Map<Integer, Integer> themeSelectedMap;

    public ItineraryComprehensiveSortDto(ItineraryGetDayNextScenicDto getDayNextScenicDto) {
        this.lastActivity = getDayNextScenicDto.getLastActivity();
        this.selectedList = getDayNextScenicDto.getSelectedActivityList();
        this.travelPreferenceMap = getDayNextScenicDto.getTravelPreferenceMap();
        this.themeSelectedMap = getDayNextScenicDto.getSelectedThemeCountMap();
        Map<ItineraryActivityType, List<ItineraryActivityDto>> mustActivityMap = getDayNextScenicDto.getMustActivityMap();
        if (mustActivityMap != null) {
            // 必去景点
            List<ItineraryActivityDto> scenicList = mustActivityMap.get(ItineraryActivityType.SCENIC);
            this.mustScenicList = scenicList != null ? scenicList : new ArrayList<>();
            // 必去酒店
            List<ItineraryActivityDto> hotelList = mustActivityMap.get(ItineraryActivityType.HOTEL);
            this.mustHotelList = hotelList != null ? hotelList : new ArrayList<>();
        }
    }
}
