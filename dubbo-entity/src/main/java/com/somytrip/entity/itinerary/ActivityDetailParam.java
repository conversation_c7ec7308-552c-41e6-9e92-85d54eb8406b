package com.somytrip.entity.itinerary;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.itinerary
 * @className: ActivityDetailParam
 * @author: shadow
 * @description: 活动参数
 * @date: 2024/7/19 11:04
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ActivityDetailParam {

    /**
     * 活动来源
     */
    private String activityOrigin;

    /**
     * 活动ID
     */
    private String activityId;

    /**
     * 活动类型
     */
    private Integer activityType;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 城市code
     */
    private String cityCode;

    /**
     * 地址
     */
    private String address;

    /**
     * 经度
     */
    private String lon;

    /**
     * 纬度
     */
    private String lat;

    /**
     * 封面url
     */
    private String cover;

    /**
     * 图片列表
     */
    private List<String> images;

    /**
     * 评分
     */
    private String score;

    /**
     * 起价
     */
    private String startPrice;

    /**
     * 介绍
     */
    private String introduction;

    /**
     * 活动下标
     */
    private Integer index;

    public ActivityDetailParam(ItineraryUpdateParamV2.FromActivity fromActivity) {
        this.activityType = fromActivity.getType();
        this.activityOrigin = fromActivity.getActivityOrigin();
        this.activityId = fromActivity.getActivityId();
        this.index = fromActivity.getIndex();
    }

    public ActivityDetailParam(ItineraryDayEntity.ActivityEntity activityEntity) {
        this.activityOrigin = activityEntity.getActivityOrigin();
        this.activityId = activityEntity.getActivityId();
        this.activityType = activityEntity.getType();
        this.activityName = activityEntity.getActivityName();
        this.cityCode = activityEntity.getCityCode();
        this.address = activityEntity.getAddress();
        this.lon = activityEntity.getLon();
        this.lat = activityEntity.getLat();
        this.images = activityEntity.getImages();
        this.startPrice = activityEntity.getStartPrice();
    }
}
