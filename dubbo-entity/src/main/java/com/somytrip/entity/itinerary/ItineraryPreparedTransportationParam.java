package com.somytrip.entity.itinerary;

import com.somytrip.entity.enums.itinerary.ItineraryTransportationType;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;
import java.util.Locale;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.tourism
 * @className: ItineraryPreparedTransportationParam
 * @author: shadow
 * @description: 攻略预备交通数据查询参数
 * @date: 2024/4/15 17:13
 * @version: 1.0
 */
@Data
public class ItineraryPreparedTransportationParam {

    /**
     * 用户ID
     */
    private String uid;

    /**
     * 攻略序列号
     */
    private String itinerarySn;

    /**
     * 攻略预算等级(1: 经济, 2: 适中, 3: 高档)
     */
    private Integer travelBudgetLevel;

    /**
     * 交通方式
     */
    private ItineraryTransportationType tpType;

    /**
     * 开始时间
     */
    private LocalDate startDate;

    /**
     * 结束时间
     */
    private LocalDate endDate;

    /**
     * 开始城市ID
     */
    private Integer startCityId;

    /**
     * 结束城市ID
     */
    private Integer endCityId;

    /**
     * (机票用)
     */
    private Integer tempCityId;

    /**
     * 城市列表(多城市)
     */
    private List<List<Integer>> cityList;

    /**
     * 日期列表(多城市)
     */
    private List<LocalDate> dateList;

    /**
     * 语言
     */
    private Locale locale;
}
