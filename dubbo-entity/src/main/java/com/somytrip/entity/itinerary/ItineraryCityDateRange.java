package com.somytrip.entity.itinerary;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.itinerary
 * @className: ItineraryCityDateRange
 * @author: shadow
 * @description:
 * @date: 2024/7/16 9:45
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ItineraryCityDateRange {

    /**
     * 城市code
     */
    private String cityCode;

    /**
     * 城市ID
     */
    private Integer cityId;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime endTime;

    public Integer days() {
        return (int) startTime.toLocalDate().until(endTime.toLocalDate(), ChronoUnit.DAYS) + 1;
    }
}
