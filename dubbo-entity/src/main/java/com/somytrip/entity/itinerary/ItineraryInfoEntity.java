package com.somytrip.entity.itinerary;

import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Locale;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.tourism
 * @className: ItineraryInfoEntity
 * @author: shadow
 * @description: 攻略信息Entity
 * @date: 2024/4/13 15:54
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "itinerary_info", autoResultMap = true)
public class ItineraryInfoEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 攻略序列号
     */
    private String itinerarySn;

    /**
     * 用户ID
     */
    private String uid;

    /**
     * 攻略类型
     */
    private Integer ItineraryType;

    /**
     * 攻略标题
     */
    private String itineraryTitle;

    /**
     * 出发时间
     */
    private LocalDateTime goTime;

    /**
     * 返程时间
     */
    private LocalDateTime backTime;

    /**
     * 天数
     */
    private Integer days;

    /**
     * 出发城市ID
     */
    private Integer departureCityId;

    /**
     * 目的地城市ID列表
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Integer> destinationCityIds;

    /**
     * 目的地城市名称
     * 多目的地用"|"分隔
     */
    private String destinationCityNames;

    /**
     * 交通类型
     */
    private Integer tpType;

    /**
     * 旅行伴侣
     */
    private Integer travelMate;

    /**
     * 旅行预算等级
     */
    private Integer travelBudget;

    /**
     * 行程节奏
     */
    private Integer travelRhythm;

    /**
     * 游玩偏好
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<TravelPreferenceParam> travelPreference;

    /**
     * 各城市日期区间
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<ItineraryCityDateRange> cityDateRanges;

    /**
     * 必去活动列表
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<ActivityDetailParam> mustGoActivities;

    /**
     * 预算
     */
    private BigDecimal budget = BigDecimal.ZERO;

    /**
     * 封面url
     */
    private String cover;

    /**
     * 国际化
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Locale locale;

    /**
     * 是否删除
     */
    @TableField(value = "is_del")
    private boolean del;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public void convert4Replace() {
        this.id = null;
        this.updateTime = null;
        this.del = false;
    }

    public void convert4Copy() {
        this.id = null;
        this.createTime = null;
        this.updateTime = null;
    }

    public void setCityDateRanges(List<ItineraryCityDateRange> cityDateRanges) {
        if (cityDateRanges != null) {
            this.cityDateRanges = JSONArray.from(cityDateRanges).toJavaList(ItineraryCityDateRange.class);
        }
    }
}
