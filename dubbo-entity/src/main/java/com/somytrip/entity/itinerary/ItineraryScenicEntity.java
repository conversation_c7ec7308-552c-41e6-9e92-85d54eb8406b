package com.somytrip.entity.itinerary;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.tourism
 * @className: ItineraryScenicEntity
 * @author: shadow
 * @description:
 * @date: 2024/5/29 16:35
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(autoResultMap = true)
public class ItineraryScenicEntity {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 景区名称
     */
    private String name;

    /**
     * 主题
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> themes;

    /**
     * 评分
     */
    private BigDecimal scenicScore;

    /**
     * 地址
     */
    private String address;

    /**
     * 经度
     */
    private String lon;

    /**
     * 纬度
     */
    private String lat;

    /**
     * 图片
     */
    private String pic;

    /**
     * 介绍
     */
    private String brief;

    /**
     * 游玩时间
     */
    private BigDecimal playTimeValue;

    /**
     * 城市code
     */
    private String cityCode;

    /**
     * 县级市ID
     */
    private Integer countryLevelCityId;

    /**
     * 价格数值
     */
    private BigDecimal priceNum;

    /**
     * 价格类型(CNY、EUR......)
     */
    private String priceType;

    /**
     * 价格描述
     */
    private String priceDesc;

    /**
     * 营业时间(描述)
     */
    private String businessHours;

    /**
     * 新 营业时间(格式化)
     */
    private String businessHoursNew;

    /**
     * 暂停营业(1: 暂停营业)
     */
    private Integer businessStop;

    /**
     * 是否夜游(1: 只能夜游)
     */
    private Integer evening;

    /**
     * 层级
     * 0: 一级景区
     * 1: 二级景区(如: 游乐场内游玩项目)
     */
    private Integer level;

    /**
     * 相似值
     * 此属性相同的景点为互相相似的景点
     */
    private Integer similarValue;
}
