package com.somytrip.entity.itinerary;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.apache.commons.lang3.ObjectUtils;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.itinerary
 * @className: ItineraryUpdateParamV2
 * @author: shadow
 * @description: 攻略更新参数V2
 * @date: 2024/11/15 10:07
 * @version: 1.0
 */
@Data
public class ItineraryUpdateParamV2 {

    /**
     * 更新类型
     */
    @NotNull(message = "{api.itinerary-non-update-type}")
    private Integer updateType;

    /**
     * 攻略序列号
     */
    @NotBlank(message = "{api.itinerary.non-itinerary-sn}")
    private String itinerarySn;

    /**
     * 预算等级
     */
    @NotNull(message = "{api.itinerary.non-budget-level}")
    private Integer budgetLevel;

    /**
     * 用户ID
     */
    private String uid;

    /**
     * 攻略标题
     */
    private String itineraryTitle;

    /**
     * 时间区间
     */
    private TimeRange timeRange;

    /**
     * 下标区间
     */
    private IndexRange indexRange;

    /**
     * 各城市日期区间
     */
    private List<ItineraryCityDateRange> cityDateRanges;

    /**
     * 活动详情
     */
    private ActivityDetailParam activityDetail;

    /**
     * 原活动信息
     */
    private FromActivity fromActivity;

    /**
     * 是否检查交通
     */
    private boolean checkTp = true;

    /**
     * 是否检查与其他活动相邻太近
     */
    private boolean checkTooClose = true;

    @Data
    public static class TimeRange {

        /**
         * 开始时间/目标时间
         * 添加或修改活动时的目标时间点
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
        private LocalDateTime startTime;

        /**
         * 结束时间
         * 修改攻略整体时间时用
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
        private LocalDateTime endTime;

        /**
         * 更新前时间
         * update=2时 修改活动前的时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
        private LocalDateTime fromTime;

        /**
         * 停留时间(分钟)
         */
        private Integer stayMinutes;

        public LocalDateTime getEndTime() {
            if (endTime != null) {
                return endTime;
            }
            if (ObjectUtils.allNotNull(startTime, stayMinutes)) {
                endTime = startTime.plusMinutes(stayMinutes);
            }
            return endTime;
        }
    }

    @Data
    public static class IndexRange {

        private Integer fromIndex;

        private Integer toIndex;

        private List<List<Integer>> overallIndexes;
    }

    @Data
    public static class FromActivity {

        /**
         * 活动类型
         */
        private Integer type;

        /**
         * 活动来源
         */
        private String activityOrigin;

        /**
         * 活动ID
         */
        private String activityId;

        /**
         * 下标
         */
        private Integer index;
    }
}
