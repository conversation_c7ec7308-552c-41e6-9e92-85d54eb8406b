package com.somytrip.entity.itinerary;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.itinerary
 * @className: ItineraryArrangeActivityParam
 * @author: shadow
 * @description: 攻略安排日行程参数
 * @date: 2024/11/27 14:49
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ItineraryArrangeActivityParam extends ItineraryArrangeLevelParam {

    /**
     * 城市code
     */
    private String cityCode;

    /**
     * 起点
     */
    private ItineraryActivityDto startPoint;

    /**
     * 已选择景点ID
     */
    private Set<String> selectedScenicIds = new HashSet<>();

    /**
     * 当前城市预备数据
     */
    private ItineraryPreparedDataDto.PreparedDataDetail curPreparedData;

    /**
     * 当前城市必去活动
     */
    private List<ActivityDetailParam> mustActivities;

    public ItineraryArrangeActivityParam(ItineraryArrangeLevelParam arrangeLevelParam) {
//        super(arrangeLevelParam);
        this.setGenerationParam(arrangeLevelParam.getGenerationParam());
        this.setCityCodeDtoMap(arrangeLevelParam.getCityCodeDtoMap());
        this.setTravelPreferenceMap(arrangeLevelParam.getTravelPreferenceMap());
        this.setSimilarMap(arrangeLevelParam.getSimilarMap());
        selectedScenicIds = new HashSet<>();
    }
}
