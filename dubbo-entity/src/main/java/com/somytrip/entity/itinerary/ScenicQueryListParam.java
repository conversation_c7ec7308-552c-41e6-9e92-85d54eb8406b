package com.somytrip.entity.itinerary;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.somytrip.entity.dto.hotel.PaginationDto;
import com.somytrip.entity.enums.itinerary.ActivitySortType;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.param
 * @className: QueryScenicListParam
 * @author: shadow
 * @description: 查询景点列表参数
 * @date: 2024/7/11 14:38
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ScenicQueryListParam {

    /**
     * 城市code
     */
    @NotNull(message = "{api.city.non-code}")
    private String cityCode;

    /**
     * 城市ID
     */
    private Integer cityId;

    /**
     * 经度
     */
    private String lon;

    /**
     * 纬度
     */
    private String lat;

    /**
     * 距离(公里)
     */
    private Integer distance;

    /**
     * 排序方式
     */
    private Integer sort = ActivitySortType.DISTANCE_ASC.getValue();

    /**
     * 查询关键字
     */
    private String queryText;

    /**
     * 主题ID
     */
    private Integer theme;

    /**
     * 目标ID列表
     */
    private List<String> targetIds;

    /**
     * 排除ID列表
     */
    private List<String> excludeIds;

    /**
     * 查询数量
     */
    private Integer limit;

    /**
     * 语言
     */
    private String lang = Locale.CHINA.toString();

    /**
     * 分页参数
     */
    private PaginationDto pagination;

    public ScenicQueryListParam(ItineraryNearbyActivitySearchParam param) {
        this.cityCode = param.getCityCode();
        this.queryText = param.getQueryText();
        this.lon = param.getLon();
        this.lat = param.getLat();
        this.distance = param.getDistance();
        this.theme = CollUtil.getFirst(param.getThemes());
        this.pagination = param.getPagination();
        String activityIds = param.getActivityIds();
        if (StrUtil.isNotBlank(activityIds)) {
            this.targetIds = new ArrayList<>(Arrays.asList(activityIds.split(",")));
        }
        String fromActivityId = param.getFromActivityId();
        if (StrUtil.isNotBlank(fromActivityId)) {
            this.excludeIds = new ArrayList<>(List.of(fromActivityId));
        }
    }
}
