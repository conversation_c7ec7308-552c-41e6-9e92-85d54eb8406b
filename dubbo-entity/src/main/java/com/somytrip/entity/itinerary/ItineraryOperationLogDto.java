package com.somytrip.entity.itinerary;

import com.alibaba.fastjson2.JSONObject;
import com.somytrip.entity.enums.itinerary.ItineraryOperationType;
import com.somytrip.entity.enums.itinerary.ItinerarySubOperationType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.itinerary
 * @className: ItineraryOperationDto
 * @author: shadow
 * @description: 攻略操作dto
 * @date: 2024/9/24 16:43
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ItineraryOperationLogDto {

    /**
     * 用户ID
     */
    private String uid;

    /**
     * 攻略主键ID
     */
    private Long itineraryId;

    /**
     * 操作类型
     */
    private ItineraryOperationType operationType;

    /**
     * 子操作类型
     */
    private ItinerarySubOperationType subOperationType;

    /**
     * 参数
     */
    private JSONObject params;

    public ItineraryOperationLogDto(String uid, Long itineraryId, ItineraryOperationType operationType) {
        this.uid = uid;
        this.itineraryId = itineraryId;
        this.operationType = operationType;
    }

    public ItineraryOperationLogDto(String uid, Long itineraryId, ItineraryOperationType operationType, ItinerarySubOperationType subOperationType, Object params) {
        this.uid = uid;
        this.itineraryId = itineraryId;
        this.operationType = operationType;
        this.subOperationType = subOperationType;
        this.params = JSONObject.from(params);
    }
}
