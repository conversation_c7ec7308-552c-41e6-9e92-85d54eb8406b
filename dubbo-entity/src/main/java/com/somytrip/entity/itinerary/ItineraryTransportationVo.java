package com.somytrip.entity.itinerary;

import com.alibaba.fastjson2.JSONArray;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.somytrip.entity.enums.itinerary.ItineraryTransportationType;
import com.somytrip.entity.enums.itinerary.TransportationSegmentType;
import com.somytrip.entity.vo.flight.FlightInfoVo;
import com.somytrip.model.flight.enums.ProviderSourceEnum;
import com.somytrip.utils.LocalDateTimeUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.tourism
 * @className: ItineraryTransportationVo
 * @author: shadow
 * @description: 攻略交通数据vo
 * @date: 2024/4/9 23:19
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ItineraryTransportationVo {

    /**
     * 交通方式类型(1:飞机, 2:火车)
     */
    private Integer type;

    /**
     * 行程段类型
     */
    private TransportationSegmentType segmentType;

    /**
     * 班次名称
     * 车次、航班
     */
    private String name;

    /**
     * 交通详细数据
     */
    private JSONArray data;

    /**
     * 预算
     */
    private BigDecimal budget;

    /**
     * 总时长(如: 8h20m)
     */
    private String[] totalDuration;

    /**
     * 总时长
     */
    private String duration;

    /**
     * 到达地
     */
    private String arrival;

    /**
     * 出发地
     */
    private String departure;

    /**
     * 到达时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime arrivalTime;

    /**
     * 出发时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime departureTime;

    public ItineraryTransportationVo(ItineraryTransportationEntity entity) {
        this.type = entity.getTransportationMode();
        if (entity.getTransportationContent() != null) {
            this.data = JSONArray.from(entity.getTransportationContent());
        }
    }

    public ItineraryTransportationVo(ItineraryTransportationType type) {
        this.type = type.getValue();
    }

    /**
     * 交通明细 节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TransportationDetail {

        /**
         * 交通方式类型(1:飞机, 2:火车)
         */
        private Integer type;

        /**
         * 数据来源
         */
        private ProviderSourceEnum sourceType;

        /**
         * 出发机场/车站 名称
         */
        private String departureName;

        /**
         * 到达机场/车站 名称
         */
        private String arrivalName;

        /**
         * 总时长(如: 总8h9m)
         */
        private String totalDuration;

        /**
         * 出发时间
         */
        @JsonFormat(pattern = "HH:mm")
        private LocalTime departureTime;

        /**
         * 到达时间
         */
        @JsonFormat(pattern = "HH:mm")
        private LocalTime arrivalTime;

        /**
         * 跨天数
         * 如: 当到达时间为出发时间的第二天时, plusDays=1
         */
        private Integer plusDays;

        /**
         * 价格
         */
        private String price;

        /**
         * 航空公司名称
         */
        private String airlineDisplay;

        /**
         * 航空公司图标
         */
        private String airlineIcon;

        public TransportationDetail(FlightInfoVo flightInfoVo) {
            if (flightInfoVo == null) {
                return;
            }
            this.type = 1;
            this.sourceType = flightInfoVo.getSourceType();
            this.departureName = flightInfoVo.getDepartureName();
            this.arrivalName = flightInfoVo.getArrivalName();
            this.totalDuration = "总" + flightInfoVo.getTotalDuration()
                    .replace("时", "h")
                    .replace("分", "h");

            String departureTime = flightInfoVo.getDepartureTime();
            LocalDateTime departureDateTime = LocalDateTimeUtil.convertStr2LocalDateTime(departureTime, "yyyy-MM-dd HH:mm:ss");
            String arrivalTime = flightInfoVo.getArrivalTime();
            LocalDateTime arrivalDateTime = LocalDateTimeUtil.convertStr2LocalDateTime(arrivalTime, "yyyy-MM-dd HH:mm:ss");
            this.departureTime = departureDateTime.toLocalTime();
            this.arrivalTime = arrivalDateTime.toLocalTime();
            this.plusDays = Math.toIntExact(ChronoUnit.DAYS.between(departureDateTime.toLocalDate(), arrivalDateTime.toLocalDate()));
            this.airlineDisplay = flightInfoVo.getMarketingAirlineDisplay();
            this.price = String.valueOf(flightInfoVo.getPrice());
        }
    }
}
