package com.somytrip.entity.itinerary;

import com.alibaba.fastjson2.JSONArray;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.somytrip.entity.dto.city.CityDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.i18n.LocaleContextHolder;

import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Locale;

/**
 * @ClassName: StrategyQueryParam
 * @Description: 攻略查询参数
 * @Author: shadow
 * @Date: 2024/3/21 16:35
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ItinerarySearchParam {

    /**
     * 用户ID
     */
    private String uid;

    /**
     * 出发城市code
     */
    private String departureCityCode;

    /**
     * 出发城市ID
     */
    private Integer departureCityId;

    /**
     * 目的地城市code列表
     */
    private List<String> destinationCityCodes;

    /**
     * 目的地城市ID列表
     */
    private List<Integer> destinationCityIds;

    /**
     * 目的地城市名称
     * 多目的地用"|"分隔
     */
    private String destinationCityNames;

    /**
     * 目的地城市dto列表
     */
    private List<CityDto> destinationCityDtoList;

    /**
     * 出发时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime goTime;

    /**
     * 返程时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime backTime;

    /**
     * 天数
     */
    private Integer days;

    /**
     * 攻略类型
     * 1 - 纯玩游
     * 2 - 本地游
     * 3 - 错峰游
     * 10 - DIY
     */
    private Integer itineraryType = 1;

    /**
     * 旅行伴侣
     */
    private Integer travelMate;

    /**
     * 行程节奏
     */
    private Integer travelRhythm = 2;

    /**
     * 旅行预算(1: 经济型, 2: 适中型, 3: 高档型)
     */
    private Integer travelBudget;

    /**
     * 游玩偏好
     */
    private List<TravelPreferenceParam> travelPreference;

    /**
     * 位置信息
     */
    private Location location;

    /**
     * 交通方式
     */
    private Integer tpType;

    /**
     * 城市日期区间
     */
    private List<ItineraryCityDateRange> cityDateRanges;

    /**
     * 必去列表
     */
    private List<ActivityDetailParam> mustActivities;

    /**
     * 国际化
     */
    private Locale locale = Locale.CHINA;

    /**
     * 来自于更新攻略(updateType=13)的攻略主键ID
     */
    private Long fromItineraryId;

    /**
     * 是否插入餐厅占位节点
     */
    private boolean restaurantFlag = true;

    public ItinerarySearchParam(ItineraryInfoEntity infoEntity) {
        this.uid = infoEntity.getUid();
        this.departureCityId = infoEntity.getDepartureCityId();
        this.destinationCityIds = infoEntity.getDestinationCityIds();
        this.goTime = infoEntity.getGoTime();
        this.backTime = infoEntity.getBackTime();
        this.itineraryType = infoEntity.getItineraryType();
        this.tpType = infoEntity.getTpType();
        this.travelMate = infoEntity.getTravelMate();
        this.travelBudget = infoEntity.getTravelBudget();
        this.fromItineraryId = infoEntity.getId();
        if (infoEntity.getTravelPreference() != null) {
            this.travelPreference = JSONArray.from(infoEntity.getTravelPreference()).toJavaList(TravelPreferenceParam.class);
        }
        if (infoEntity.getCityDateRanges() != null) {
            this.cityDateRanges = JSONArray.from(infoEntity.getCityDateRanges()).toJavaList(ItineraryCityDateRange.class);
        }
        if (infoEntity.getMustGoActivities() != null) {
            this.mustActivities = JSONArray.from(infoEntity.getMustGoActivities()).toJavaList(ActivityDetailParam.class);
        }
    }

    public ItinerarySearchParam(ItineraryInfoEntity infoEntity, ItineraryUpdateParamV2 updateParam) {
        this.locale = LocaleContextHolder.getLocale();
        this.uid = infoEntity.getUid();
        this.departureCityId = infoEntity.getDepartureCityId();
        this.destinationCityIds = infoEntity.getDestinationCityIds();
        this.itineraryType = infoEntity.getItineraryType();
        this.tpType = infoEntity.getTpType();
        this.travelMate = infoEntity.getTravelMate();
        this.travelBudget = infoEntity.getTravelBudget();
        this.fromItineraryId = infoEntity.getId();
        if (infoEntity.getTravelPreference() != null) {
            this.travelPreference = JSONArray.from(infoEntity.getTravelPreference()).toJavaList(TravelPreferenceParam.class);
        }
        if (infoEntity.getMustGoActivities() != null) {
            this.mustActivities = JSONArray.from(infoEntity.getMustGoActivities()).toJavaList(ActivityDetailParam.class);
        }

        ItineraryUpdateParamV2.TimeRange timeRange = updateParam.getTimeRange();
        this.goTime = timeRange.getStartTime();
        this.backTime = timeRange.getEndTime();

        List<ItineraryCityDateRange> cityDateRanges = updateParam.getCityDateRanges();
        cityDateRanges = cityDateRanges.stream()
                .sorted(Comparator.comparing(ItineraryCityDateRange::getStartTime, LocalDateTime::compareTo))
                .toList();
        this.cityDateRanges = cityDateRanges;
        this.destinationCityCodes = cityDateRanges.stream().map(ItineraryCityDateRange::getCityCode).toList();
    }

    public ItinerarySearchParam(ItineraryGenerationParam generationParam) {
        this.uid = generationParam.getUid();
        ItineraryGenerationParam.Location location = generationParam.getLocation();
        this.departureCityCode = location.getDepartureCityCode();
        this.destinationCityCodes = location.getDestinationCityCodes();
        this.location = new Location(location);
        ItineraryGenerationParam.TimeRange timeRange = generationParam.getTimeRange();
        this.goTime = timeRange.getGoTime();
        this.backTime = timeRange.getBackTime();
        this.itineraryType = generationParam.getItineraryType();
        ItineraryGenerationParam.Condition condition = generationParam.getCondition();
        this.travelBudget = condition.getTravelBudget();
        this.travelMate = condition.getTravelMate();
        this.travelRhythm = condition.getTravelRhythm();
        this.travelPreference = condition.getTravelPreference();
        this.tpType = condition.getTpType();
        this.cityDateRanges = condition.getCityDateRanges();
//        this.mustActivities = condition.getMustActivities();
    }

    /**
     * 位置信息 节点
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Location {

        /**
         * 经度
         */
        private String lon;

        /**
         * 纬度
         */
        private String lat;

        /**
         * 距离(单位: 米)
         */
        private Integer distance;

        public Location(ItineraryGenerationParam.Location location) {
            this.lon = location.getLon();
            this.lat = location.getLat();
            this.distance = location.getDistance();
        }

        public boolean empty() {
            return StringUtils.isBlank(lon) || StringUtils.isBlank(lat);
        }
    }
}
