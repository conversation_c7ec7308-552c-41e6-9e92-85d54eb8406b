package com.somytrip.entity.itinerary;

import com.somytrip.entity.enums.itinerary.ItineraryActivityType;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.tourism
 * @className: ItineraryGetDayNextScenicDto
 * @author: shadow
 * @description: 攻略获取当天下一个景区dto
 * @date: 2024/4/10 16:22
 * @version: 1.0
 */
@Data
public class ItineraryGetDayNextScenicDto {

    /**
     * 第几天
     */
    private Integer dayNum;

    /**
     * 预备景区数据
     */
    private List<ItineraryActivityDto> preparedScenicList;

    /**
     * 已选择活动列表
     */
    private List<ItineraryActivityDto> selectedActivityList;

    /**
     * 已选择活动ID
     */
    private Set<String> selectedIds;

    /**
     * 当天剩余时间(小时)
     */
    private BigDecimal remainingHours;

    /**
     * 上一个活动
     */
    private ItineraryActivityDto lastActivity;

    /**
     * 当前活动开始时间
     */
    private LocalDateTime curStartDateTime;

    /**
     * 活动间距(km)
     */
    private Integer activitySpacing;

    /**
     * 活动最大间距(km)
     */
    private Integer activityMaxSpacing;

    /**
     * 位置信息
     */
    private ItinerarySearchParam.Location location;

    /**
     * 游玩偏好map
     * md5Key - 下标
     * value - 主题id列表
     */
    private Map<Integer, List<Integer>> travelPreferenceMap;

    /**
     * 已选择主题map
     * md5Key - 主题id
     * value - 选择次数
     */
    private Map<Integer, Integer> selectedThemeCountMap;

    /**
     * 当前是否选择夜游(true: 可以选择夜游, false: 不可以)
     */
    private boolean evening = Boolean.FALSE;

    /**
     * 必去活动
     */
    private Map<ItineraryActivityType, List<ItineraryActivityDto>> mustActivityMap;

    /**
     * 相似景点map
     * key -> 相似值
     * value -> 该相似值已安排个数
     */
    private Map<Integer, Integer> similarMap = new HashMap<>();
}
