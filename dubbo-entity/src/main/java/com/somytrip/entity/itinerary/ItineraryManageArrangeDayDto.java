package com.somytrip.entity.itinerary;

import lombok.Data;

import java.util.List;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.itinerary
 * @className: ItineraryManageArrangeDayDto
 * @author: shadow
 * @description: 安排一日行程参数dto(管理)
 * @date: 2024/6/28 16:56
 * @version: 1.0
 */
@Data
public class ItineraryManageArrangeDayDto {

    /**
     * 攻略序列号
     */
    private String itinerarySn;

    /**
     * 第几天
     */
    private Integer dayNum;

    /**
     * 活动列表
     */
    private List<ActivityDetailParam> activities;
}
