package com.somytrip.entity.itinerary;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.somytrip.entity.dto.city.CityDto;
import com.somytrip.entity.enums.itinerary.ItineraryActivityType;
import com.somytrip.entity.enums.itinerary.ItineraryTransportationType;
import com.somytrip.entity.enums.itinerary.ItineraryType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.tourism
 * @className: ItineraryPrepareDataParam
 * @author: shadow
 * @description: 攻略预备数据查询参数dto
 * @date: 2024/4/12 14:22
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ItineraryPrepareDataParam {

    /**
     * 用户ID
     */
    private String uid;

    /**
     * 攻略序列号
     */
    private String itinerarySn;

    /**
     * 攻略类型
     * 1 - 纯玩游
     * 2 - 本地游
     * 3 - 错峰游
     * 10 - DIY
     */
    private Integer itineraryType;

    /**
     * 出发城市ID
     */
    private Integer departureCityId;

    /**
     * 目的地城市ID
     */
    private Integer destinationCityId;

    /**
     * 目的地城市ID列表
     */
    private List<Integer> destinationCityIds;

    /**
     * 目的地城市Code列表
     */
    private List<String> destinationCityCodes;

    /**
     * 目的地城市ID Map
     */
    private Map<String, Integer> destinationCityIdMap;

    /**
     * 出发地城市dto
     */
    private CityDto departureCityDto = null;

    /**
     * 目的地城市dto列表
     */
    private List<CityDto> destinationCityDtoList = null;

    /**
     * 出发时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime goTime;

    /**
     * 返程时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime backTime;

    /**
     * 旅行伴侣
     */
    private Integer travelMate;

    /**
     * 预算等级
     */
    private Integer travelBudget;

    /**
     * 交通方式
     */
    private ItineraryTransportationType tpType;

    /**
     * 要获取的活动类型列表
     */
    private List<ItineraryActivityType> activityTypes = new ArrayList<>();

    /**
     * 是否请求交通
     */
    private boolean queryTp = false;

    /**
     * 城市日期区间
     */
    private List<ItineraryCityDateRange> cityDateRanges;

    /**
     * 是否是生成攻略过程中
     */
    private boolean generating = false;

    /**
     * 语言
     */
    private Locale locale = Locale.CHINA;

    public ItineraryPrepareDataParam(ItinerarySearchParam searchParam) {
        this.uid = searchParam.getUid();
        this.itineraryType = searchParam.getItineraryType();
        this.goTime = searchParam.getGoTime();
        this.backTime = searchParam.getBackTime();
        this.travelMate = searchParam.getTravelMate();
        this.travelBudget = searchParam.getTravelBudget();
        Integer tpType = searchParam.getTpType();
        this.tpType = ItineraryTransportationType.fromValue(tpType);
        this.activityTypes = new ArrayList<>(List.of(ItineraryActivityType.SCENIC, ItineraryActivityType.HOTEL));
        this.cityDateRanges = searchParam.getCityDateRanges();
        this.destinationCityIds = searchParam.getDestinationCityIds();
        this.destinationCityCodes = searchParam.getDestinationCityCodes();
        this.destinationCityDtoList = searchParam.getDestinationCityDtoList();
        this.departureCityId = searchParam.getDepartureCityId();
        this.generating = true;
        this.queryTp = ItineraryType.fromValue(searchParam.getItineraryType()).isQueryTp()
                && (tpType == 1 || tpType == 2)
                && this.departureCityId != null;
        this.locale = searchParam.getLocale();
    }

    public ItineraryPrepareDataParam(ItineraryGenerationParam generationParam) {
        this.uid = generationParam.getUid();
        this.itineraryType = generationParam.getItineraryType();
        ItineraryGenerationParam.TimeRange timeRange = generationParam.getTimeRange();
        this.goTime = timeRange.getGoTime();
        this.backTime = timeRange.getBackTime();
        ItineraryGenerationParam.Condition condition = generationParam.getCondition();
        this.travelMate = condition.getTravelMate();
        this.travelBudget = condition.getTravelBudget();
        this.queryTp = true;
        this.tpType = ItineraryTransportationType.fromValue(condition.getTpType());
        this.activityTypes = new ArrayList<>(List.of(ItineraryActivityType.SCENIC));
        this.cityDateRanges = condition.getCityDateRanges();
        ItineraryGenerationParam.Location location = generationParam.getLocation();
        this.destinationCityCodes = location.getDestinationCityCodes();
        this.generating = true;
        this.queryTp = ItineraryType.fromValue(generationParam.getItineraryType()).isQueryTp();
    }

    public void setCityDateRanges(List<ItineraryCityDateRange> cityDateRanges) {
        this.cityDateRanges = cityDateRanges;
        if (cityDateRanges != null && !cityDateRanges.isEmpty()) {
            // 根据城市日期区间修改总往返时间
            this.goTime = cityDateRanges.get(0).getStartTime();
            this.backTime = cityDateRanges.get(cityDateRanges.size() - 1).getEndTime();
        }
    }
}
