package com.somytrip.entity.itinerary;

import com.somytrip.entity.dto.tourism.TourismConfigList;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.itinerary
 * @className: TravelPreferenceParam
 * @author: shadow
 * @description: 游玩偏好参数对象
 * @date: 2024/11/21 9:57
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TravelPreferenceParam {

    /**
     * 类型code
     */
    private String typeValue = "1";

    /**
     * 选择下标
     */
    private Integer index = 1;

    /**
     * 值
     * 0-100
     */
    private Float value;

    public TravelPreferenceParam(TourismConfigList.TravelPreferenceConfig config) {
        this.typeValue = config.getValue();
        this.index = config.getDefaultIndex();
    }
}
