package com.somytrip.entity.itinerary;

import com.somytrip.entity.dto.city.CityDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.itinerary
 * @className: ItineraryArrangeLevelParam
 * @author: shadow
 * @description: 攻略安排预算等级行程参数
 * @date: 2024/11/26 15:17
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ItineraryArrangeLevelParam {

    /**
     * 攻略生成参数
     */
    private ItineraryGenerationParam generationParam;

    /**
     * 城市code-dto映射map
     */
    private Map<String, CityDto> cityCodeDtoMap;

    /**
     * 日期城市map
     */
    private Map<LocalDate, String> dateCityMap;

    /**
     * 预备数据
     */
    private ItineraryPreparedDataDto preparedData;

    /**
     * 游玩偏好map
     * 结构: {0: [], 1: [], 2: []}
     */
    private Map<Integer, List<Integer>> travelPreferenceMap;

    /**
     * 相似景点map
     * key -> 相似景点类型
     * value -> 数量
     */
    private Map<Integer, Integer> similarMap = new HashMap<>();
}
