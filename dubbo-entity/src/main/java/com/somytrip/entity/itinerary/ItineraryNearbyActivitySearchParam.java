package com.somytrip.entity.itinerary;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.somytrip.entity.dto.hotel.PaginationDto;
import com.somytrip.entity.enums.itinerary.ActivitySortType;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.tourism
 * @className: ItineraryNearbyActivitySearchParam
 * @author: shadow
 * @description: 攻略附近活动查询参数
 * @date: 2024/4/20 15:26
 * @version: 1.0
 */
@Data
public class ItineraryNearbyActivitySearchParam {

    /**
     * 活动类型
     */
    @NotNull(message = "{api.itinerary.non-activity-type}")
    private Integer activityType;

    /**
     * 活动ID列表
     */
    private String activityIds;

    /**
     * 城市code
     */
    @NotNull(message = "{api.itinerary.non-city-code}")
    private String cityCode;

    /**
     * 起始活动来源
     */
    private String fromActivityOrigin;

    /**
     * 起始活动ID
     */
    private String fromActivityId;

    /**
     * 经度
     */
    private String lon;

    /**
     * 纬度
     */
    private String lat;

    /**
     * 距离(公里)
     */
    private Integer distance;

    /**
     * 排序方式
     */
    private Integer sort = ActivitySortType.DISTANCE_ASC.getValue();

    /**
     * 主题ID列表
     */
    private List<Integer> themes;

    /**
     * 结果类型
     */
    private List<String> resultTypes;

    /**
     * 搜索关键字
     */
    private String queryText;

    /**
     * 预算等级(1: 经济型, 2: 适中型, 3: 高档型)
     */
    private Integer budgetLevel;

    /**
     * 日期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime datetime;

    /**
     * 分页参数
     */
    private PaginationDto pagination;
}
