package com.somytrip.entity.itinerary;

import com.somytrip.entity.dto.hotel.PaginationDto;
import lombok.Data;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.itinerary
 * @className: ItineraryManageQueryHotListDto
 * @author: shadow
 * @description: 查询热门攻略列表参数dto(管理)
 * @date: 2024/6/28 15:29
 * @version: 1.0
 */
@Data
public class ItineraryManageQueryHotListDto {

    /**
     * 是否只查询可用
     * true - 只查询可用
     * false - 查全部
     */
    private boolean valid;

    /**
     * 分页参数
     */
    private PaginationDto pagination;
}
