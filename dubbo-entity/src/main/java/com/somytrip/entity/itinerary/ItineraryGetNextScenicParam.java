package com.somytrip.entity.itinerary;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.itinerary
 * @className: ItineraryGetNextScenicParam
 * @author: shadow
 * @description: 攻略获取下一个景点参数
 * @date: 2024/11/27 17:27
 * @version: 1.0
 */
@Data
public class ItineraryGetNextScenicParam {

    /**
     * 生成攻略参数
     */
    private ItineraryGenerationParam generationParam;

    /**
     * 预备景点列表
     */
    private List<ItineraryActivityDto> preparedScenicList;

    /**
     * 上一个活动
     */
    private ItineraryActivityDto lastActivity;

    /**
     * 剩余可用小时数
     */
    private BigDecimal remainingHours;
}
