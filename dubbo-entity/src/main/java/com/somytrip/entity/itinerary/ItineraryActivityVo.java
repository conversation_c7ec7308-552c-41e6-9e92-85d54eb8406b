package com.somytrip.entity.itinerary;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.somytrip.entity.po.amap.PoiItemResp;
import com.somytrip.entity.vo.hotel.HotelListVo;
import com.somytrip.utils.BigDecimalUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.tourism
 * @className: ItineraryActivityVo
 * @author: shadow
 * @description: 攻略活动vo
 * @date: 2024/4/8 11:07
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ItineraryActivityVo {

    /**
     * 活动类型(1: 景区, 2: 餐饮, 3: 酒店)
     */
    private Integer type;

    /**
     * 活动数据来源
     * (目前只支持酒店)
     */
    private String activityOrigin;

    /**
     * 活动ID
     */
    private String activityId;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 主题ID列表
     */
    private List<Integer> themes = new ArrayList<>();

    /**
     * 评分
     */
    private String score;

    /**
     * 星级
     */
    private Integer star;

    /**
     * 短介绍
     */
    private String shortIntro;

    /**
     * 地址
     */
    private String address;

    /**
     * 商圈
     */
    private String businessZone;

    /**
     * 经度
     */
    private String lon;

    /**
     * 纬度
     */
    private String lat;

    /**
     * 城市code
     */
    private String cityCode;

    /**
     * 图片列表(最多3张)
     */
    private List<String> images;

    /**
     * 价格(如: 免费、￥251元起)
     */
    private String startPrice;

    /**
     * 停留开始时间(如: 09:00)
     */
    @JsonFormat(pattern = "HH:mm")
    private LocalTime stayTimeStart;

    /**
     * 停留结束时间(如: 18:00)
     */
    @JsonFormat(pattern = "HH:mm")
    private LocalTime stayTimeEnd;

    /**
     * 停留开始日期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime stayDateTimeStart;

    /**
     * 停留结束日期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime stayDateTimeEnd;

    /**
     * 停留时间段(如: 18:00 - 19:00、20:00)
     */
    private String stayTimePeriod;

    /**
     * 预计停留时间(几小时)
     */
    private String stayTime;

    /**
     * 预计停留时间数值
     */
    private BigDecimal stayTimeValue = BigDecimal.valueOf(2);

    /**
     * 推荐游玩时间
     */
    private BigDecimal recommendedPlaytime;

    /**
     * 到下一地点距离
     */
    private String distanceToNext;

    /**
     * 到下一地点距离值(米)
     */
    private BigDecimal distanceToNextValue;

    /**
     * 到下一地点花费时间
     */
    private String timeToNext;

    /**
     * 到下一个地点花费小时数值
     */
    private BigDecimal hoursToNext;

    /**
     * 到下一地点的交通方式
     */
    private Integer transportationModeToNext;

    public ItineraryActivityVo(ItineraryActivityDto dto) {
        this.type = dto.getType();
        this.activityOrigin = dto.getActivityOrigin();
        this.activityId = dto.getActivityId();
        this.activityName = dto.getActivityName();
        this.themes = dto.getThemes();
        this.score = dto.getScore();
        this.star = dto.getStar();
        this.shortIntro = dto.getShortIntro();
        this.address = dto.getAddress();
        this.businessZone = dto.getBusinessZone();
        this.lon = dto.getLon();
        this.lat = dto.getLat();
        this.cityCode = dto.getCityCode();
        this.images = dto.getImages();
        this.startPrice = dto.getStartPrice();
        this.stayTimeStart = dto.getStayTimeStart();
        this.stayTimeEnd = dto.getStayTimeEnd();
        this.stayDateTimeStart = dto.getStayDateTimeStart();
        this.stayDateTimeEnd = dto.getStayDateTimeEnd();
        this.stayTimePeriod = dto.getStayTimePeriod();
        this.stayTime = dto.getStayTime();
        this.stayTimeValue = dto.getStayTimeValue();
        this.recommendedPlaytime = dto.getRecommendedPlaytime();
        this.distanceToNext = dto.getDistanceToNext();
        this.distanceToNextValue = dto.getDistanceToNextValue();
        this.timeToNext = dto.getTimeToNext();
        this.hoursToNext = dto.getHoursToNext();
        this.transportationModeToNext = dto.getTransportationModeToNext();
    }

    public ItineraryActivityVo(HotelListVo hotelListVo) {
        this.type = 3;
        this.activityOrigin = hotelListVo.getHotelOrigin().toString();
        this.activityId = hotelListVo.getHotelId();
        this.activityName = hotelListVo.getHotelName();
        this.cityCode = hotelListVo.getCityCode();
        this.score = hotelListVo.getScore();
        this.star = hotelListVo.getStar() != null ? hotelListVo.getStar() : hotelListVo.getCategory();
        this.address = hotelListVo.getAddress();
        this.businessZone = hotelListVo.getBusinessZoneName();
        this.lon = hotelListVo.getLon();
        this.lat = hotelListVo.getLat();
        this.startPrice = hotelListVo.getStartPrice();
        if (StringUtils.isNotBlank(hotelListVo.getCover())) {
            this.images = new ArrayList<>(List.of(hotelListVo.getCover()));
        }
    }

    public ItineraryActivityVo(ItineraryDayEntity.ActivityEntity activityEntity) {
        this.stayTimeStart = activityEntity.getStayTimeStart();
        this.stayTimeEnd = activityEntity.getStayTimeEnd();
        this.stayDateTimeStart = activityEntity.getStayDateTimeStart();
        this.stayDateTimeEnd = activityEntity.getStayDateTimeEnd();
        this.stayTimePeriod = activityEntity.getStayTimePeriod();
        this.stayTime = activityEntity.getStayTime();
        this.stayTimeValue = activityEntity.getStayTimeValue();
        this.recommendedPlaytime = activityEntity.getRecommendedPlaytime();
        this.distanceToNext = activityEntity.getDistanceToNext();
        this.distanceToNextValue = activityEntity.getDistanceToNextValue();
        this.timeToNext = activityEntity.getTimeToNext();
        this.hoursToNext = activityEntity.getHoursToNext();
        this.transportationModeToNext = activityEntity.getTransportationModeToNext();

        this.type = activityEntity.getType();
        this.activityOrigin = activityEntity.getActivityOrigin();
        this.activityId = activityEntity.getActivityId();
        this.activityName = activityEntity.getActivityName();
        this.themes = activityEntity.getThemes();
        this.score = activityEntity.getScore();
        this.address = activityEntity.getAddress();
        this.lon = activityEntity.getLon();
        this.lat = activityEntity.getLat();
        this.cityCode = activityEntity.getCityCode();
        this.images = activityEntity.getImages();
        this.startPrice = activityEntity.getStartPrice();
    }

    public ItineraryActivityVo(PoiItemResp poiItemResp, String cityCode) {
        this.type = 2;
        this.activityOrigin = "AMap";
        this.activityId = poiItemResp.getId();
        this.cityCode = cityCode;
        if (StringUtils.isNotBlank(poiItemResp.getLocation())) {
            String[] locationArr = poiItemResp.getLocation().split(",");
            this.lon = locationArr[0];
            this.lat = locationArr[1];
        }
        this.activityName = poiItemResp.getName();
        this.address = poiItemResp.getAddress();
        if (poiItemResp.getPhotos() != null && !poiItemResp.getPhotos().isEmpty()) {
            this.images = poiItemResp.getPhotos().stream().map(PoiItemResp.Photo::getUrl).limit(3).toList();
        }
        if (StringUtils.isNotBlank(poiItemResp.getBusiness().getCost())) {
            BigDecimal cost = new BigDecimal(poiItemResp.getBusiness().getCost());
            this.startPrice = BigDecimalUtil.halfAdjust2Str(cost, 0);
        }
        this.score = poiItemResp.getBusiness().getRating();
    }

    public void setStayDateTimeStart(LocalDateTime stayDateTimeStart) {
        this.stayDateTimeStart = stayDateTimeStart;
        if (stayDateTimeStart != null) {
            this.stayTimeStart = stayDateTimeStart.toLocalTime();
        }
    }

    public void setStayDateTimeEnd(LocalDateTime stayDateTimeEnd) {
        this.stayDateTimeEnd = stayDateTimeEnd;
        if (stayDateTimeEnd != null) {
            this.stayTimeEnd = stayDateTimeEnd.toLocalTime();
        }
    }
}
