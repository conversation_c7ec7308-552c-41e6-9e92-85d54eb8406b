package com.somytrip.entity.itinerary;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.itinerary
 * @className: ItinerarySaveAsParam
 * @author: shadow
 * @description: 攻略另存为参数
 * @date: 2024/9/13 10:51
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ItinerarySaveAsParam {

    /**
     * 攻略序列号
     */
    @NotNull(message = "{api.itinerary.non-itinerary-sn}")
    private String itinerarySn;

    /**
     * 攻略标题
     */
    private String title;

    /**
     * 出发时间
     */
    private LocalDate goTime;

    /**
     * 用户ID
     */
    private String uid;

    public ItinerarySaveAsParam(ItineraryCopyReq copyReq) {
        this.itinerarySn = copyReq.getItinerarySn();
        this.title = copyReq.getTitle();
        this.goTime = copyReq.getGoTime();
        this.uid = copyReq.getUid();
    }
}
