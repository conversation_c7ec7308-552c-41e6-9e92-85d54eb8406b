package com.somytrip.entity.itinerary;

import com.somytrip.entity.enums.itinerary.DaysUnitLocale;
import com.somytrip.entity.vo.CitySimpleVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.context.i18n.LocaleContextHolder;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Locale;

/**
 * @ClassName: ItineraryDetailVo
 * @Description: 攻略详情
 * @Author: shadow
 * @Date: 2024/3/21 17:11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ItineraryDetailVo {

    /**
     * 用户ID
     */
    private String uid;

    /**
     * 攻略序列号
     */
    private String itinerarySn;

    /**
     * 攻略标题
     */
    private String itineraryTitle;

    /**
     * 攻略类型
     */
    private Integer itineraryType;

    /**
     * 天数(如: 5天)
     */
    private String days;

    /**
     * 攻略年份
     */
    private String itineraryYear;

    /**
     * 出发城市名称
     */
    private CitySimpleVo departureCity;

    /**
     * 预算等级(用户选择)
     */
    private Integer budgetLevel;

    /**
     * 预算
     */
    private BigDecimal budget;

    /**
     * 旅行伴侣(如: 亲子)
     */
    private String travelMate;

    /**
     * 行程节奏
     */
    private String travelRhythm;

    /**
     * 各城市日期区间
     */
    private List<ItineraryCityDateRange> cityDateRanges;

    /**
     * 预算等级列表
     */
    private List<ItineraryBudgetLevelVo> budgetLevels;

    /**
     * 旅行指南
     */
    private List<ItineraryGuidebookVo> guidebook;

    public ItineraryDetailVo(ItineraryInfoEntity infoEntity) {

        // 国际化
        Locale locale = LocaleContextHolder.getLocale();

        // 用户ID
        this.uid = infoEntity.getUid();
        // 攻略序列号
        this.itinerarySn = infoEntity.getItinerarySn();
        // 攻略标题
        this.itineraryTitle = infoEntity.getItineraryTitle();
        // 公路而类型
        this.itineraryType = infoEntity.getItineraryType();
        // 天数
        if (infoEntity.getDays() != null) {
            String daysUnit = DaysUnitLocale.fromLocale(locale);
            this.days = infoEntity.getDays() + daysUnit;
        }
        // 年份
        LocalDateTime goTime = infoEntity.getGoTime();
        if (goTime != null) {
            this.itineraryYear = String.valueOf(goTime.getYear());
        }
        // 当前预算等级
        this.budgetLevel = infoEntity.getTravelBudget();
        // 预算
        this.budget = infoEntity.getBudget();
        // 各城市日期区间
        this.cityDateRanges = infoEntity.getCityDateRanges();
    }
}
