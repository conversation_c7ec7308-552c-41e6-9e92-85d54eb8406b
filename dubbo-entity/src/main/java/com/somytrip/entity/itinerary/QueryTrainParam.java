package com.somytrip.entity.itinerary;

import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.param
 * @className: QueryTrainParam
 * @author: shadow
 * @description: 查询火车参数
 * @date: 2024/5/16 14:27
 * @version: 1.0
 */
@Data
public class QueryTrainParam {

    /**
     * 出发城市ID
     */
    private Integer departureCityId;

    /**
     * 目的地城市ID
     */
    private Integer destinationCityId;

    /**
     * 出发日期
     */
    private LocalDate goDate;

    /**
     * 返程日期
     */
    private LocalDate backDate;

    /**
     * 城市列表(多城市)
     */
    private List<List<Integer>> cityList;

    /**
     * 日期列表(多城市)
     */
    private List<LocalDate> dateList;
}
