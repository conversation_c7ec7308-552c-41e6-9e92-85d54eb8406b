package com.somytrip.entity.itinerary;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.tourism
 * @className: ItineraryGetDayHotelDto
 * @author: shadow
 * @description: 攻略获取每日酒店参数dto
 * @date: 2024/5/24 9:37
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ItineraryGetDayHotelDto {

    /**
     * 预备酒店列表
     */
    private List<ItineraryActivityDto> preparedHotelList;

    /**
     * 当天已选择活动列表
     */
    private List<ItineraryActivityDto> selectedActivityList;

    /**
     * 上一个活动
     */
    private ItineraryActivityDto lastActivity;

    /**
     * 上一个酒店
     */
    private ItineraryActivityDto lastHotel;

    /**
     * 攻略查询参数
     */
    private ItinerarySearchParam itinerarySearchParam;

    /**
     * 最大间距
     */
    private Integer maxSpacing;

    public ItineraryGetDayHotelDto(ItineraryArrangeDayListDto dto) {
        this.preparedHotelList = dto.getPreparedData().getPreparedHotelList();
        this.lastHotel = dto.getLastHotel();
        this.itinerarySearchParam = dto.getItinerarySearchParam();
        this.maxSpacing = dto.getGetDayNextScenicDto().getActivityMaxSpacing();
    }
}
