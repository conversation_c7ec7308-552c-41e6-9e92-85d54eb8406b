package com.somytrip.entity.itinerary;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Map;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.itinerary
 * @className: ItineraryGuidebookEntity
 * @author: shadow
 * @description: 攻略指南Entity
 * @date: 2024/8/6 14:57
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("itinerary_guidebook")
public class ItineraryGuidebookEntity implements Serializable {

    @Serial
    private final static long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 城市ID
     */
    private Integer cityId;

    /**
     * 城市名称
     */
    private String cityNameCn;

    /**
     * 国家iD
     */
    private Integer countryId;

    /**
     * 报警电话
     */
    private String alarmTel;

    /**
     * 旅游局电话
     */
    private String tourismAdTel;

    /**
     * 中国大使馆电话
     */
    private String chinaEmbassyTel;

    /**
     * 城市概况
     */
    private String brief;

    /**
     * 最佳出游时间
     */
    private String tripTime;

    /**
     * 实用信息
     */
    private String actMessage;

    /**
     * 国际化字典
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Map<String, String>> localeDict;
}
