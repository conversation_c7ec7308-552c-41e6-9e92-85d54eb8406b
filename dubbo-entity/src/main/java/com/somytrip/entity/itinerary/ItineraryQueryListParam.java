package com.somytrip.entity.itinerary;

import com.somytrip.entity.dto.hotel.PaginationDto;
import lombok.Data;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.tourism
 * @className: ItineraryQueryListParam
 * @author: shadow
 * @description: 攻略列表查询参数
 * @date: 2024/4/13 17:36
 * @version: 1.0
 */
@Data
public class ItineraryQueryListParam {

    /**
     * 用户ID
     */
    private String uid;

    /**
     * 搜索关键字
     */
    private String queryText;

    /**
     * 年
     */
    private Integer year;

    /**
     * 分页参数
     */
    private PaginationDto pagination = new PaginationDto();
}
