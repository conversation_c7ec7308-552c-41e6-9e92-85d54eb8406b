package com.somytrip.entity.itinerary;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.tourism
 * @className: ItineraryListVo
 * @author: shadow
 * @description: 攻略列表vo
 * @date: 2024/4/13 17:14
 * @version: 1.0
 */
@Data
public class ItineraryListVo {

    /**
     * 年(如: 2024年)
     */
    private String year;

    /**
     * 该年方案列表
     */
    private List<ItineraryListDetail> list;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ItineraryListDetail {

        /**
         * 攻略序列号
         */
        private String itinerarySn;

        /**
         * 攻略标题
         */
        private String title;

        /**
         * 出发城市
         */
        private String departureCity;

        /**
         * 目的地城市名称
         */
        private String destinationCityNames;

        /**
         * 日期
         */
        private String date;
//        @JsonFormat(pattern = "M月d日")
//        private LocalDate date;

        /**
         * 天数
         */
        private String days;

        /**
         * 旅行伴侣
         */
        private String travelMate;

        /**
         * 旅行伴侣数值
         */
        private Integer travelMateValue;

        /**
         * 封面
         */
        private String cover;

        /**
         * 攻略创建时间
         */
        private String createTime;
//        @JsonFormat(pattern = "M月d日 HH:mm:ss")
//        private LocalDateTime createTime;
    }
}
