package com.somytrip.entity.itinerary;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.itinerary
 * @className: ItineraryGenerationParam
 * @author: shadow
 * @description: 攻略生成参数
 * @date: 2024/11/20 14:35
 * @version: 1.0
 */
@Data
public class ItineraryGenerationParam {

    /**
     * 攻略类型
     */
    private Integer itineraryType;

    /**
     * 时间区间
     */
    private TimeRange timeRange;

    /**
     * 用户ID
     */
    private String uid;

    /**
     * 位置信息
     */
    private Location location;

    /**
     * 生成条件
     */
    private Condition condition;

    @Data
    public static class TimeRange {

        /**
         * 出发时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
        private LocalDateTime goTime;

        /**
         * 返程时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
        private LocalDateTime backTime;

        public Integer days() {
            return (int) goTime.toLocalDate().until(backTime.toLocalDate(), ChronoUnit.DAYS) + 1;

        }
    }

    @Data
    public static class Location {

        /**
         * 出发城市code
         */
        private String departureCityCode;

        /**
         * 目的地城市code列表
         */
        private List<String> destinationCityCodes;

        /**
         * 经度
         */
        private String lon;

        /**
         * 纬度
         */
        private String lat;

        /**
         * 距离(单位: 米)
         */
        private Integer distance;

        public List<String> allCityCodes() {
            ArrayList<String> codes = new ArrayList<>(List.of(departureCityCode));
            codes.addAll(destinationCityCodes);
            return codes;
        }
    }

    @Data
    public static class Condition {

        /**
         * 旅行伴侣
         */
        private Integer travelMate;

        /**
         * 行程节奏
         */
        private Integer travelRhythm = 2;

        /**
         * 旅行预算(1: 经济型, 2: 适中型, 3: 高档型)
         */
        private Integer travelBudget;

        /**
         * 游玩偏好
         */
        private List<TravelPreferenceParam> travelPreference;

        /**
         * 交通方式
         */
        private Integer tpType;

        /**
         * 城市日期区间
         */
        private List<ItineraryCityDateRange> cityDateRanges;

        /**
         * 必去列表
         */
//        private List<ActivityDetailParam> mustActivities;
        private List<MustActivityCity> mustActivityCities;
    }

    @Data
    public static class MustActivityCity {

        /**
         * 城市code
         */
        private String cityCode;

        /**
         * 当前城市必去活动列表
         */
        private List<ActivityDetailParam> activities;
    }
}
