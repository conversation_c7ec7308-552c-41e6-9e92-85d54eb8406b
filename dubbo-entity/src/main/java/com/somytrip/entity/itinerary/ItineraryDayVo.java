package com.somytrip.entity.itinerary;

import com.alibaba.fastjson2.JSONArray;
import com.somytrip.entity.vo.CitySimpleVo;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.tourism
 * @className: ItineraryDayVo
 * @author: shadow
 * @description: 攻略每日数据vo
 * @date: 2024/4/9 23:43
 * @version: 1.0
 */
@Data
public class ItineraryDayVo {

    /**
     * 日期
     */
    private LocalDate date;

    /**
     * 周几
     */
    private String week;

    /**
     * 第几天
     */
    private Integer dayNum;

    /**
     * 当天城市
     */
    private CitySimpleVo cityInfo;

    /**
     * 活动列表
     */
    private List<ItineraryActivityVo> activityList;

    /**
     * 交通数据
     */
    private List<ItineraryTransportationVo> transportations;

    public void setTransportations(List<ItineraryTransportationVo> transportations) {
        if (transportations != null) {
            this.transportations = JSONArray.from(transportations).toJavaList(ItineraryTransportationVo.class);
        }
    }
}
