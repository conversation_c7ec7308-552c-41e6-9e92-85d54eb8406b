package com.somytrip.entity.itinerary;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.tourism
 * @className: ItineraryAddActivityParam
 * @author: shadow
 * @description: 攻略添加活动参数
 * @date: 2024/4/15 17:58
 * @version: 1.0
 */
@Data
public class ItineraryUpdateParam {

    /**
     * 更新类型
     * 1 - 添加活动
     * 2 - 修改活动
     * 3 - 删除活动
     * 4 - 更换活动
     * 10 - 修改攻略时间
     * 11 - 修改攻略标题
     * 12 - 修改预算等级
     * 13 - 修改多城市日期安排
     */
    @NotNull(message = "{api.itinerary-non-update-type}")
    private Integer updateType;

    /**
     * 用户ID
     */
    private String uid;

    /**
     * 攻略序列号
     */
    @NotNull(message = "{api.itinerary.non-itinerary-sn}")
    private String itinerarySn;

    /**
     * 预算等级
     */
    private Integer budgetLevel;

    /**
     * 标题
     */
    private String title;

    /**
     * 停留时间(分钟)
     */
    private Integer stayMinutes;

    /**
     * 开始时间/目标时间
     * 添加或修改活动时的目标时间点
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime startTime;

    /**
     * 结束时间
     * 修改攻略整体时间时用
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime endTime;

    /**
     * 更新前时间
     * update=2时 修改活动前的时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime fromTime;

    /**
     * 智能分配
     */
    private Boolean ai;

    /**
     * 原活动ID
     * updateType=4时用 表示被更换活动的ID
     */
    private String fromActivityId;

    /**
     * 各城市日期区间
     */
    private List<ItineraryCityDateRange> cityDateRanges;

    /**
     * 活动详情
     */
    private ActivityDetailParam activityDetail;
}
