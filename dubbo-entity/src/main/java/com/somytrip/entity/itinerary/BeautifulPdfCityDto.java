package com.somytrip.entity.itinerary;

import com.somytrip.entity.dto.city.GlobalCityEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.itinerary
 * @className: BeautifulPdfCityDto
 * @author: shadow
 * @description: 美观pdf城市dto
 * @date: 2025/1/7 15:50
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BeautifulPdfCityDto {

    private String cityCode;

    private String cityNameCn;

    private String cityNameEn;

    private Integer countryId;

    public BeautifulPdfCityDto(GlobalCityEntity entity) {
        this.cityCode = entity.getCityCode();
        this.cityNameCn = entity.getCityNameCn();
        this.cityNameEn = entity.getCityNameEn();
        this.countryId = entity.getCountryId();
    }
}
