package com.somytrip.entity.itinerary;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.itinerary
 * @className: ItineraryUserConditionEntity
 * @author: shadow
 * @description: 攻略用户查询条件Entity
 * @date: 2024/11/12 14:45
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "itinerary_user_conditions")
public class ItineraryUserConditionEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = -7011461301423964415L;

    /**
     * 自增主见ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private String uid;

    /**
     * 查询条件
     */
    @TableField(typeHandler = Fastjson2TypeHandler.class)
    private ItinerarySearchParam queryCondition;

    /**
     * 是否可用
     */
    @TableField(value = "is_active")
    private Boolean active;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    public ItineraryUserConditionEntity(String uid, ItinerarySearchParam queryCondition) {
        this.uid = uid;
        this.queryCondition = queryCondition;
    }
}
