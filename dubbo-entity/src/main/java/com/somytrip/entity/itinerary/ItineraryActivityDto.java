package com.somytrip.entity.itinerary;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONArray;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.somytrip.entity.po.amap.PoiItemResp;
import com.somytrip.entity.vo.hotel.HotelDetailVo;
import com.somytrip.entity.vo.hotel.HotelListVo;
import com.somytrip.utils.BigDecimalUtil;
import com.somytrip.utils.LocalDateTimeUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.itinerary
 * @className: ItineraryActivityDto
 * @author: shadow
 * @description: 攻略活动点dto
 * @date: 2024/11/21 11:31
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ItineraryActivityDto {

    private static final String OPEN_TIME_SPLIT_REGEX = ",";
    private static final String SUB_OPEN_TIME_SPLIT_REGEX = "-";
    /**
     * 活动类型(1: 景区, 2: 餐饮, 3: 酒店)
     */
    private Integer type;
    /**
     * 活动数据来源
     * (目前只支持酒店)
     */
    private String activityOrigin;
    /**
     * 活动ID
     */
    private String activityId;
    /**
     * 活动名称
     */
    private String activityName;
    /**
     * 主题ID列表
     */
    private List<Integer> themes = new ArrayList<>();
    /**
     * 评分
     */
    private String score;
    /**
     * 星级
     */
    private Integer star;
    /**
     * 短介绍
     */
    private String shortIntro;
    /**
     * 地址
     */
    private String address;
    /**
     * 商圈
     */
    private String businessZone;
    /**
     * 经度
     */
    private String lon;
    /**
     * 纬度
     */
    private String lat;
    /**
     * 城市code
     */
    private String cityCode;
    /**
     * 县级市ID
     */
    private Integer countryLevelCityId;
    /**
     * 夜游(1: 只能夜游)
     */
    private Integer evening;
    /**
     * 相似值
     * 此属性相同的景点为互相相似的景点
     */
    private Integer similarValue;
    /**
     * 图片列表(最多3张)
     */
    private List<String> images;
    /**
     * 价格(如: 免费、￥251元起)
     */
    private String startPrice;
    /**
     * 营业时间
     */
    private List<List<String>> businessHours;
    /**
     * 停留开始时间(如: 09:00)
     */
    @JsonFormat(pattern = "HH:mm")
    private LocalTime stayTimeStart;
    /**
     * 停留结束时间(如: 18:00)
     */
    @JsonFormat(pattern = "HH:mm")
    private LocalTime stayTimeEnd;
    /**
     * 停留开始日期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime stayDateTimeStart;
    /**
     * 停留结束日期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime stayDateTimeEnd;
    /**
     * 停留时间段(如: 18:00 - 19:00、20:00)
     */
    private String stayTimePeriod;
    /**
     * 预计停留时间(几小时)
     */
    private String stayTime;
    /**
     * 预计停留时间数值
     */
    private BigDecimal stayTimeValue = BigDecimal.valueOf(2);
    /**
     * 推荐游玩时间
     */
    private BigDecimal recommendedPlaytime;
    /**
     * 到下一地点距离
     */
    private String distanceToNext;
    /**
     * 到下一地点距离值(米)
     */
    private BigDecimal distanceToNextValue;
    /**
     * 到下一地点花费时间
     */
    private String timeToNext;
    /**
     * 到下一个地点花费小时数值
     */
    private BigDecimal hoursToNext;
    /**
     * 到下一地点的交通方式
     */
    private Integer transportationModeToNext;

    public ItineraryActivityDto(ItineraryScenicEntity scenic) {
        this.type = 1;
        this.activityId = String.valueOf(scenic.getId());
        this.activityName = scenic.getName();
        this.score = BigDecimalUtil.halfAdjust2Str(scenic.getScenicScore(), 1);
        if (scenic.getThemes() != null && !scenic.getThemes().isEmpty()) {
            this.themes = scenic.getThemes().stream().map(Integer::parseInt).toList();
        }
        this.address = scenic.getAddress();
        this.lon = scenic.getLon();
        this.lat = scenic.getLat();
        this.cityCode = scenic.getCityCode();
        this.countryLevelCityId = scenic.getCountryLevelCityId();
        if (StringUtils.isNotBlank(scenic.getPic())) {
            List<String> imageList = JSONArray.parseArray(scenic.getPic(), String.class);
            this.images = new ArrayList<>(imageList.subList(0, Math.min(3, imageList.size())));
        }
        this.stayTimeValue = scenic.getPlayTimeValue();
        this.recommendedPlaytime = scenic.getPlayTimeValue();
        if (Objects.equals("CNY", scenic.getPriceType())) {
            this.startPrice = BigDecimalUtil.halfAdjust2Str(scenic.getPriceNum(), 0);
        }
        this.evening = scenic.getEvening();
        this.similarValue = scenic.getSimilarValue();
    }

    public ItineraryActivityDto(HotelListVo hotelListVo) {
        this.type = 3;
        this.activityOrigin = hotelListVo.getHotelOrigin().toString();
        this.activityId = hotelListVo.getHotelId();
        this.activityName = hotelListVo.getHotelName();
        this.cityCode = hotelListVo.getCityCode();
        this.score = hotelListVo.getScore();
        this.star = hotelListVo.getStar() != null ? hotelListVo.getStar() : hotelListVo.getCategory();
        this.address = hotelListVo.getAddress();
        this.businessZone = hotelListVo.getBusinessZoneName();
        this.lon = hotelListVo.getLon();
        this.lat = hotelListVo.getLat();
        this.startPrice = hotelListVo.getStartPrice();
        if (StringUtils.isNotBlank(hotelListVo.getCover())) {
            this.images = new ArrayList<>(List.of(hotelListVo.getCover()));
        }
    }

    public ItineraryActivityDto(HotelDetailVo hotelDetailVo) {
        this.type = 3;
        this.activityOrigin = hotelDetailVo.getHotelOrigin().toString();
        this.activityId = hotelDetailVo.getHotelId();
        this.activityName = hotelDetailVo.getHotelName();
        this.cityCode = hotelDetailVo.getCityCode();
        this.score = hotelDetailVo.getScore();
        this.star = hotelDetailVo.getStar() != null ? hotelDetailVo.getStar() : hotelDetailVo.getCategory();
        this.address = hotelDetailVo.getAddress();
        this.businessZone = hotelDetailVo.getBusinessZoneName();
        this.lon = hotelDetailVo.getLon();
        this.lat = hotelDetailVo.getLat();
        this.startPrice = hotelDetailVo.getStartPrice();
        if (hotelDetailVo.getImages() != null && !hotelDetailVo.getImages().isEmpty()) {
            this.images = hotelDetailVo.getImages().subList(0, Math.min(3, hotelDetailVo.getImages().size()));
        }
    }

    public ItineraryActivityDto(ActivityDetailParam activityDetail) {
        this.type = activityDetail.getActivityType();
        this.activityOrigin = activityDetail.getActivityOrigin();
        this.activityId = activityDetail.getActivityId();
        this.activityName = activityDetail.getActivityName();
        this.cityCode = activityDetail.getCityCode();
        this.address = activityDetail.getAddress();
        this.lon = activityDetail.getLon();
        this.lat = activityDetail.getLat();
        this.score = activityDetail.getScore();
        this.images = activityDetail.getImages();
        this.startPrice = activityDetail.getStartPrice();
    }

    public ItineraryActivityDto(ItineraryDayEntity.ActivityEntity activityEntity) {
        this.stayTimeStart = activityEntity.getStayTimeStart();
        this.stayTimeEnd = activityEntity.getStayTimeEnd();
        this.stayDateTimeStart = activityEntity.getStayDateTimeStart();
        this.stayDateTimeEnd = activityEntity.getStayDateTimeEnd();
        this.stayTimePeriod = activityEntity.getStayTimePeriod();
        this.stayTime = activityEntity.getStayTime();
        this.stayTimeValue = activityEntity.getStayTimeValue();
        this.recommendedPlaytime = activityEntity.getRecommendedPlaytime();
        this.distanceToNext = activityEntity.getDistanceToNext();
        this.distanceToNextValue = activityEntity.getDistanceToNextValue();
        this.timeToNext = activityEntity.getTimeToNext();
        this.hoursToNext = activityEntity.getHoursToNext();
        this.transportationModeToNext = activityEntity.getTransportationModeToNext();

        this.type = activityEntity.getType();
        this.activityOrigin = activityEntity.getActivityOrigin();
        this.activityId = activityEntity.getActivityId();
        this.activityName = activityEntity.getActivityName();
        this.themes = activityEntity.getThemes();
        this.score = activityEntity.getScore();
        this.address = activityEntity.getAddress();
        this.lon = activityEntity.getLon();
        this.lat = activityEntity.getLat();
        this.cityCode = activityEntity.getCityCode();
        this.images = activityEntity.getImages();
        this.startPrice = activityEntity.getStartPrice();
    }

    public ItineraryActivityDto(ItineraryCreateParam.DayItem.ActivityItem activityItem) {
        this.type = activityItem.getActivityType();
        this.activityId = activityItem.getActivityId();
        this.activityName = activityItem.getActivityName();
        this.lon = activityItem.getLon();
        this.lat = activityItem.getLat();
        this.stayTimeValue = activityItem.getStayTime();
    }

    public ItineraryActivityDto(PoiItemResp poiItemResp, String cityCode) {
        this.type = 2;
        this.activityOrigin = "AMap";
        this.activityId = poiItemResp.getId();
        this.cityCode = cityCode;
        if (StringUtils.isNotBlank(poiItemResp.getLocation())) {
            String[] locationArr = poiItemResp.getLocation().split(",");
            this.lon = locationArr[0];
            this.lat = locationArr[1];
        }
        this.activityName = poiItemResp.getName();
        this.address = poiItemResp.getAddress();
        if (poiItemResp.getPhotos() != null && !poiItemResp.getPhotos().isEmpty()) {
            this.images = poiItemResp.getPhotos().stream().map(PoiItemResp.Photo::getUrl).limit(3).toList();
        }
        PoiItemResp.Business business = poiItemResp.getBusiness();
        if (business != null) {
            // 价格
            String cost = business.getCost();
            if (StringUtils.isNotBlank(cost)) {
                BigDecimal costValue = new BigDecimal(cost);
                this.startPrice = BigDecimalUtil.halfAdjust2Str(costValue, 0);
            }

            // 评分
            this.score = business.getRating();

            // 类型
            String typeCode = poiItemResp.getTypeCode();
            if (StrUtil.isNotBlank(typeCode)) {
                this.themes = new ArrayList<>(Arrays.asList(typeCode.split("\\|"))).stream()
                        .map(Integer::parseInt)
                        .collect(Collectors.toCollection(ArrayList::new));
            }

            // 营业时间
            String openTimeToday = business.getOpenTimeToday();
            if (StrUtil.isNotBlank(openTimeToday)) {
                List<List<String>> businessHours = new ArrayList<>();
                for (String subOpenTime : openTimeToday.split(OPEN_TIME_SPLIT_REGEX)) {
                    String[] subOpenTimes = subOpenTime.split(SUB_OPEN_TIME_SPLIT_REGEX);
                    if (subOpenTimes.length == 2) {
                        List<String> subBusinessHours = new ArrayList<>(List.of("*", "*", "*", subOpenTimes[0], subOpenTimes[1]));
                        businessHours.add(subBusinessHours);
                    }
                }
                this.businessHours = businessHours;
            }
        }
    }

    public void setStayDateTimeStart(LocalDateTime stayDateTimeStart) {
        this.stayDateTimeStart = stayDateTimeStart;
        if (stayDateTimeStart != null) {
            this.stayTimeStart = stayDateTimeStart.toLocalTime();
        }
    }

    public void setStayDateTimeEnd(LocalDateTime stayDateTimeEnd) {
        this.stayDateTimeEnd = stayDateTimeEnd;
        if (stayDateTimeEnd != null) {
            this.stayTimeEnd = stayDateTimeEnd.toLocalTime();

            if (this.stayDateTimeStart != null) {
                this.stayTimeValue = LocalDateTimeUtil.getHoursBetweenDateTimes(this.stayDateTimeStart, this.stayDateTimeEnd);
            }
        }
    }

    /**
     * 合并活动Entity到活动vo
     *
     * @param activityEntity 活动Entity
     * <AUTHOR>
     * @date 2024/4/14 10:16
     */
    public void mergeDayEntity(ItineraryDayEntity.ActivityEntity activityEntity) {
        if (activityEntity == null) {
            return;
        }
        this.stayDateTimeStart = activityEntity.getStayDateTimeStart();
        this.stayDateTimeEnd = activityEntity.getStayDateTimeEnd();
        this.stayTimeStart = activityEntity.getStayTimeStart();
        this.stayTimeEnd = activityEntity.getStayTimeEnd();
        this.stayTimePeriod = activityEntity.getStayTimePeriod();
        this.stayTime = activityEntity.getStayTime();
        this.stayTimeValue = activityEntity.getStayTimeValue();
        if (activityEntity.getRecommendedPlaytime() != null) {
            this.recommendedPlaytime = activityEntity.getRecommendedPlaytime();
        }
        this.distanceToNext = activityEntity.getDistanceToNext();
        this.distanceToNextValue = activityEntity.getDistanceToNextValue();
        this.timeToNext = activityEntity.getTimeToNext();
        this.hoursToNext = activityEntity.getHoursToNext();
        this.transportationModeToNext = activityEntity.getTransportationModeToNext();
    }

    public String buildQueryText() {
        // 名字
        return StringUtils.isNotBlank(this.activityName) ? this.activityName : "";
//        // 名字|地址
//        String pattern = "%s|%s";
//        return pattern.formatted(
//                StringUtils.isNotBlank(this.activityName) ? this.activityName : "",
//                StringUtils.isNotBlank(this.address) ? this.address : ""
//        );
    }
}
