package com.somytrip.entity.itinerary;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.tourism
 * @className: ItineraryHotEntity
 * @author: shadow
 * @description: 热门攻略实体
 * @date: 2024/5/30 14:33
 * @version: 1.0
 */
@Data
@TableName(value = "itinerary_hot", autoResultMap = true)
public class ItineraryHotEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1;

    /**
     * 自增主键ID
     */
    private Long id;

    /**
     * 攻略ID
     */
    private Long itineraryId;

    /**
     * 目的地城市ID列表
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Integer> destinationCityIds;

    /**
     * 是否可用
     */
    @TableField(value = "is_valid")
    private boolean valid;

    /**
     * 是否删除
     */
    @TableField(value = "is_del")
    private boolean del;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
