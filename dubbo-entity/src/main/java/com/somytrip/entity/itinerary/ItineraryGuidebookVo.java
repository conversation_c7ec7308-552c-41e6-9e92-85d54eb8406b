package com.somytrip.entity.itinerary;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.itinerary
 * @className: ItineraryGuidebookVo
 * @author: shadow
 * @description: 攻略指南vo
 * @date: 2024/7/24 15:14
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ItineraryGuidebookVo {

    /**
     * 城市code
     */
    private String cityCode;

    /**
     * 城市ID
     */
    private Integer cityId;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 指南详情
     */
    private GuidebookDetail guidebookDetail;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GuidebookDetail {

        /**
         * 紧急联络电话
         */
        private EmergencyContact emergencyContact;

        /**
         * 城市概况
         */
        private String brief;

        /**
         * 推荐游玩时间
         */
        private String tripTime;

        /**
         * 实用信息
         */
        private String actMessage;

        public GuidebookDetail(ItineraryGuidebookEntity guidebookEntity) {
            this.emergencyContact = new EmergencyContact(guidebookEntity);
            this.brief = guidebookEntity.getBrief();
            this.tripTime = guidebookEntity.getTripTime();
            this.actMessage = guidebookEntity.getActMessage();
        }

        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class EmergencyContact {

//            /**
//             * 国家名称
//             */
//            private String countryName;

            /**
             * 报警电话
             */
            private String alarmTel;

            /**
             * 旅游局电话
             */
            private String tourismAdTel;

            /**
             * 中国大使馆电话
             */
            private String chinaEmbassyTel;

            public EmergencyContact(ItineraryGuidebookEntity guidebookEntity) {
                if (guidebookEntity == null) return;
                this.alarmTel = guidebookEntity.getAlarmTel();
                this.chinaEmbassyTel = guidebookEntity.getChinaEmbassyTel();
                this.tourismAdTel = guidebookEntity.getTourismAdTel();
            }
        }
    }
}
