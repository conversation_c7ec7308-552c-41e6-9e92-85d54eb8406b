package com.somytrip.entity.itinerary;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONArray;
import com.somytrip.entity.dto.Scenic;
import com.somytrip.entity.enums.itinerary.ItineraryActivityType;
import com.somytrip.entity.hotel.HotelGeneralEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.itinerary
 * @className: BeautifulPdfDto
 * @author: shadow
 * @description: 攻略美观pdf数据dto
 * @date: 2025/1/7 10:11
 * @version: 1.0
 */
@Data
public class BeautifulPdfDto {

    /**
     * 攻略信息
     */
    private ItineraryInfo itineraryInfo;

    /**
     * 天列表
     */
    private List<DayInfo> dayInfos;

    /**
     * 出行清单类型列表
     */
    private List<ChecklistType> checkListTypes;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ItineraryInfo {

        /**
         * 标题
         */
        private String title;

        /**
         * 作者
         */
        private String author;

        /**
         * 出发日期
         * 例: 2024年4月1日出发
         */
        private String departureDate;

        /**
         * 天数和国家、城市数量描述
         * 例: 共5天，2个国家，2个城市
         */
        private String daysAndCountDesc;

        /**
         * 封面
         */
        private String cover;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DayInfo {

        /**
         * 第n天
         * 例: 01
         */
        private String dayNum;

        /**
         * 日期
         * 例: 2024年04月01日
         */
        private String date;

        /**
         * 周
         * 例: 星期一
         */
        private String week;

        /**
         * 城市列表
         */
        private List<BeautifulPdfCityDto> cities;

        /**
         * 景点列表
         */
        private List<Activity> activities;

        /**
         * 酒店
         */
        private Activity hotel;

        /**
         * 交通
         */
        private List<Transportation> tp;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Activity {

        /**
         * 类型
         */
        private Integer type;

        /**
         * 活动名称
         */
        private String name;

        /**
         * 活动英文名
         */
        private String nameEn;

        /**
         * 地址
         */
        private String address;

        /**
         * 介绍
         */
        private String introduction;

        /**
         * 标签
         * 例: 桥梁,主题观光
         */
        private String tags;

        /**
         * 营业时间
         * 例: 全天
         */
        private String businessHours;

        /**
         * 价格描述
         * 例: 登塔：票价100克朗，学生70克朗
         */
        private String priceDesc;

        /**
         * 封面url
         */
        private String cover;

        /**
         * 到下一活动距离(公里)
         * 例: 4.06公里
         */
        private String distanceToNext;

        public Activity(Scenic scenic) {
            this.type = ItineraryActivityType.SCENIC.getValue();
            this.name = scenic.getName();
            this.nameEn = scenic.getEngName();
            this.address = scenic.getAddress();
            this.introduction = scenic.getBrief();
            String tags = scenic.getTags();
            if (StrUtil.isNotBlank(tags)) {
                List<String> tagList = JSONArray.parseArray(tags).toJavaList(String.class);
                this.tags = String.join(",", tagList);
            }
            this.businessHours = scenic.getBusinessHours();
            this.priceDesc = scenic.getPrice();
            this.cover = scenic.getThumbnail();
        }

        public Activity(HotelGeneralEntity hotelGeneralEntity) {
            this.type = ItineraryActivityType.HOTEL.getValue();
            this.name = hotelGeneralEntity.getHotelName();
            this.address = hotelGeneralEntity.getAddress();
            this.introduction = hotelGeneralEntity.getIntroEditor();
            String cover = hotelGeneralEntity.getCover();
            if (StrUtil.isBlank(cover)) {
                List<String> images = hotelGeneralEntity.getImages();
                cover = CollUtil.getFirst(images);
            }
            this.cover = cover;
        }

        public Activity(ItineraryDayEntity.ActivityEntity activityEntity) {
            this.type = ItineraryActivityType.HOTEL.getValue();
            this.name = activityEntity.getActivityName();
            this.address = activityEntity.getAddress();
            List<String> images = activityEntity.getImages();
            this.cover = CollUtil.getFirst(images);
        }
    }

    @Data
    public static class Transportation {

        /**
         * 出发站
         */
        private String departure;

        /**
         * 到达站
         */
        private String destination;

        /**
         * 出发城市
         */
        private BeautifulPdfCityDto departureCity;

        /**
         * 到达城市
         */
        private BeautifulPdfCityDto arrivalCity;

        /**
         * 出发时间
         * 例: 00:55
         */
        private String departureTime;

        /**
         * 到达时间
         * 例: 06:35
         */
        private String arrivalTime;

        /**
         * 班次名称
         * 例: MU707
         */
        private String name;
    }

    @Data
    public static class ChecklistType {

        private String type;

        private List<ChecklistItem> items;
    }

    @Data
    public static class ChecklistItem {

        private String name;
    }
}
