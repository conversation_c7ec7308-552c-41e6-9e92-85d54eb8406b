package com.somytrip.entity.itinerary;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.tourism
 * @className: ItineraryCopyHotReq
 * @author: shadow
 * @description: 复制攻略请求参数
 * @date: 2024/6/6 11:36
 * @version: 1.0
 */
@Data
public class ItineraryCopyReq {

    /**
     * 攻略序列号
     */
    @NotNull(message = "{api.itinerary.non-itinerary-sn}")
    private String itinerarySn;

    /**
     * 攻略标题
     */
    @NotNull(message = "{api.itinerary.non-title}")
    private String title;

    /**
     * 出发时间
     */
    @NotNull(message = "{api.itinerary.non-go-time}")
    private LocalDate goTime;

    /**
     * 用户ID
     */
    private String uid;
}
