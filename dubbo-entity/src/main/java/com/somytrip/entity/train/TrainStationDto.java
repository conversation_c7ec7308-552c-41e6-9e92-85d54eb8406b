package com.somytrip.entity.train;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.train
 * @className: TrainStationDto
 * @author: shadow
 * @description: 火车站dto
 * @date: 2025/5/12 15:40
 * @version: 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TrainStationDto {

    /**
     * 火车站code
     */
    private String code;

    /**
     * 火车站名称(多语言)
     */
    private String name;

    /**
     * 中文名称
     */
    private String nameCn;

    /**
     * 所属城市ID
     */
    private Integer cityId;
}
