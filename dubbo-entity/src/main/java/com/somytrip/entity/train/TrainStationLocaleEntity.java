package com.somytrip.entity.train;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity
 * @className: TrainStationLocaleEntity
 * @author: shadow
 * @description: 百度火车站
 * @date: 2024/12/21 15:35
 * @version: 1.0
 */
@Data
@TableName("train_stations_locale")
public class TrainStationLocaleEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 7230169872737933574L;

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 火车站ID
     */
    private Integer trainStationId;

    /**
     * 语言
     */
    private String lang;

    /**
     * 火车站名称
     */
    private String name;

    private String pinyin;
}
