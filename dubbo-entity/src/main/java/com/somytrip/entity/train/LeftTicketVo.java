package com.somytrip.entity.train;

import lombok.Data;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.train
 * @className: LeftTicketVo
 * @author: shadow
 * @description: 余票Dto
 * @date: 2025/5/8 10:54
 * @version: 1.0
 */
@Data
public class LeftTicketVo {

    /**
     * 车次好
     */
    private String trainCode;

    /**
     * 价格
     */
    private String price;

    /**
     * 出发站
     */
    private String beginStation;

    /**
     * 到达站
     */
    private String endStation;

    /**
     * 出发日期时间
     * 2025-05-08 11:00
     */
    private String fromDateTime;

    /**
     * 到达日期时间
     * 2025-05-08 15:00
     */
    private String toDateTime;

    /**
     * 出发时间
     * 11:00
     */
    private String fromTime;

    /**
     * 到达时间
     * 15:00
     */
    private String toTime;

    /**
     * 历时
     * 4h0m
     */
    private String duration;
}
