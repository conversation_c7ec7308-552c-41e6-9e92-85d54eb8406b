package com.somytrip.entity.train;

import lombok.Getter;

import java.util.Arrays;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity.train
 * @enumName: SeatType
 * @author: shadow
 * @description: 火车座位类型枚举
 * @date: 2025/6/3 15:19
 * @version: 1.0
 */
@Getter
public enum SeatType {

    /**
     * 商务座
     */
    SWZ("商务座", "9", "SWZ", 32),
    /**
     * 特等座
     */
    TZ("特等座", "P", "TZ", 25),
    /**
     * 一等座
     */
    ZY("一等座", "M", "ZY", 31),
    /**
     * 二等座
     */
    ZE("二等座", "O", "ZE", 30),
    /**
     * 优选一等座
     */
    GG("优选一等座", "D", "GG", 20),
    /**
     * 高级软卧
     */
    GR("高级软卧", "6", "GR", 21),
    /**
     * 高级动卧
     */
    GRA("高级动卧", "A", "GR", 21),
    /**
     * 软卧
     */
    RW("软卧", "4", "RW", 23),
    /**
     * 一等卧
     */
    RWI("一等卧", "I", "RW", 23),
    /**
     * 动卧
     */
    SRRB("动卧", "F", "SRRB", 33),
    /**
     * 硬卧
     */
    YW("硬卧", "3", "YW", 28),
    /**
     * 二等卧
     */
    YWJ("二等卧", "J", "YW", 28),
    /**
     * 软座
     */
    RZ("软座", "2", "RZ", 24),
    /**
     * 硬座
     */
    YZ("硬座", "1", "YZ", 29),
    /**
     * 无座
     */
    WZY("无座", "1", "WZ", 26),
    /**
     * 无座
     */
    WZE("无座", "O", "WZ", 26),
    /**
     * 其他
     */
    QT("其他", "H", "QT", 22),
    ;

    private final String name;
    private final String code;
    private final String subName;
    private final int index;

    SeatType(String name, String code, String subName, int index) {
        this.name = name;
        this.code = code;
        this.subName = subName;
        this.index = index;
    }

    public static SeatType getFromCode(String code) {
        return Arrays.stream(SeatType.values())
                .filter(item -> item.code.equals(code))
                .findFirst()
                .orElse(null);
    }
}
