package com.somytrip.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@TableName("sms_content")
public class SmsContent {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 手机号 有无区号 有区号的，且明文
     */
    private String phone;
    /**
     * 短信类型
     */
    private String typeSms;
    /**
     * 短信内容
     */
    private String content;
    /**
     * 短信内容数组
     */
    @TableField(exist = false)
    private String[] contentArr;
    /**
     * api返回内容
     */
    private String retContent;
    /**
     * api返回key
     */
    private String retKey;

    /**
     * 创建时间
     */
    private Date createTime;

    public SmsContent(String phone, String typeSms, String content, String[] contentArr, String retContent, String retKey) {
        this.phone = phone;
        this.typeSms = typeSms;
        this.content = content;
        this.contentArr = contentArr;
        this.retContent = retContent;
        this.retKey = retKey;
        this.createTime = new Date();
    }


    public SmsContent(String phone, String typeSms, String content, String retContent, String retKey,
                      Date createTime) {
        super();
        this.phone = phone;
        this.typeSms = typeSms;
        this.content = content;
        this.retContent = retContent;
        this.retKey = retKey;
        this.createTime = createTime;
    }
}
