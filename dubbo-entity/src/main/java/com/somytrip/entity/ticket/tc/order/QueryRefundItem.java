package com.somytrip.entity.ticket.tc.order;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-11-12 10:00
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryRefundItem {
    /**
     * 订单退款列表
     */
    private String orderSerialId;
    /**
     * 退款创建时间
     */
    private String createTime;
    /**
     * 0未处理,1退款处理中,2退款成功,3退款失败
     */
    private Integer refundStatus;
    /**
     * 0：部分退款 1：全部退款
     */
    private Integer refundType;
    /**
     * 退款金额
     */
    private String refundMoney;
    /**
     * 退款手续费
     */
    private String refundFee;
    /**
     * 退款票数
     */
    private Integer ticketsNum;
    /**
     * 退款驳回原因
     */
    private String reason;
}
