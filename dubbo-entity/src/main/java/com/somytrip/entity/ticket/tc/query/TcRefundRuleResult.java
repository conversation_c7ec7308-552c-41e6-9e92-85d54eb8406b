package com.somytrip.entity.ticket.tc.query;

import com.alibaba.fastjson2.annotation.JSONField;
import com.somytrip.entity.ticket.tc.TcResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-11-09 15:38
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class TcRefundRuleResult extends TcResponse {
    private String backRule;
    @JSONField(name = "RefundRuleList")
    private List<TcRefundRuleItem> refundRuleList;
}
