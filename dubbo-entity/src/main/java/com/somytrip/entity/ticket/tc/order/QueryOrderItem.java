package com.somytrip.entity.ticket.tc.order;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-11-12 9:49
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryOrderItem {
    /**
     * 同程订单流水号
     */
    private String orderSerialId;
    /**
     * 同程订单流水号
     */
    private String tcSerialId;
    /**
     * somytri订单号
     */
    private String thirdSerialId;
    /**
     * 旅游日期
     */
    private String travelDate;
    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 订单状态(N:客户提交；F：同程已确认；J：供应商已出票；V：已核单；C：取消；S：NoShow)
     */
    private String orderState;
    /**
     * N未支付 P已支付 P1部分支付 R已退款R1申请部分退款 R2申请全额退款
     */
    private String payState;
    /**
     * 票数
     */
    private Integer ticketsNumber;
    /**
     * 核销时间
     */
    private String checkOrderTime;
}
