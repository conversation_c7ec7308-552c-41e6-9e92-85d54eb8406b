package com.somytrip.entity.ticket.tc.increment;

import cn.hutool.core.util.StrUtil;
import com.somytrip.entity.ticket.tc.TcHead;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-11-09 17:26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ScenicUpdateBody implements TcHead {
    /**
     * 起始增量Id
     */
    private Integer maxIncrementId;
    /**
     * 查询数量
     * 如果不传该属性，则默认查询20条
     */
    private Integer queryNumber;
    /**
     * 增量类型
     * 0:默认查询全部增量类型
     * 1:表示查询新上线景区
     * 2：表示查询已下线景区
     * 3:表示查询信息修改的景区
     */
    private Integer incrementType;

    @Override
    public String md5Args() {
        return StrUtil.join("", maxIncrementId, queryNumber, incrementType);
    }
}
