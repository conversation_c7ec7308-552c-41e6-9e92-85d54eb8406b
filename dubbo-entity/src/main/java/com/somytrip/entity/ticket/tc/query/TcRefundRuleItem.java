package com.somytrip.entity.ticket.tc.query;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-11-12 17:55
 */
@Data
public class TcRefundRuleItem {
    @JSONField(name = "IsRefund")
    private Integer isRefund;
    @JSONField(name = "IsPartialRefund")
    private Integer isPartialRefund;
    @JSONField(name = "ChargeType")
    private Integer chargeType;
    @JSONField(name = "Charge")
    private String charge;
    @JSONField(name = "TCChargeType")
    private Integer tcChargeType;
    @JSONField(name = "TCCharge")
    private String tcCharge;
    @JSONField(name = "Day")
    private Integer day;
    @JSONField(name = "Hour")
    private Integer hour;
    @JSONField(name = "Minute")
    private Integer minute;
}
