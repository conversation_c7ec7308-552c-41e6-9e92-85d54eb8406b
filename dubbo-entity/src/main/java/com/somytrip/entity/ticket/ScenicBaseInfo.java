package com.somytrip.entity.ticket;

import lombok.Data;

import java.util.List;

/**
 * @Description: 供应商景区信息
 * @author: pigeon
 * @created: 2024-11-08 11:26
 */
@Data
public class ScenicBaseInfo {
    /**
     * 同程：景区ID
     * 华侨城：供应商编号
     */
    private String id;
    /**
     * 景区名称
     * 华侨城：供应商名称
     */
    private String name;
    /**
     * 景区别名
     */
    private String aliasName;
    /**
     * 景区级别
     */
    private String level;
    /**
     * 首图
     */
    private String cover;
    /**
     * 图片列表
     */
    private List<ImageInfoItem> images;
    /**
     * 原样json
     */
    private String extJson;
}
