package com.somytrip.entity.ticket.vo;

import com.somytrip.entity.enums.ticket.TicketSourceEnum;
import com.somytrip.entity.ticket.dto.ScenicTicketGoodDto;
import com.somytrip.entity.ticket.otm.order.CreateOrder;
import com.somytrip.entity.ticket.otm.scenic.ProductTicketDetail;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-11-15 15:47
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BookingField1 {
    /**
     * 原始入参
     */
    private BookingOrderReq req;
    /**
     * 供应商
     */
    private TicketSourceEnum source;
    /**
     * 商品列表
     */
    private List<ScenicTicketGoodDto> goods;

    private List<ProductTicketDetail.Rsp> goodDetails;

    private List<CreateOrder.Req> reqs;
}
