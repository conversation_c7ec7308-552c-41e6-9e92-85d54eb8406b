package com.somytrip.entity.ticket.tc.order;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.annotation.JSONField;
import com.somytrip.entity.ticket.tc.TcHead;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-11-09 17:44
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderCheckBody implements TcHead {
    /**
     * 产品Id
     */
    @JSONField(name = "PriceId")
    private Integer priceId;
    /**
     * 旅游日期
     */
    @JSONField(name = "TravelDate")
    private String travelDate;
    /**
     * 取票数
     */
    @JSONField(name = "Tickets")
    private Integer tickets;
    /**
     * 取票人证件类型
     */
    @JSONField(name = "TravelerIdCardNoType")
    private String travelerIdCardNoType;
    /**
     * 取票人证件号
     */
    @JSONField(name = "TravelerIdCardNo")
    private String travelerIdCardNo;
    /**
     * 取票人手机号码
     */
    @JSONField(name = "TravelerMobile")
    private String travelerMobile;
    /**
     * 预定人手机号码识别码
     */
    @JSONField(name = "MobileIdentifier")
    private String mobileIdentifier;
    /**
     * 实名制列表
     */
    @JSONField(name = "RealBookInfo")
    private List<CheckUserItem> realBookInfo;
    /**
     * 预定人邮箱
     */
    @JSONField(name = "BookEmail")
    private String bookEmail;
    /**
     * 同程价
     */
    @JSONField(name = "TCAmount")
    private String tcAmount;
    /**
     * 场次信息
     */
    @JSONField(name = "ScreeningInfo")
    private CheckScreenInfo screeningInfo;
    /**
     * 分销结算总价
     */
    @JSONField(name = "AgentAmount")
    private String agentAmount;
    /**
     * 收货信息
     */
    private CheckPostInfo postReceiveInfo;

    @Override
    public String md5Args() {
        return StrUtil.join("", priceId, tickets, travelerMobile);
    }
}
