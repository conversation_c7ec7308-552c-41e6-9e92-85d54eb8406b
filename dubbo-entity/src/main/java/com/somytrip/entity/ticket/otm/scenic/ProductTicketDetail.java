package com.somytrip.entity.ticket.otm.scenic;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description: 景区商品门票
 * @author: pigeon
 * @created: 2024-07-18 11:47
 */
public class ProductTicketDetail {
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Req {
        /**
         * 景区编号
         */
        @JSONField(name = "scenic_no")
        private String scenicNo;
        /**
         * 商品编号
         */
        @JSONField(name = "goods_no")
        private String goodsNo;
    }

    @Data
    public static class Rsp {

        private Long id;
        /**
         * 销售景区编码
         */
        @JSONField(name = "sale_scenic_no")
        private String saleScenicNo;
        /**
         * 销售景区名称
         */
        @JSONField(name = "sale_scenic_name")
        private String saleScenicName;
        /**
         * 供应景区编码
         */
        @JSONField(name = "supply_scenic_no")
        private String supplyScenicNo;
        /**
         * 供应景区名称
         */
        @JSONField(name = "supply_scenic_name")
        private String supplyScenicName;
        /**
         * 商品编号
         */
        @JSONField(name = "goods_no")
        private String goodsNo;
        /**
         * 商品名称
         */
        @JSONField(name = "goods_name")
        private String goodsName;
        /**
         * 商品简称
         */
        @JSONField(name = "goods_shortname")
        private String goodsShortname;
        /**
         * 商品业态编码
         */
        @JSONField(name = "goods_type_code")
        private String goodsTypeCode;
        /**
         * 商品业态名称
         */
        @JSONField(name = "goods_type_name")
        private String goodsTypeName;
        /**
         * 商品封面图
         */
        private String cover;
        /**
         * 商品短评
         */
        private String abbreviation;
        /**
         * 温馨提示
         */
        private String tips;
        /**
         * 预定须知
         */
        @JSONField(name = "reservation_notes")
        private String reservationNotes;
        /**
         * 商品详情描述
         */
        private String description;
        @JSONField(name = "split_order")
        private String splitOrder;
        /**
         * 库存
         */
        private Sku sku;
        /**
         * 售后服务
         */
        @JSONField(name = "after_sale")
        private AfterSale afterSale;
        /**
         * 销售策略
         */
        @JSONField(name = "sales_policy")
        private SalesPolicy salesPolicy;
        /**
         * 价格策略
         */
        @JSONField(name = "price_policy")
        private PricePolicy pricePolicy;
        /**
         * 使用策略
         */
        @JSONField(name = "use_policy")
        private UsePolicy usePolicy;
        /**
         * 套餐发码方式(套餐: "PACKAGE", 子商品: "SUB_VOUCHER")
         */
        @JSONField(name = "made_voucher_mode")
        private String madeVoucherMode;
        @JSONField(name = "sub_goods")
        private List<ProductList.SubGood> subGoods;
        @JSONField(name = "activate_policy")
        private ActivatePolicy activatePolicy;
        private Renewal renewal;
        @JSONField(name = "gift_item")
        private List<GiftItem> giftItem;
    }

    @Data
    public static class GiftItem {
        @JSONField(name = "hire_name")
        private String hireName;
        private Integer quantity;
    }

    @Data
    public static class Renewal {
        /**
         * 续卡费用
         */
        @JSONField(name = "renew_card_price")
        private String renewCardPrice;
        /**
         * 可续卡时间, 有效期结束前 N 天开始
         */
        @JSONField(name = "allow_before_day")
        private Integer allowBeforeDay;
        /**
         * 可续卡时间, 有效期结束后 N 天截止
         */
        @JSONField(name = "allow_after_day")
        private Integer allowAfterDay;
        /**
         * 续卡后有效期计算方式。DURATION 为 valid_period 续卡后有效期，
         * DOUBLE 原有效期时间，延长 1 倍
         */
        @JSONField(name = "valid_period_type")
        private String validPeriodType;
        /**
         * 年卡有效期续期时长(1d:1 天,2H:2 小时,3M:3 个月,1y:1 年)
         */
        @JSONField(name = "valid_period")
        private String validPeriod;
        /**
         * 续卡后计算有效期开始时间。CURRENT_DATE 为 当天开始计算，
         * NEXT_DATE 原有效期结束时间+1 天
         */
        @JSONField(name = "valid_period_start_day")
        private String validPeriodStartDay;
    }

    @Data
    public static class ActivatePolicy {
        /**
         * 激活时限类型[购买后激活:"DYNAMIC_TIME", 固定时间激
         * 活:"FIXED_TIME"]
         */
        @JSONField(name = "activate_time_limit_type")
        private String activateTimeLimitType;
        /**
         * 激 活 模 式 [ 用 户 自 助 激 活 :"AUTO", 人 工 确 认 激
         * 活:"MANUAL_AUDIT"]
         */
        @JSONField(name = "activation_mode")
        private String activationMode;
        /**
         * 固定时间激活:"FIXED_TIME"的激活时限开始时间
         */
        @JSONField(name = "activate_time_limit_start")
        private String activateTimeLimitStart;
        /**
         * 固定时间激活:"FIXED_TIME"的激活时限结束时间
         */
        @JSONField(name = "activate_time_limit_end")
        private String activateTimeLimitEnd;
        /**
         * 购买后激活:"DYNAMIC_TIME"的激活时限(1d:1 天,2H:2 小时,3M:3
         * 个月,1y:1 年)
         */
        @JSONField(name = "activate_period")
        private String activatePeriod;
        /**
         * datetime 固定时间激活:"FIXED_TIME"的激活时限结束时间
         */
        @JSONField(name = "delay_activate_time")
        private String delayActivateTime;
        /**
         * 可激活人数
         */
        @JSONField(name = "person_number")
        private Integer personNumber;
        /**
         * 附件信息
         */
        private List<Addition> additions;
    }

    @Data
    public static class Addition {
        /**
         * 附件标签
         */
        @JSONField(name = "label_name")
        private String labelName;
        /**
         * 附件标签预览值
         */
        @JSONField(name = "label_name")
        private String previewData;
        /**
         * 附件值键
         */
        private String field;
        /**
         * 附件类型[图片:"IMAGE", 文本:"TEXT"]
         */
        @JSONField(name = "label_name")
        private String tagType;
        /**
         * 示例说明图片链接
         */
        @JSONField(name = "label_name")
        private String exampleLink;
    }

    @Data
    public static class Sku {
        /**
         * 合同生效时间
         */
        @JSONField(name = "contract_start_date")
        private String contractStartDate;
        /**
         * 合同失效时间
         */
        @JSONField(name = "contract_end_date")
        private String contractEndDate;
        /**
         * 合同可售数量(0 即不限制)
         */
        @JSONField(name = "contract_quantity")
        private Long contractQuantity;
        /**
         * 合同已售数量
         */
        @JSONField(name = "contract_sold_num")
        private Long contractSoldNum;
    }

    @Data
    public static class AfterSale {
        /**
         * 是否可退[允许:"ALLOW", 不允许"NOT_ALLOW"]
         */
        private String refund;
        /**
         * 是否可改期[允许:"ALLOW", 不允许"NOT_ALLOW"]
         */
        private String change;
    }

    @Data
    public static class SalesPolicy {
        /**
         * 上架时间
         */
        @JSONField(name = "list_time")
        private String listTime;
        /**
         * 下架时间
         */
        @JSONField(name = "delist_time")
        private String delistTime;
        /**
         * 最大预售天数, 指定出行日 POINT_DAY_VALIDITY 商品的特
         * 有限制
         */
        @JSONField(name = "max_advance_days")
        private Long maxAdvanceDays;
        /**
         * 单笔订单最小购买数
         */
        @JSONField(name = "min_quantity_each_order")
        private Long minQuantityEachOrder;
        /**
         * 单日最大可售数(0 即不限制), 指定出行日 POINT_DAY_VALIDITY 商品按出行日计算, 固定
         * 有效期 FIXED_VALIDITY 与变动有效期 DYNAMIC_VALIDITY
         * 商品按购买当天计算
         */
        @JSONField(name = "max_quantity_each_day")
        private Long maxQuantityEachDay;
        /**
         * 建议售价范围
         */
        @JSONField(name = "guiding_price_range")
        private GuidingPriceRange guidingPriceRange;
        /**
         * 门市价
         */
        @JSONField(name = "retail_price")
        private Long retailPrice;
        /**
         * 购买条件
         */
        @JSONField(name = "purchase_conditions")
        private PurchaseConditions purchaseConditions;
        /**
         * 支持的证件类型[身份证:"1", 军官证:"2", 学生证:"3", 残疾
         * 人证:"4", 护照:"5", 回乡证:"6", 台胞证:"7"]
         */
        @JSONField(name = "support_certificate_type")
        private List<Integer> supportCertificateType;
    }

    @Data
    public static class GuidingPriceRange {
        /**
         * 最低价格
         */
        private String min;
        /**
         * 最高价格
         */
        private String max;
    }

    @Data
    public static class PurchaseConditions {
        /**
         * 有效期类型，固定有效期 FIXED_VALIDITY 、变动有效期
         * DYNAMIC_VALIDITY 、指定出行日 POINT_DAY_VALIDITY
         */
        private String type;
        /**
         * 指定出行日 POINT_DAY_VALIDITY 商品出行日当天最晚可购买时间
         * （单位分钟）， 默认 0 即不限制
         */
        @JSONField(name = "buy_before_in")
        private Integer buyBeforeIn;
        /**
         * 必要媒介(手机号:"MOBILE", 证件: "CERTIFICATE", 人脸:"FACE")
         */
        private List<String> requirements;
        /**
         * 限购条件
         */
        @JSONField(name = "limit_conditions")
        private List<LimitCondition> limitConditions;
    }

    @Data
    public static class LimitCondition {
        /**
         * 限购媒介(手机号:"MOBILE", 证件: "CERTIFICATE")
         */
        private String media;
        /**
         * 限购方式(默认商品限购:"TOTAL", 周期性限购: "DATE_RANGE")，指
         * 定出行日 POINT_DAY_VALIDITY 商品针对出行日进行控制；固定有效
         * 期FIXED_VALIDITY与变动有效期DYNAMIC_VALIDITY商品针对销售日
         * 进行控制
         */
        @JSONField(name = "limit_mode")
        private String limitMode;
        /**
         * 限购数量
         */
        private Integer quantity;
        /**
         * 周期性限购属性，限购起始日期（格式 yyyy-MM-dd）
         */
        @JSONField(name = "start_date")
        private String startDate;
        /**
         * 周期性限购属性，限购结束日期（格式 yyyy-MM-dd）
         */
        @JSONField(name = "end_date")
        private String endDate;
    }

    @Data
    public static class PricePolicy {
        /**
         * 价格策略类型，固定有效期与变动有效期为固定价格 FIXED，指定出行日
         * 为变动价格 DYNAMIC
         */
        private String type;
        /**
         * 结算价定义
         */
        @JSONField(name = "settle_price")
        private SettlePrice settlePrice;
    }

    @Data
    public static class SettlePrice {
        /**
         * 具体价格
         */
        @JSONField(name = "price_data")
        private List<PriceDaum> priceData;
        /**
         * 日程定义,固定价格 FIXED 无此日程
         */
        @JSONField(name = "schedule_time")
        private List<ScheduleTime> scheduleTime;
    }

    @Data
    public static class ScheduleTime {
        /**
         * 日程年度
         */
        private int year;
        /**
         * 全年日程编码，368 个字符，a,b,c,d 代表可以指定购买，0,1 代表不能
         * 指定购买
         */
        private String time;
    }

    @Data
    public static class PriceDaum {
        /**
         * 价格
         */
        private String price;
        /**
         * 日程编码
         */
        @JSONField(name = "schedule_period_code")
        private String schedulePeriodCode;
    }

    @Data
    public static class UsePolicy {
        /**
         * 有效期类型。固定有效期 FIXED_VALIDITY 、变动有效期
         * DYNAMIC_VALIDITY 、指定出行日 POINT_DAY_VALIDITY
         */
        private String type;
        /**
         * 使用规则
         */
        @JSONField(name = "use_rule")
        private UseRule useRule;
    }

    @Data
    public static class UseRule {
        /**
         * 指定商品周几有效
         */
        @JSONField(name = "effect_day_of_week")
        private String effectDayOfWeek;
        /**
         * 开始入园时间(单位分钟),如上午 8 点整即 480
         */
        @JSONField(name = "start_in_time")
        private Integer startInTime;
        /**
         * 最迟入园时间(单位分钟),如下午 6 点整即 1080
         */
        @JSONField(name = "end_in_time")
        private Integer endInTime;
        /**
         * 年度限制使用日
         */
        @JSONField(name = "invalid_day_of_year")
        private List<ScheduleTime> invalidDayOfYear;
        /**
         * 固定有效期 FIXED_VALIDITY 和变动有效期
         * DYNAMIC_VALIDITY 商品的特有时间限制标示，延迟生效时
         * 间单位(DAY:天, MIN:分钟)
         */
        @JSONField(name = "effect_time_type")
        private String effectTimeType;
        /**
         * 固定有效期 FIXED_VALIDITY 和变动有效期
         * DYNAMIC_VALIDITY 商品的特有时间限制标示，购买后凭证
         * 延迟生效时间(单位由 effect_time_type 确定)
         */
        @JSONField(name = "take_effect_time")
        private Integer takeEffectTime;
        /**
         * 变动有效期 DYNAMIC_VALIDITY 商品的特有时间限制标示，
         * 凭证生效时长(单位由 effect_time_type 确定)
         */
        @JSONField(name = "valid_day_range")
        private Integer validDayRange;
        /**
         * 固定有效期 FIXED_VALIDITY 商品的特有时间限制标示, 商品
         * 有效期开始时间
         */
        @JSONField(name = "validity_start_date")
        private String validityStartDate;
        /**
         * 固定有效期 FIXED_VALIDITY 商品的特有时间限制标示, 商品
         * 有效期结束时间
         */
        @JSONField(name = "validity_end_date")
        private String validityEndDate;
        /**
         * 指定出行日 POINT_DAY_VALIDITY 商品的特有时间限制标示, 商品当天后可持续有效天数(默认 1 当天)
         */
        @JSONField(name = "voucher_validity_day")
        private Integer voucherValidityDay;
        @JSONField(name = "package_use_in_time")
        private Integer packageUseInTime;
        @JSONField(name = "package_validity_model")
        private String packageValidityModel;
        @JSONField(name = "valid_period")
        private String validPeriod;
        private String feature;
    }
}
