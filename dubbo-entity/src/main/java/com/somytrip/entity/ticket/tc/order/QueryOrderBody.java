package com.somytrip.entity.ticket.tc.order;

import cn.hutool.core.util.StrUtil;
import com.somytrip.entity.ticket.tc.TcHead;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 分销商订单查询接口 body
 * @author: pigeon
 * @created: 2024-11-09 17:44
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryOrderBody implements TcHead {
    /**
     * 订单状态(
     * N:客户提交；
     * F：同程已确认；
     * J：供应商已出票；
     * V：已核单；
     * C：取消
     * ；S：NoShow)
     * ,如果不传该参数，默认查看所有状态的订单
     */
    private String orderState;
    /**
     * 同程订单号
     */
    private String orderSerialId;
    /**
     * 旅游日期开始日期
     */
    private String travelDateFrom;
    /**
     * 旅游日期结束日期
     */
    private String travelDateTo;
    /**
     * 页码索引(默认为1)
     */
    private Integer pageIndex;
    /**
     * 页面数据数量
     */
    private Integer pageSize;

    @Override
    public String md5Args() {
        return StrUtil.concat(true, orderSerialId, StrUtil.join("", pageIndex, pageSize));
    }
}
