package com.somytrip.entity.ticket.tc.notify;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-11-12 10:06
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RefundNotify {
    /**
     * 退款时间
     */
    private String refundTime;
    /**
     * 请求编码
     * 1：成功， 0：失败
     */
    private Integer returnCode;
    /**
     * 失败时，会给出失败原因说明
     */
    private String returnMsg;
    /**
     * 订单流水号
     */
    private String serialId;
    /**
     * 0：部分退款 1：全部退款
     */
    @JsonProperty("RefundType")
    @JSONField(name = "RefundType")
    private Integer refundType;
    /**
     * 退票数
     * 全部退款可不传退票数量
     */
    private Integer refundTicketsNum;
    /**
     * 退款金额
     */
    private String refundAmount;
    /**
     * 手续费
     */
    private String poundageAmount;
    /**
     * 退款流水号
     */
    private String orderBillId;
}
