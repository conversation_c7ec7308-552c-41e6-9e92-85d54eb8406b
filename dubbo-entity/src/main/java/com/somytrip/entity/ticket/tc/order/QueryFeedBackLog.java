package com.somytrip.entity.ticket.tc.order;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-11-12 10:15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryFeedBackLog {
    /**
     * 日志创建时间
     */
    @JSONField(name = "CreateTime")
    private String createTime;
    /**
     * 操作内容
     */
    @JSONField(name = "OperateContent")
    private String operateContent;
    /**
     * 日志备注
     */
    @J<PERSON>NField(name = "Remark")
    private String remark;
}
