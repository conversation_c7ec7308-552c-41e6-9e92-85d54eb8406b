package com.somytrip.entity.ticket.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description 小奇请求-dto
 * <AUTHOR>
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
public class TripAITicketsReq {

    private Integer pageIndex = 0;
    private Integer pageSize = 10;
    @NotNull(message = "cityCodes not null")
    private List<String> cityCodes;
    private String scenicName;
}
