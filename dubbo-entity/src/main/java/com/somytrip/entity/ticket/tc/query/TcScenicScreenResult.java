package com.somytrip.entity.ticket.tc.query;

import com.alibaba.fastjson2.annotation.JSONField;
import com.somytrip.entity.ticket.tc.TcResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description: 同程场次票
 * @author: pigeon
 * @created: 2025-06-25 14:23
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class TcScenicScreenResult extends TcResponse {
    @JSONField(name = "ShowList")
    private List<TcScenicScreenItem> showList;
}
