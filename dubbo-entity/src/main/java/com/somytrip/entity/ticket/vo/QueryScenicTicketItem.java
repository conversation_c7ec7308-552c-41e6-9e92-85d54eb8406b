package com.somytrip.entity.ticket.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.somytrip.entity.vo.ScenicDetailVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-11-08 14:35
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryScenicTicketItem {
    /**
     * 首图
     */
    private String cover;
    /**
     * 景区名称|第三方的
     */
    private String scenicName;
    /**
     * 最低价
     */
    private String price;
    /**
     * 结算货币
     */
    private String cny;
    /**
     * ticket表景区ID
     */
    private Integer scenicTicketId;
    /**
     * somytrip景区详情
     */
    private ScenicDetailVo scenicDetail;
    /**
     * somytrip景区ID
     */
    private String scenicId;
    /**
     * 景区概要
     */
    private String scenicDec;
    /**
     * 景区类型
     */
    private String scenicType;

    /**
     * 来源
     */
    @JsonIgnore
    private Integer source;
    @JsonIgnore
    private String otmPrice;
}
