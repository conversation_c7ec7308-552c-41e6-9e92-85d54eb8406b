package com.somytrip.entity.ticket.otm.order;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

/**
 * @Description: 订单预校验
 * @author: pigeon
 * @created: 2024-07-19 15:05
 */
public class CheckBuyLimit {
    @Data
    public static class Req {
        /**
         * 景区编号
         */
        @JSONField(name = "sale_scenic_no")
        private String saleScenicNo;
        /**
         * 商品编号
         */
        @JSONField(name = "goods_no")
        private String goodsNo;
        /**
         * 指定出行日 POINT_DAY_VALIDITY 类型的商品必要条
         * 件，预计出行日期(yyyy-MM-dd)
         */
        @JSONField(name = "appoint_trip_date")
        private String appointTripDate;
        /**
         * 预购买份数
         */
        private Integer quantity;
        /**
         * 出行人手机号码（接受凭证使用）
         */
        @JSONField(name = "travel_user_mobile")
        private String travelUserMobile;
        /**
         * 证件类型,参照商品属性support_certificate_type支持
         * 的证件类型
         */
        @JSONField(name = "certificate_type")
        private Integer certificateType;
        /**
         * 证件类型对应的证件号
         */
        @JSONField(name = "certificate_value")
        private String certificateValue;
    }
}
