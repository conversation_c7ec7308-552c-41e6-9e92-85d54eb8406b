package com.somytrip.entity.ticket.otm.scenic;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * @Description: 商品价格日历
 * @author: pigeon
 * @created: 2024-07-19 14:33
 */
public class ProductDailyPrice {
    @Data
    public static class Req {
        /**
         * 景区编号
         */
        @JSONField(name = "scenic_no")
        private String scenicNo;
        /**
         * 商品编号
         */
        @JSONField(name = "goods_no")
        private String goodsNo;
        /**
         * 查询日期范围起始日期
         */
        @JSONField(name = "date_range_start")
        private String dateRangeStart;
        /**
         * 查询日期范围截止日期。查询日期范围最大不得超过 31 天
         */
        @JSONField(name = "date_range_end")
        private String dateRangeEnd;

        public Req(String scenicNo, String goodsNo, String dateRangeStart, String dateRangeEnd) {
            this.scenicNo = scenicNo;
            this.goodsNo = goodsNo;
            this.dateRangeStart = dateRangeStart;
            this.dateRangeEnd = dateRangeEnd;
        }

        public Req() {
        }
    }

    @Data
    public static class Rsp {
        /**
         * 景区编号
         */
        @JSONField(name = "sale_scenic_no")
        private String saleScenicNo;
        /**
         * 商品编号
         */
        @JSONField(name = "goods_no")
        private String goodsNo;
        @JSONField(name = "date_prices")
        private List<DatePrice> datePrices;
    }

    @Data
    public static class DatePrice {
        /**
         * 是否可以购买
         */
        @JSONField(name = "can_buy")
        private Boolean canBuy;
        /**
         * 日期
         */
        private String date;
        /**
         * 结算价
         */
        @JSONField(name = "settle_price")
        private String settlePrice;
    }

}
