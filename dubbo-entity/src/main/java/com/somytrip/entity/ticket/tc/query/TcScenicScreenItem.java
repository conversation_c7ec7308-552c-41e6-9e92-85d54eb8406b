package com.somytrip.entity.ticket.tc.query;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description: 同程场次票Item
 * @author: pigeon
 * @created: 2025-06-25 14:27
 */
@Data
public class TcScenicScreenItem {
    /**
     * 场次开始时间
     */
    @JSONField(name = "ShowBeginTime")
    private LocalDateTime showBeginTime;
    /**
     * 场次结束时间
     */
    @JSONField(name = "ShowEndTime")
    private LocalDateTime showEndTime;
    /**
     * 场次ID
     */
    @JSONField(name = "ShowId")
    private String showId;
    /**
     * 场次名称
     */
    @JSONField(name = "ShowName")
    private String showName;
    /**
     * 剩余门票
     */
    @JSONField(name = "LeftAmount")
    private Integer leftAmount;
}
