package com.somytrip.entity.ticket.tc.query;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.annotation.JSONField;
import com.somytrip.entity.ticket.tc.TcHead;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-11-09 11:47
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TcRefundRuleBody implements TcHead {
    /**
     * 是否按照景区查询退改规则信息(true:按照景区查询；false:按照产品查询)
     */
    @JSONField(name = "ViewSceneryRule")
    private Boolean viewSceneryRule;
    /**
     * 如果ViewSceneryRule=true,ProductID默认为景区Id，
     * 如果ViewSceneryRule=false, ProductID默认为产品Id
     */
    @JSONField(name = "ProductID")
    private String productId;

    @Override
    public String md5Args() {
        return StrUtil.join("", productId);
    }
}
