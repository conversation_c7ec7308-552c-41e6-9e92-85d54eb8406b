package com.somytrip.entity.ticket.vo;


import cn.hutool.core.date.LocalDateTimeUtil;
import com.somytrip.entity.ticket.tc.query.TcScenicScreenItem;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * @Description: 价格相关
 * @author: pigeon
 * @created: 2024-07-25 14:13
 */
public class QueryScreen {
    @Data
    @Valid
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Req {
        @NotNull
        private LocalDate tripDate;
        @NotNull
        private Integer goodId;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ScreenItem {
        private String startTime;
        private String endTime;
        private String screenName;
        private String orderInfo;
        private Integer seat;

        public ScreenItem(TcScenicScreenItem item) {
            this.startTime = LocalDateTimeUtil.format(item.getShowBeginTime(), "HH:mm");
            this.endTime = LocalDateTimeUtil.format(item.getShowEndTime(), "HH:mm");
            this.screenName = item.getShowName();
            this.orderInfo = item.getShowId();
            this.seat = item.getLeftAmount();
        }
    }
}
