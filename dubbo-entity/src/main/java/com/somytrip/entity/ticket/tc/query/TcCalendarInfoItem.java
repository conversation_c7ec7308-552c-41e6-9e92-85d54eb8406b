package com.somytrip.entity.ticket.tc.query;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-11-09 11:50
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TcCalendarInfoItem {
    /**
     * 产品ID
     */
    private Integer priceId;
    /**
     * 旅游日期
     */
    private LocalDate date;
    /**
     * 票型名称
     */
    private String ticketName;
    /**
     * 门市价
     */
    private String marketAmount;
    /**
     * 分销结算价
     */
    private String agentAmount;
    /**
     * 同程价
     */
    private String tcAmount;
    /**
     * 单次预订最小票数
     */
    private Integer minTickets;
    /**
     * 单次预订最大票数
     */
    private Integer maxTickets;
    /**
     * 是否有库存限制(1表示有库存限制；0表示无库存限制)
     */
    private Boolean isStock;
    /**
     * 库存数量
     */
    private Integer stockNum;
}
