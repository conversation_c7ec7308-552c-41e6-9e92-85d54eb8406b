package com.somytrip.entity.ticket.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-11-20 11:11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("ticket_refund_record")
public class TicketRefundRecordDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 审核结果(申请中 APPLYING 审核通过 APPROVE, 审核拒绝 REJECT,VOUCHER_CONSUME)
     */
    @TableField("result")
    private String result;

    /**
     * 申请单号
     */
    @TableField("unique_apply_no")
    private String uniqueApplyNo;

    /**
     * 审核意见
     */
    @TableField("audit_suggestion")
    private String auditSuggestion;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 审核时间
     */
    @TableField("audit_time")
    private LocalDateTime auditTime;

    /**
     * 申请时间
     */
    @TableField("apply_time")
    private LocalDateTime applyTime;

    /**
     * 退订处理完成时间
     */
    @TableField("refund_time")
    private LocalDateTime refundTime;

    /**
     * 凭证id
     */
    @TableField("voucher_id")
    private String voucherId;
    /**
     * 产品订单编号
     */
    @TableField("out_order_no")
    private String outOrderNo;

    /**
     * 核销记录id
     */
    @TableField("consume_record_no")
    private String consumeRecordNo;

    /**
     * 退款金额
     */
    @TableField("refund_amount")
    private String refundAmount;
}
