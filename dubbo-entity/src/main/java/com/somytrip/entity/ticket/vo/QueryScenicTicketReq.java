package com.somytrip.entity.ticket.vo;

import com.somytrip.entity.ticket.PageHelper;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-11-08 14:33
 */
@Valid
@Data
public class QueryScenicTicketReq {
    /**
     * 搜索关键词
     */
    private String keyword;
    /**
     * 出行日期 yyyy-MM-dd
     */
    @NotBlank
    private String tripDate;
    /**
     * 城市code
     */
    private String cityCode;
    /**
     * 分页参数
     */
    @NotNull
    private PageHelper.Req pageHelper;
}
