package com.somytrip.entity.ticket.tc.increment;

import cn.hutool.core.util.StrUtil;
import com.somytrip.entity.ticket.tc.TcHead;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-11-09 17:26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OfflineGoodListBody implements TcHead {
    /**
     * 起始增量Id
     */
    private Integer maxIncrementId;
    /**
     * 查询数量
     */
    private Integer queryNumber;

    @Override
    public String md5Args() {
        return StrUtil.join("", maxIncrementId, queryNumber);
    }
}
