package com.somytrip.entity.ticket.vo;

import com.somytrip.entity.enums.ticket.TicketSourceEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-11-20 15:22
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NotifyHandlerVo<T> implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    private T body;
    private TicketSourceEnum source;
}
