package com.somytrip.entity.ticket.tc.query;

import cn.hutool.core.util.StrUtil;
import com.somytrip.entity.ticket.tc.TcHead;
import lombok.Data;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-11-08 15:06
 */
@Data
public class TcScenicListBody implements TcHead {
    private Integer pageIndex;
    private Integer pageSize;
    private Integer sceneryId;
    private Integer provinceId;
    private Integer cityId;

    @Override
    public String md5Args() {
        return StrUtil.join("", pageSize, pageIndex);
    }
}
