package com.somytrip.entity.ticket.tc.query;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.annotation.JSONField;
import com.somytrip.entity.ticket.tc.TcHead;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-11-09 11:47
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TcCalendarInfoBody implements TcHead {
    @JSONField(name = "PriceId")
    private Integer priceId;
    @JSONField(name = "TravelDate")
    private String travelDate;
    @JSONField(name = "MonthNumber")
    private Integer monthNumber;

    @Override
    public String md5Args() {
        return StrUtil.join("", priceId);
    }
}
