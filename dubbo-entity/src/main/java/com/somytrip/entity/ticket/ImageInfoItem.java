package com.somytrip.entity.ticket;

import com.somytrip.entity.ticket.tc.query.TcPicInfoItem;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-11-08 11:36
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ImageInfoItem {
    private String dec;
    private String url;

    public ImageInfoItem(TcPicInfoItem tcPicInfoItem) {
        this.dec = tcPicInfoItem.getSceneryImgRemarks();
        this.url = tcPicInfoItem.getSceneryImgPath();
    }
}
