package com.somytrip.entity.ticket.tc.query;

import com.alibaba.fastjson2.annotation.JSONField;
import com.somytrip.entity.ticket.tc.TcResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-11-09 15:38
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class TcScenicDetailResult extends TcResponse {
    @JSONField(name = "SceneryID")
    private Integer sceneryId;
    @JSONField(name = "SceneryName")
    private String sceneryName;
    @JSONField(name = "SceneryGrade")
    private Integer sceneryGrade;
    @JSONField(name = "SceneryAddress")
    private String sceneryAddress;
    @JSONField(name = "SceneryProvinceName")
    private String sceneryProvinceName;
    @JSONField(name = "SceneryCityName")
    private String sceneryCityName;
    @JSONField(name = "SceneryCountyName")
    private String sceneryCountyName;
    @JSONField(name = "ScenerySummary")
    private String scenerySummary;
    @JSONField(name = "SceneryDetailIntroduce")
    private String sceneryDetailIntroduce;
    @JSONField(name = "UserNotes")
    private TcUseNote userNotes;
    @JSONField(name = "SceneryLongitude")
    private Double sceneryLongitude;
    @JSONField(name = "SceneryLatitude")
    private Double sceneryLatitude;
    @JSONField(name = "TicketType")
    private Integer ticketType;
    @JSONField(name = "TicketTypeName")
    private String ticketTypeName;
    @JSONField(name = "SceneryAliasName")
    private String sceneryAliasName;
    @JSONField(name = "SceneryTrafficGuide")
    private String sceneryTrafficGuide;
    @JSONField(name = "PictureListInfo")
    private List<TcPicInfoItem> pictureListInfo;
}
