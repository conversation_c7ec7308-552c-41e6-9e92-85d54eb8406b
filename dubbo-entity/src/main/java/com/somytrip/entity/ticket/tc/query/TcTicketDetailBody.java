package com.somytrip.entity.ticket.tc.query;

import cn.hutool.core.util.StrUtil;
import com.somytrip.entity.ticket.tc.TcHead;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-11-09 11:47
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TcTicketDetailBody implements TcHead {
    /**
     * 每次请求查询的数量
     */
    private Integer pageSize;
    /**
     * 查询的页索引
     */
    private Integer pageIndex;
    /**
     * 景区id 【改成必传】
     */
    private Integer sceneryId;
    /**
     * 价格策略id【改成必传】
     */
    private Integer priceId;
    /**
     * 景区名称
     */
    private String sceneryName;
    /**
     * 产品名称
     */
    private String ticketName;

    @Override
    public String md5Args() {
        return StrUtil.join("", pageSize, pageIndex);
    }
}
