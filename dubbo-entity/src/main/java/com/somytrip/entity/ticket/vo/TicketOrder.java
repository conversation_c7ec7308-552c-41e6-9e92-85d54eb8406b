package com.somytrip.entity.ticket.vo;

import com.somytrip.entity.enums.ticket.TicketSourceEnum;
import com.somytrip.entity.ticket.otm.order.CreateOrder;
import com.somytrip.entity.ticket.otm.scenic.ProductTicketDetail;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * @Description: 门票订单
 * @author: pigeon
 * @created: 2024-07-25 16:47
 */
public class TicketOrder {
    @Data
    @Valid
    public static class Req {
        @NotNull
        private List<CreateItem> orders;
        @NotBlank
        private String uid;
    }

    @Data
    @Valid
    public static class CreateItem {
        @NotNull
        private Integer goodId;
        @NotNull
        private List<Integer> tripUserInfoIds;
        @NotBlank
        private String tripDate;
    }

    @Data
    public static class BigField {
        private TicketSourceEnum source;
        private List<CreateOrder.Req> reqs;
        private List<ProductTicketDetail.Rsp> goodDetails;

        public BigField(List<CreateOrder.Req> reqs, List<ProductTicketDetail.Rsp> goodDetails) {
            this.reqs = reqs;
            this.goodDetails = goodDetails;
        }
    }
}
