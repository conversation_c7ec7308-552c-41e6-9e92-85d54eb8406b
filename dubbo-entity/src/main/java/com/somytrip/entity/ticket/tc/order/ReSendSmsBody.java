package com.somytrip.entity.ticket.tc.order;

import cn.hutool.core.util.StrUtil;
import com.somytrip.entity.ticket.tc.TcHead;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-11-12 10:00
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReSendSmsBody implements TcHead {
    /**
     * 同程订单流水号
     */
    private List<String> serialId;
    /**
     * 手机号码
     * （当客户更改手机号码后可以通过此参数传值）
     */
    private String sendMobile;

    @Override
    public String md5Args() {
        return StrUtil.concat(true, StrUtil.join("", serialId), sendMobile);
    }
}
