package com.somytrip.entity.ticket.tc.query;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-11-08 15:17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TcScenicListItem {
    @JSONField(name = "SceneryID")
    private Integer sceneryId;
    @JSONField(name = "SceneryName")
    private String sceneryName;
    @JSONField(name = "SceneryGrade")
    private Integer sceneryGrade;
    @JSONField(name = "SceneryAddress")
    private String sceneryAddress;
    @JSONField(name = "SceneryProvinceName")
    private String sceneryProvinceName;
    @JSONField(name = "SceneryCityName")
    private String sceneryCityName;
    @JSONField(name = "ScenerySummary")
    private String scenerySummary;
    @JSONField(name = "TicketType")
    private Integer ticketType;
    @JSONField(name = "TicketTypeName")
    private String ticketTypeName;
    @JSONField(name = "SceneryAliasName")
    private String sceneryAliasName;
    @JSONField(name = "SceneryTrafficGuide")
    private String sceneryTrafficGuide;
    @JSONField(name = "UserNotes")
    private List<TcUserNoteItem> userNotes;
    @JSONField(name = "SceneryDetailIntroduce")
    private String sceneryDetailIntroduce;
    @JSONField(name = "PictureListInfo")
    private List<TcPicInfoItem> pictureListInfo;
}
