package com.somytrip.entity.ticket.tc.order;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-11-09 17:52
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CheckUserItem {
    /**
     * 取票人姓名
     */
    @JSONField(name = "Name")
    private String name;
    /**
     * 证件类型
     * 不传默认身份证。
     * 6050201：身份证；
     * 6050202：护照；
     * 6050203：台胞证；
     * 6050204：港澳通行证；
     */
    @JSONField(name = "IdCardType")
    private String idCardType;
    /**
     * 取票人手机号码
     */
    @JSONField(name = "Mobile")
    private String mobile;
    /**
     * 身份证/护照号码
     */
    @JSONField(name = "IdCard")
    private String idCard;
    /**
     * 取票人邮箱
     */
    @JSONField(name = "Email")
    private String email;
}
