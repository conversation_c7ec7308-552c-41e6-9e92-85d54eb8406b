package com.somytrip.entity.ticket.vo;

import cn.hutool.core.util.EnumUtil;
import com.somytrip.entity.dto.ticket.ScenicTicketGoodDTO;
import com.somytrip.entity.enums.ticket.RefundChangeEnum;
import com.somytrip.entity.enums.ticket.TicketTypeEnum;
import com.somytrip.entity.ticket.otm.scenic.ProductTicketDetail;
import com.somytrip.entity.vo.ScenicDetailVo;
import com.somytrip.utils.CommonUtil;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

/**
 * @Description: 查询详情
 * @author: pigeon
 * @created: 2024-07-23 14:20
 */
public class QueryDetail {

    @Data
    @Valid
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Req {
        @NotNull
        private Long ticketId;
        private LocalDate tripDate;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Rsp {
        /**
         * 景区描述
         */
        private ScenicDetailVo scenicDetail;
        /**
         * 可以售卖的商品列表
         */
        private List<RspItem> goods;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RspItem {
        /**
         * 预订须知
         */
        private BookInfo bookInfo;
        /**
         * 商品info
         */
        private Good goodInfo;

        public RspItem(ProductTicketDetail.Rsp rsp, ScenicTicketGoodDTO dto, List<String> creditType) {
            List<String> periods = List.of("购买时指定入园日期当天有效，出园再入园需另外购票",
                    String.format("开园时间：%s， 闭园时间：%s, 开闭园时间仅供参考，以景区现场公告为准",
                            CommonUtil.convertMinutesToTime(rsp.getUsePolicy().getUseRule().getStartInTime()),
                            CommonUtil.convertMinutesToTime(rsp.getUsePolicy().getUseRule().getEndInTime())));

            this.bookInfo = new BookInfo(new RefundChangeInfo(
                    EnumUtil.fromString(RefundChangeEnum.class, rsp.getAfterSale().getRefund()).getDec() + "退票",
                    EnumUtil.fromString(RefundChangeEnum.class, rsp.getAfterSale().getChange()).getDec() + "改期"
            ), creditType, periods, rsp.getReservationNotes(), rsp.getTips());
            long quantity = -1;
            if (rsp.getSku().getContractQuantity() != 0) {
                quantity = rsp.getSku().getContractQuantity() - rsp.getSku().getContractSoldNum();
            }
            String price = "";
            try {
                price = rsp.getPricePolicy().getSettlePrice().getPriceData().get(0).getPrice();
            } catch (Exception e) {
                System.out.println();
            }
            this.goodInfo = new Good(dto.getId(),
                    rsp.getCover(),
                    rsp.getDescription(),
                    rsp.getGoodsName(),
                    dto.getType(), quantity, price);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Good {
        private Long goodId;
        /**
         * 首图
         */
        private String cover;
        /**
         * 描述
         */
        private String description;
        /**
         * 名称
         */
        private String goodName;
        /**
         * 商品类型
         */
        private TicketTypeEnum type;
        /**
         * 余票
         */
        private long quantity;
        /**
         * 预估价
         */
        private String price;
        /**
         * 是否是场次票
         */
        private Boolean isScreen;

        public Good(Long goodId, String cover, String description, String goodName, TicketTypeEnum type, long quantity, String price) {
            this.goodId = goodId;
            this.cover = cover;
            this.description = description;
            this.goodName = goodName;
            this.type = type;
            this.quantity = quantity;
            this.price = price;
            this.isScreen = false;
        }
    }

    /**
     * 预订须知
     */
    @Data
    @AllArgsConstructor
    public static class BookInfo {
        /**
         * 退改规则
         */
        private RefundChangeInfo refundChangeInfo;
        /**
         * 支持的证件
         */
        private List<String> supportCertType;
        /**
         * 有效期
         */
        private List<String> periodValidity;
        /**
         * 预订须知
         */
        private String reservationNotes;
        /**
         * 温馨提醒
         */
        private String tips;
        /**
         * 联系客服
         */
        private String contactService;


        public BookInfo() {
            this.contactService = "如有订单相关问题请拨打平台客服热线4001-520-999";
        }

        public BookInfo(RefundChangeInfo refundChangeInfo, List<String> supportCertType, List<String> periodValidity, String reservationNotes, String tips) {
            this.refundChangeInfo = refundChangeInfo;
            this.supportCertType = supportCertType;
            this.periodValidity = periodValidity;
            this.reservationNotes = reservationNotes;
            this.tips = tips;
            this.contactService = "如有订单相关问题请拨打平台客服热线4001-520-999";
        }
    }

    /**
     * 退改须知
     */
    @Data
    public static class RefundChangeInfo {
        private String refundDec;
        private String changeDec;

        public RefundChangeInfo(String refundDec, String changeDec) {
            this.refundDec = refundDec;
            this.changeDec = changeDec;
        }
    }
}
