package com.somytrip.entity.ticket.otm.order;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * @Description: 创建订单
 * @author: pigeon
 * @created: 2024-07-19 15:19
 */
public class CreateOrder {
    @Data
    public static class Req {
        /**
         * 景区编号
         */
        @JSONField(name = "sale_scenic_no")
        private String saleScenicNo;
        /**
         * 商品编号
         */
        @JSONField(name = "goods_no")
        private String goodsNo;
        /**
         * OTA 订单号
         */
        @JSONField(name = "out_order_no")
        private String outOrderNo;
        /**
         * 购买份数
         */
        private Integer quantity;
        /**
         * OTA 销售单价
         */
        @JSONField(name = "sale_price")
        private String salePrice;
        /**
         * 购买人信息，若订单项商品为年卡，则该商品的
         * purchase_conditions 购买条件以购买人信息为依据。
         */
        private Buyer buyer;
        /**
         * 购买商品的必要条件，若订单项商品为年卡，则该订
         * 单项只允许一项。
         */
        @JSONField(name = "order_items")
        private List<OrderItem> orderItems;
        /**
         * 是否发送短信(默认 false 不发送短信)
         */
        @JSONField(name = "send_sms")
        private boolean sendSms;
    }

    @Data
    public static class OrderItem {
        /**
         * 订单子项号
         */
        @JSONField(name = "out_order_item_id")
        private String outOrderItemId;
        /**
         * 指定出行日 POINT_DAY_VALIDITY 类型的商品必要条
         * 件，预计出行日期(yyyy-MM-dd)
         */
        @JSONField(name = "appoint_trip_date")
        private String appointTripDate;
        /**
         * 出行人分配到的商品份数，多出行人时，该参数的总
         * 和必须等于 quantity 商品购买份数
         */
        @JSONField(name = "allocation_quantity")
        private Integer allocationQuantity;
        /**
         * 出行人信息，若该商品有 purchase_conditions 购买条
         * 件，该参数必需有，且需按 purchase_conditions 的要
         * 求提供对应参数 。除年卡商品外必填
         */
        @JSONField(name = "travel_user")
        private List<TravelUser> travelUser;
    }

    @Data
    public static class TravelUser {
        /**
         * 出行人姓名
         */
        @JSONField(name = "travel_user_name")
        private String travelUserName;
        /**
         * 出行人手机号码（接受凭证使用）
         */
        @JSONField(name = "travel_user_mobile")
        private String travelUserMobile;
        /**
         * 证件类型,参照商品属性support_certificate_type支持
         * 的证件类型
         */
        @JSONField(name = "certificate_type")
        private String certificateType;
        /**
         * 证件号
         */
        @JSONField(name = "certificate_value")
        private String certificateValue;
        /**
         * 人脸用户 ID
         */
        @JSONField(name = "face_user_id")
        private String faceUserId;
        /**
         * JSON 格式
         */
        @JSONField(name = "extend_info")
        private String extendInfo;
    }

    @Data
    public static class Buyer {
        /**
         * 购买人姓名
         */
        @JSONField(name = "buyer_name")
        private String buyerName;
        /**
         * 购买人手机号码
         */
        @JSONField(name = "buyer_mobile")
        private String buyerMobile;
        /**
         * JSON 格式
         */
        @JSONField(name = "extend_info")
        private String extendInfo;

        public Buyer(String buyerName, String buyerMobile, String extendInfo) {
            this.buyerName = buyerName;
            this.buyerMobile = buyerMobile;
            this.extendInfo = extendInfo;
        }
    }

}
