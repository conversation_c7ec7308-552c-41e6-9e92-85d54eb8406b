package com.somytrip.entity.ticket.tc.query;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-11-09 15:17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TcTicketDetailItem {
    /**
     * 产品Id
     */
    private Integer ticketPriceId;
    /**
     * 产品名称
     */
    private String ticketName;
    /**
     * 产品名称
     */
    private String effectiveBeginDate;
    /**
     * 票型有效期结束时间
     */
    private String effectiveEndDate;
    /**
     * 票型开始售卖日期
     */
    private String startSellDate;
    /**
     * 票型结束售卖日期
     */
    private String stopSellDate;
    /**
     * 屏蔽日期
     */
    private List<LocalDate> exceptDate;
    /**
     * 景区Id
     */
    private Integer sceneryId;
    /**
     * 景区名称
     */
    private String sceneryName;
    /**
     * 门市价
     */
    private String marketPrice;
    /**
     * 同程价
     */
    private String tcAmountPrice;
    /**
     * 分销商结算价
     */
    private String agentPrice;
    /**
     * 是否是实名制
     */
    private Boolean isRealName;
    /**
     * 是否需要填写证件
     */
    private Boolean isNeedIdCard;
    /**
     * 证件类型
     * 支持的证件类型代码：6050201：身份证；6050202：护照；6050203：台胞证；6050204：港澳通行证；
     */
    private List<String> certificateType;
    /**
     * 单次购买最小量
     */
    private Integer minSaleQty;
    /**
     * 单次购买最大量
     */
    private Integer maxSaleQty;
    /**
     * 下单提前时间
     */
    private Integer reserveBeforeDays;
    /**
     * 几点之前下单
     */
    private String reserveBeforeTime;
    /**
     * 下单后N小时入园
     */
    private Integer reserveAdvanceTime;
    /**
     * 预定频次限制
     */
    private Integer reserveType;
    /**
     * 下单次数
     */
    private Integer reserveTimes;
    /**
     * 总票数
     */
    private Integer reserveTotalTickets;
    /**
     * 限制购买天数
     */
    private Integer reserveDaysLimit;
    /**
     * 产品单元
     */
    private Integer productUnit;
    /**
     * 产品单元名称
     */
    private String productUnitName;
    /**
     * 是否开放平台
     */
    @JSONField(name = "IsOpenPlat")
    private Integer isOpenPlat;
    /**
     * 验证方式
     */
    @JSONField(name = "reservecheckWay")
    private String reserveCheckWay;
    /**
     * 是否支持智能刷卡
     */
    private Boolean isIntelligent;
    /**
     * 是否支持邮箱
     */
    private Boolean isNeedMail;
    /**
     * 是否支持护照
     */
    private Boolean isPassport;
    /**
     * 是否支持场次
     */
    private Boolean isScreening;
    /**
     * 是否支持通行证
     */
    private Boolean isPass;
    /**
     * 是否邮寄
     */
    @JSONField(name = "IsPost")
    private Boolean isPost;
    /**
     * 邮费
     */
    private String postPage;
    /**
     * 是否支持备注
     */
    private Boolean isReMark;
    /**
     * 包含项目
     */
    private String containedItems;
    /**
     * 资源合作模式
     */
    @JSONField(name = "BCTTicketPriceMode")
    private Integer bctTicketPriceMode;
    /**
     * 取票地址
     */
    private String getTicketMode;
    /**
     * 针对人群
     */
    private Integer consumers;
    /**
     * 针对人群名称
     */
    private String consumersName;
    /**
     * 主题编号
     */
    private List<Integer> ticketType;
    /**
     * 主题名称
     */
    private List<String> ticketTypeName;
    /**
     * 预定说明
     */
    private String ticketPriceRemark;
    /**
     * 是否合作系统票型
     */
    private Boolean isCopSys;
    /**
     * 是否特价票
     */
    private Boolean isPreference;
    /**
     * 是否预约票
     */
    private Boolean isBespeakTicket;
}
