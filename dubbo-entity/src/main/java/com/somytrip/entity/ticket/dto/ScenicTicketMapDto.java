package com.somytrip.entity.ticket.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.somytrip.entity.enums.ticket.TicketSourceEnum;
import com.somytrip.entity.ticket.ScenicBaseInfo;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description: 景区映射表
 * @author: pigeon
 * @created: 2024-11-08 11:02
 */
@Data
@TableName(value = "scenic_ticket_map", autoResultMap = true)
public class ScenicTicketMapDto {
    // 唯一确认一个景区：scenicId+outScenicNo+source+cityCode
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 景区表外键
     */
    private Long scenicId;
    /**
     * 城市表外键 城市code
     */
    private String cityCode;
    /**
     * 同程：景区ID
     * 华侨城：销售code
     */
    private String outScenicNo;
    /**
     * 供应商
     */
    private TicketSourceEnum source;
    /**
     * 供应商景区数据
     */
    @TableField(typeHandler = Fastjson2TypeHandler.class)
    private ScenicBaseInfo outScenicInfo;
    /**
     * 是否上线
     */
//    @TableLogic(delval = "0", value = "1")
    private Boolean online;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
}
