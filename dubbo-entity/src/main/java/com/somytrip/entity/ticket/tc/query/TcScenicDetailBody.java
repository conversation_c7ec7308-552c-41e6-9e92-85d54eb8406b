package com.somytrip.entity.ticket.tc.query;

import cn.hutool.core.util.StrUtil;
import com.somytrip.entity.ticket.tc.TcHead;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-11-09 11:47
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TcScenicDetailBody implements TcHead {
    private Integer sceneryId;

    @Override
    public String md5Args() {
        return StrUtil.join("", sceneryId);
    }
}
