package com.somytrip.entity.ticket.otm.scenic;

import com.alibaba.fastjson2.annotation.JSONField;
import jakarta.validation.constraints.Max;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Description: 景区商品列表
 * @author: pigeon
 * @created: 2024-07-18 11:47
 */
public class ProductList {
    @Data
    public static class Req {
        /**
         * 景区编号
         */
        @JSONField(name = "scenic_no")
        private String scenicNo;
        /**
         * 分页参数 1
         */
        @JSONField(name = "page_index")
        private Integer pageIndex;
        /**
         * 分页参数 10
         */
        @JSONField(name = "page_size")
        @Max(value = 50)
        private Integer pageSize;

        public Req(String scenicNo, Integer pageIndex, Integer pageSize) {
            this.scenicNo = scenicNo;
            this.pageIndex = pageIndex;
            this.pageSize = pageSize;
        }

        public Req() {
        }

        public int pageIndexSize() {
            return pageIndex * pageSize;
        }
    }

    @Data
    public static class Rsp {
        /**
         * 记录数
         */
        private Integer count;
        /**
         * 当前请求页页号
         */
        @JSONField(name = "page_index")
        private Integer pageIndex;
        /**
         * 当前请求每页数据记录条数
         */
        @JSONField(name = "page_size")
        private Integer pageSize;
        /**
         * 商品列表
         */
        private List<Item> items;
    }

    @Data
    public static class Item {
        /**
         * 销售景区编码
         */
        @JSONField(name = "sale_scenic_no")
        private String saleScenicNo;
        /**
         * 销售景区名称
         */
        @JSONField(name = "sale_scenic_name")
        private String saleScenicName;
        /**
         * 供应景区编码
         */
        @JSONField(name = "supply_scenic_no")
        private String supplyScenicNo;
        /**
         * 供应景区名称
         */
        @JSONField(name = "supply_scenic_name")
        private String supplyScenicName;
        /**
         * 商品编号
         */
        @JSONField(name = "goods_no")
        private String goodsNo;
        /**
         * 商品名称
         */
        @JSONField(name = "goods_name")
        private String goodsName;
        /**
         * 商品简称
         */
        @JSONField(name = "goods_shortname")
        private String goodsShortname;
        /**
         * 商品业态编码[景区门票:"SCENIC_TICKET", 套餐:"PACKAGE_GOODS", 消
         * 费券:"COUPON", 场馆:"VENUE", 电子年卡:"YEAR_CARD"]
         */
        @JSONField(name = "goods_type_code")
        private String goodsTypeCode;
        /**
         * 商品业态名称
         */
        @JSONField(name = "goods_type_name")
        private String goodsTypeName;
    }

    @EqualsAndHashCode(callSuper = false)
    @Data
    public static class SubGood extends Item {
        private Integer quantity;
    }
}
