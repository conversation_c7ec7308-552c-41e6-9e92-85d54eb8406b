package com.somytrip.entity.ticket.tc.order;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.annotation.JSONField;
import com.somytrip.entity.ticket.tc.TcHead;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-11-12 10:00
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryFeedBackBody implements TcHead {
    /**
     * 同程订单流水号
     */
    @JSONField(name = "OrderSerialId")
    private String orderSerialId;
    /**
     * 反馈类型
     * 1：未收到取票码； 2：供应商无游客信息； 3：验票机刷不了 4: 行程变动退款(全部退款) 5: 入园受阻退款(全部退款) 6: 产品取消退款(全部退款) 7: 行程变动退款(部分退款) 8: 入园受阻退款(部分退款) 9：修改日期 10：退款申诉
     */
    @JSONField(name = "FeedBackType")
    private String feedBackType;

    @Override
    public String md5Args() {
        return StrUtil.join("", orderSerialId);
    }
}
