package com.somytrip.entity.ticket.otm.order;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description: 订单详情
 * @author: pigeon
 * @created: 2024-07-19 15:41
 */
public class QueryOrderDetail {
    @Data
    public static class Req {
        @JSONField(name = "out_order_no")
        private String outOrderNo;
        @JSONField(name = "sale_scenic_no")
        private String saleScenicNo;

        public Req() {
        }

        public Req(String outOrderNo, String saleScenicNo) {
            this.outOrderNo = outOrderNo;
            this.saleScenicNo = saleScenicNo;
        }
    }

    @Data
    public static class Rsp {
        /**
         * OTA 订单号
         */
        @JSONField(name = "out_order_no")
        private String outOrderNo;
        /**
         * 订单号（OTM）
         */
        @JSONField(name = "order_no")
        private String orderNo;
        /**
         * 订单状态( 待确认 PRE_ORDER，已下单 ORDERED，全部退款 REFUND，
         * 部分退款 PART_REFUND )
         */
        @JSONField(name = "order_status")
        private String orderStatus;
        /**
         * 下单时间（格式 yyyy-MM-dd HH:mm:ss ）
         */
        @JSONField(name = "order_time")
        private String orderTime;
        /**
         * 若下单成功，则有该凭证信息
         */
        private List<Voucher> vouchers;
    }

    @Data
    public static class Voucher {
        /**
         * 凭证编号
         */
        @JSONField(name = "voucher_id")
        private String voucherId;
        /**
         * 凭证码
         */
        @JSONField(name = "voucher_no")
        private String voucherNo;
        /**
         * 订单子项号
         */
        @JSONField(name = "out_order_item_id")
        private String outOrderItemId;
        /**
         * 商品编号
         */
        @JSONField(name = "goods_no")
        private String goodsNo;
        /**
         * 商品名称
         */
        @JSONField(name = "goods_name")
        private String goodsName;
        /**
         * 商品简称
         */
        @JSONField(name = "goods_shortname")
        private String goodsShortname;
        /**
         * 商品业态编码
         */
        @JSONField(name = "goods_type_code")
        private String goodsTypeCode;
        /**
         * 商品业态名称
         */
        @JSONField(name = "goods_type_name")
        private String goodsTypeName;
        /**
         * 销售景区编码
         */
        @JSONField(name = "sale_scenic_no")
        private String saleScenicNo;
        /**
         * 销售景区名称
         */
        @JSONField(name = "sale_scenic_name")
        private String saleScenicName;
        /**
         * 商品销售单价
         */
        @JSONField(name = "sale_price")
        private String salePrice;
        /**
         * 商品结算单价
         */
        @JSONField(name = "settle_price")
        private String settlePrice;
        /**
         * 总份数
         */
        @JSONField(name = "all_copies")
        private Integer allCopies;
        /**
         * 已核销份数
         */
        @JSONField(name = "used_copies")
        private Integer usedCopies;
        /**
         * 已作废份数
         */
        @JSONField(name = "invalid_copies")
        private Integer invalidCopies;
        /**
         * 冻结份数
         */
        @JSONField(name = "freeze_copies")
        private Integer freezeCopies;
        /**
         * 凭证二维码链接地址
         */
        @JSONField(name = "voucher_image_url")
        private String voucherImageUrl;
        @JSONField(name = "valid_start_date")
        private Long validStartDate;
        @JSONField(name = "valid_end_date")
        private Long validEndDate;
        private String status;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ReqR {
        @JSONField(name = "apply_no")
        private String applyNo;
        @JSONField(name = "sale_scenic_no")
        private String saleScenicNo;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RspR {
        @JSONField(name = "order_no")
        private String orderNo;
        @JSONField(name = "out_order_no")
        private String outOrderNo;
        @JSONField(name = "voucher_id")
        private String voucherId;
        @JSONField(name = "apply_quantity")
        private Integer applyQuantity;
        @JSONField(name = "apply_no")
        private String applyNo;
        @JSONField(name = "refund_id")
        private String refundId;
        @JSONField(name = "refund_source")
        private String refundSource;
        @JSONField(name = "refund_amount")
        private String refundAmount;
        @JSONField(name = "refund_fee")
        private String refundFee;
        private String result;
        private String remarks;
        @JSONField(name = "audit_suggestion")
        private String auditSuggestion;
        @JSONField(name = "audit_time")
        private String auditTime;
        @JSONField(name = "apply_time")
        private String applyTime;
        @JSONField(name = "refund_time")
        private String refundTime;
    }
}
