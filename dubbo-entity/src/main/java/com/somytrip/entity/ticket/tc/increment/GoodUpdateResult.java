package com.somytrip.entity.ticket.tc.increment;

import com.somytrip.entity.ticket.tc.TcResponse;
import com.somytrip.entity.ticket.tc.query.TcTicketDetailItem;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-11-09 17:26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class GoodUpdateResult extends TcResponse {
    /**
     * 起始增量Id
     */
    private Integer maxIncrementId;
    /**
     * 查询数量
     */
    private List<TcTicketDetailItem> ticketPriceList;
}
