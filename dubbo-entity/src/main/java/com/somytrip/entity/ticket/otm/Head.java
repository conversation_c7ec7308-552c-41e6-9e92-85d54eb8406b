package com.somytrip.entity.ticket.otm;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @Description: 请求头数据
 * @author: pigeon
 * @created: 2024-07-18 9:42
 */
@Data
public class Head {
    private String version;
    @JsonProperty(value = "sequence_id")
    @JSONField(name = "sequence_id")
    private String sequenceId;
    private String timestamp;
    private String message;
    @JsonProperty(value = "distributor_id")
    @JSONField(name = "distributor_id")
    private String distributorId;
    @JsonProperty(value = "transaction_code")
    @JSONField(name = "transaction_code")
    private String transactionCode;
    @JsonProperty(value = "status_code")
    @JSONField(name = "status_code")
    private String statusCode;

    public boolean isSuccessful() {
        final String successCode = "200";
        return StrUtil.equals(successCode, statusCode);
    }
}
