package com.somytrip.entity.ticket.tc.order;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.annotation.JSONField;
import com.somytrip.entity.ticket.tc.TcHead;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-11-09 17:44
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderBookingBody implements TcHead {
    /**
     * 购买数量
     */
    private List<Integer> ticketsNum;
    /**
     * 预定人
     */
    private List<String> bookMan;
    /**
     * 预定人手机类型(1：大陆 0：非大陆)
     */
    private Integer bookMobileType;
    /**
     * 预定人手机号码
     */
    private List<String> bookMobile;
    /**
     * 预定人地址
     */
    private String bookAddress;
    /**
     * 预定人邮编
     */
    private String bookPostCode;
    /**
     * 预定人邮箱
     */
    private String bookEmail;
    /**
     * 取票人姓名
     */
    private List<String> travelerName;
    /**
     * 取票人手机号码
     */
    private List<String> travelerMobile;

    /**
     * 手机类型(1：大陆 0：非大陆)
     */
    private Integer travelerMobileType;
    /**
     * 产品ID
     */
    @JSONField(name = "priceID")
    private Integer priceId;
    /**
     * 旅游日期
     */
    private String travelDate;
    /**
     * 实名制内容
     */
    private String remark;
    /**
     * 取票人证件号
     * 根据产品验证方式决定，如果是证件验证模式，则需要传输身份证号码，否则，不需要
     */
    private String travelerIdCardNo;
    /**
     * 取票人证件类型
     */
    private String travelerCerType;
    /**
     * 取票人证件号码
     */
    private List<String> travelerIdCardNoList;
    /**
     * 取票人证件类型
     */
    private List<String> travelerCerTypeList;
    /**
     * 第三方流水号
     */
    private List<String> thirdSerialId;
    /**
     * 预订演出信息
     */
    private BookingScreenInfo sceneryScreeningInfo;
    /**
     * 收货人信息
     */
    private CheckPostInfo postReceiveInfo;

    @Override
    public String md5Args() {
        return StrUtil.join("", priceId,
                StrUtil.join("", travelerMobile),
                StrUtil.join("", thirdSerialId));
    }
}
