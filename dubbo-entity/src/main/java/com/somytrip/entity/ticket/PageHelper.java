package com.somytrip.entity.ticket;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Positive;
import lombok.Data;

/**
 * @Description: 分页参数
 * @author: pigeon
 * @created: 2024-07-19 17:22
 */
public class PageHelper {
    @Data
    @Valid
    public static class Req {
        @Positive
        @Max(value = 50)
        private Integer page;
        @Positive
        @Max(value = 50)
        private Integer size;

        public Req() {
        }

        public Req(Integer page, Integer size) {
            this.page = page;
            this.size = size;
        }
    }

    @Data
    public static class Rsp {
        private Integer count;
        private Integer nextPage;

        public Rsp() {
        }

        public Rsp(Integer count, Integer nextPage) {
            this.count = count;
            this.nextPage = nextPage;
        }

        public Rsp(long count, long nextPage) {
            this.count = Integer.valueOf(String.valueOf(count));
            this.nextPage = Integer.valueOf(String.valueOf(nextPage));
        }
    }
}
