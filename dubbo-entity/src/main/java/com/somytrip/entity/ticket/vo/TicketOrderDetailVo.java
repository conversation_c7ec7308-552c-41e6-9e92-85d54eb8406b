package com.somytrip.entity.ticket.vo;

import com.somytrip.entity.dto.order.TicketOrderDetail;
import com.somytrip.entity.dto.user.UserTripInfoVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-11-20 16:19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TicketOrderDetailVo {
    private List<TicketOrderDetail.Good> goods;
    private List<UserTripInfoVo> tripInfos;
    private Map<String, Integer> voucherGuestMapping;
}
