package com.somytrip.entity.ticket.vo;

import com.alibaba.fastjson2.JSONArray;
import com.somytrip.entity.dto.ticket.ScenicTicketGoodDTO;
import com.somytrip.entity.enums.ticket.TicketSourceEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-11-14 15:25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderDetail {
    private TicketSourceEnum source;
    private List<ScenicTicketGoodDTO> goods;
    private JSONArray reqs;
    private JSONArray goodDetails;
}
