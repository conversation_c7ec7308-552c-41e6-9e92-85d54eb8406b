package com.somytrip.entity.ticket.tc.order;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-11-09 17:50
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CheckScreenInfo {
    @JSONField(name = "ScreeningId")
    private String screeningId;
    @JSONField(name = "BeginTime")
    private String beginTime;
    @JSONField(name = "EndTime")
    private String endTime;
}
