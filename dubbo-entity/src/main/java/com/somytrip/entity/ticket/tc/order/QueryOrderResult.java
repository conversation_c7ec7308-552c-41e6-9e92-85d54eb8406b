package com.somytrip.entity.ticket.tc.order;

import com.somytrip.entity.ticket.tc.TcResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description: 分销商订单查询接口 body
 * @author: pigeon
 * @created: 2024-11-09 17:44
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class QueryOrderResult extends TcResponse {
    private Integer totalCount;
    private List<QueryOrderItem> orderList;
}
