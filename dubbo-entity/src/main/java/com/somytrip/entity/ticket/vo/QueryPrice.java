package com.somytrip.entity.ticket.vo;

import com.somytrip.entity.ticket.otm.scenic.ProductDailyPrice;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

/**
 * @Description: 价格相关
 * @author: pigeon
 * @created: 2024-07-25 14:13
 */
public class QueryPrice {
    @Data
    @Valid
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Req {
        @NotNull
        private LocalDate startDate;
        @NotNull
        private LocalDate endDate;
        @NotNull
        private Integer goodId;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CalendarRsp {
        private List<CalendarItem> datePrices;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CalendarItem {
        private boolean canBuy;
        private String price;
        private String date;

        public CalendarItem(ProductDailyPrice.DatePrice price) {
            this.canBuy = price.getCanBuy();
            this.price = price.getSettlePrice();
            this.date = price.getDate();
        }
    }
}
