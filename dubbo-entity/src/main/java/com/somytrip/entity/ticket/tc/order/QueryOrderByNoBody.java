package com.somytrip.entity.ticket.tc.order;

import cn.hutool.core.util.StrUtil;
import com.somytrip.entity.ticket.tc.TcHead;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description: 分销商订单查询接口 body
 * @author: pigeon
 * @created: 2024-11-09 17:44
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryOrderByNoBody implements TcHead {
    private List<String> thirdSerialId;

    @Override
    public String md5Args() {
        return StrUtil.join("", thirdSerialId);
    }
}
