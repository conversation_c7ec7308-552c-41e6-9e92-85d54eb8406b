package com.somytrip.entity.ticket.tc.increment;

import com.somytrip.entity.ticket.tc.TcResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-11-09 17:26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class ScenicUpdateResult extends TcResponse {
    /**
     * 起始增量Id
     */
    private Integer maxIncrementId;
    /**
     * 景区列表
     * 根据增量类型返回对应增量类型的景区Id(当请求增量类型为0时，该列表不返回值)
     */
    private List<Integer> sceneryId;
    /**
     * 景区信息列表
     */
    private List<SceneryInfo> sceneryList;
}
