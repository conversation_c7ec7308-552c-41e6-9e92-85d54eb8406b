package com.somytrip.entity.ticket.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.somytrip.entity.enums.ticket.TicketSourceEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description: 门票商品表
 * @author: pigeon
 * @created: 2024-11-08 11:02
 */
@Data
@TableName(value = "scenic_ticket_goods", autoResultMap = true)
public class ScenicTicketGoodDto {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 映射表外键
     */
    private Long ticketId;
    /**
     * 景区表外键
     */
    private Long scenicId;
    /**
     * 同程：priceId
     * 华侨城：销售code
     */
    private String outGoodNo;
    /**
     * 供应商
     */
    private TicketSourceEnum source;
    /**
     * 商品类型
     */
    private String goodType;
    /**
     * 供应商景区数据
     */
    private String outOriginalJson;
    /**
     * 是否上线
     */
    private Boolean online;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 修改时间
     */
    private LocalDateTime updateTime;
}
