package com.somytrip.entity.ticket.tc.order;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.annotation.JSONField;
import com.somytrip.entity.ticket.tc.TcHead;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-11-12 10:00
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryCodeNumBody implements TcHead {
    /**
     * 同程订单流水号
     */
    @JSONField(name = "OrderSerialIds")
    private List<String> orderSerialIds;

    @Override
    public String md5Args() {
        return StrUtil.join("", orderSerialIds);
    }
}
