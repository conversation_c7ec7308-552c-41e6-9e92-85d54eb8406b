package com.somytrip.entity.ticket.otm.scenic;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * @Description: 获取合作景点信息
 * @author: pigeon
 * @created: 2024-07-18 9:40
 */
public class ScenicList {
    @Data
    public static class Req {
        /**
         * 景区名称
         */
        @JSONField(name = "scenic_name")
        private String scenicName;
        /**
         * 景区编号
         */
        @JSONField(name = "scenic_no")
        private String scenicNo;
    }

    @Data
    public static class Rsp {
        /**
         * 记录数
         */
        private Long count;
        /**
         * 景区列表
         */
        @JSONField(name = "scenic_spots")
        private List<Req> scenicSpots;
    }
}
