package com.somytrip.entity.ticket.tc.order;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.annotation.JSONField;
import com.somytrip.entity.ticket.tc.TcHead;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 取消订单
 * @author: pigeon
 * @created: 2024-11-12 10:00
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RefundOrderBody implements TcHead {
    /**
     * 订单流水号
     */
    private String serialId;
    /**
     * 退票数量
     * 全部退款可以不传退票数量
     */
    private Integer refundTicketsNum;
    /**
     * 0：部分退款 1：全部退款
     */
    @JSONField(name = "RefundType")
    private Integer refundType;
    /**
     * 退款原因id：
     * 1：行程取消；
     * 2：门票预订错误；
     * 3：未收到取票确认号；
     * 4：景区无入园信息；
     * 5：价格不优惠；
     * 6：景区爆满/闭园；
     * 7：其他；
     */
    @JSONField(name = "RefundReasonId")
    private Integer refundReasonId;

    @Override
    public String md5Args() {
        return StrUtil.join("", serialId);
    }
}
