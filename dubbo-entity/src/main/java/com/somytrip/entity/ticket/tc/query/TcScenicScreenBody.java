package com.somytrip.entity.ticket.tc.query;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.annotation.JSONField;
import com.somytrip.entity.ticket.tc.TcHead;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 同程场次票
 * @author: pigeon
 * @created: 2025-06-25 14:23
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TcScenicScreenBody implements TcHead {
    /**
     * 产品ID
     */
    @JSONField(name = "PriceId")
    private Integer priceId;
    /**
     * 出行日期
     */
    @JSONField(name = "TravelDate")
    private String travelDate;

    @Override
    public String md5Args() {
        return StrUtil.join("", priceId, travelDate);
    }
}
