package com.somytrip.entity.ticket.vo;

import com.somytrip.entity.ticket.PageHelper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-11-08 14:33
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryScenicTicketResult {
    private List<QueryScenicTicketItem> scenicTickets;
    private PageHelper.Rsp pageNext;

    public QueryScenicTicketResult(PageHelper.Rsp pageNext) {
        this.pageNext = pageNext;
    }

}
