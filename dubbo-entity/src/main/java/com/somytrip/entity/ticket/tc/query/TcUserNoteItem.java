package com.somytrip.entity.ticket.tc.query;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-11-08 15:17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TcUserNoteItem {
    @JSONField(name = "BusinessHours")
    private String businessHours;
    @JSO<PERSON>ield(name = "GetTicketAddress")
    private String getTicketAddress;
    @JSONField(name = "RuYuanVoucher")
    private String ruYuanVoucher;
    @JSONField(name = "FavourePolicy")
    private String favouredPolicy;
    @J<PERSON><PERSON>ield(name = "Reminder")
    private String reminder;
}
