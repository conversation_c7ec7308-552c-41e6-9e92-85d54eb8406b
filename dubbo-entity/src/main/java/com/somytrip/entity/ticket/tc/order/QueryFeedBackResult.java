package com.somytrip.entity.ticket.tc.order;

import com.alibaba.fastjson2.annotation.JSONField;
import com.somytrip.entity.ticket.tc.TcResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description:
 * @author: pigeon
 * @created: 2024-11-12 10:00
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class QueryFeedBackResult extends TcResponse {
    @JSONField(name = "QueryList")
    private List<QueryFeedBackItem> queryList;
}
