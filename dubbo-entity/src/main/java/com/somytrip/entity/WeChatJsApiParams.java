package com.somytrip.entity;

/**
 * @Description:
 * @author: yefuxing
 * @created: 2023-10-20 15:21
 */

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class WeChatJsApiParams {
    @JsonProperty("combine_appid")
    @JSONField(name = "combine_appid")
    private String combineAppid;
    @JsonProperty("combine_out_trade_no")
    @JSONField(name = "combine_out_trade_no")
    private String combineOutTradeNo;
    @JsonProperty("combine_mchid")
    @JSONField(name = "combine_mchid")
    private String combineMchid;
    @JsonProperty("scene_info")
    @JSONField(name = "scene_info")
    private SceneInfo sceneInfo;
    @JsonProperty("sub_orders")
    @JSONField(name = "sub_orders")
    private List<SubOrder> subOrders;
    @JsonProperty("combine_payer_info")
    @JSONField(name = "combine_payer_info")
    private CombinePayerInfo combinePayerInfo;
    @JsonProperty("time_start")
    @JSONField(name = "time_start")
    private String timeStart;
    @JsonProperty("time_expire")
    @JSONField(name = "time_expire")
    private String timeExpire;
    @JsonProperty("notify_url")
    @JSONField(name = "notify_url")
    private String notifyUrl;
    @JsonProperty("limit_pay")
    @JSONField(name = "limit_pay")
    private List<String> limitPay;

    @Data
    public static class SceneInfo {
        @JsonProperty("device_id")
        @JSONField(name = "device_id")
        private String deviceId;
        @JsonProperty("payer_client_ip")
        @JSONField(name = "payer_client_ip")
        private String payerClientIp;
    }

    @Data
    public static class SubOrder {
        private String mchid;
        private String attach;
        private Amount amount;
        @JsonProperty("out_trade_no")
        @JSONField(name = "out_trade_no")
        private String outTradeNo;
        private String detail;
        private String description;
        @JsonProperty("settle_info")
        @JSONField(name = "settle_info")
        private SettleInfo settleInfo;
        @JsonProperty("goods_tag")
        @JSONField(name = "goods_tag")
        private String goodsTag;
    }

    @Data
    public static class Amount {
        @JsonProperty("total_amount")
        @JSONField(name = "total_amount")
        private Long totalAmount;
        private String currency;
    }

    @Data
    public static class SettleInfo {
        @JsonProperty("profit_sharing")
        @JSONField(name = "profit_sharing")
        private Boolean profitSharing;
        @JsonProperty("subsidy_amount")
        @JSONField(name = "subsidy_amount")
        private Long subsidyAmount;
    }

    @Data
    public static class CombinePayerInfo {
        private String openid;
    }
}




