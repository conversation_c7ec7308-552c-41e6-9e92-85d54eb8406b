package com.somytrip.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.somytrip.entity.enums.locale.LocaleDictType;
import com.somytrip.handler.LocaleTypeHandler;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Locale;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.entity
 * @className: LocaleDictEntity
 * @author: shadow
 * @description: 国际化字典Entity
 * @date: 2024/9/19 10:54
 * @version: 1.0
 */
@Data
@TableName(value = "locale_dict", autoResultMap = true)
public class LocaleDictEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 类型
     */
    private LocaleDictType type;

    /**
     * 表名
     */
    private String tableName;

    /**
     * 所属表主键ID
     */
    private Long pid;

    /**
     * 列名
     */
    private String columnName;

    /**
     * 国际化语种
     */
    @TableField(typeHandler = LocaleTypeHandler.class)
    private Locale locale;

    /**
     * 当前语种值
     */
    private String value;

    /**
     * 是否删除
     */
    @TableField("is_del")
    private boolean del;
}
