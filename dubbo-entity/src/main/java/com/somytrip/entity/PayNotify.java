package com.somytrip.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.util.Date;

@Data
public class PayNotify {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 订单号 全局唯一
     */
    private String outTradeNo;
    /**
     * 通知ID
     */
    private String notifyId;
    /**
     * 概要
     */
    private String summary;
    /**
     * 通知body
     */
    private String notifyContent;
    /**
     * 解析后的数据
     */
    private String dataContent;
    /**
     * 创建时间
     */
    private Date createTime;

    public PayNotify(String outTradeNo, String notifyId, String summary, String notifyContent,
                     String dataContent, Date createTime) {
        super();
        this.outTradeNo = outTradeNo;
        this.notifyId = notifyId;
        this.summary = summary;
        this.notifyContent = notifyContent;
        this.dataContent = dataContent;
        this.createTime = createTime;
    }

    public PayNotify() {
        super();
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}
