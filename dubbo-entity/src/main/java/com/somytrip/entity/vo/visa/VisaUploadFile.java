package com.somytrip.entity.vo.visa;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "somytrip_site.somytrip_third_with_visa_file", autoResultMap = true)
public class VisaUploadFile {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 用户ID 外键
     */
    private Integer userId;
    /**
     * 第三方上上签订单ID 外键
     */
    private Integer visaOrderId;
    /**
     * 文件id-文件path字典
     */
    @TableField(typeHandler = Fastjson2TypeHandler.class)
    private JSONObject fileNameDict;
    /**
     * 创建时间
     */
    private Date createTime;
}
