package com.somytrip.entity.vo.visa;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @Description:
 * @author: pigeon
 * @created: 2023-11-17 16:25
 */
@Data
public class UploadFileV2Vo {
    /**
     * 用户ID
     */
    @NotBlank
    private String userId;
    /**
     * 上上签订单ID
     */
    @NotBlank
    private String visaOrderId;
    /**
     * 上上签资料ID
     */
    @NotBlank
    private String materialId;
    /**
     * 上上签资料名称
     */
    @NotBlank
    private String materialName;
    /**
     * 文件
     */
    @NotNull
    private List<MultipartFile> file;
    /**
     * 申请人位于applicant_set的index
     * 从0开始
     */
    @NotNull
    private Integer index;
}
