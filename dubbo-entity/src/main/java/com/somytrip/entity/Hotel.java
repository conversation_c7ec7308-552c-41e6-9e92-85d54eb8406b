package com.somytrip.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-20
 */
@Deprecated
@Data
@TableName(value = "hotel")
public class Hotel {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 酒店名称
     */
    private String name;
    /**
     * ？？？？？？
     */
    private String simpleName;
    /**
     * 经度
     */
    private String lng;
    /**
     * 纬度
     */
    private String lat;
    /**
     * 高德经度
     */
    private String gLng;
    /**
     * 高德纬度
     */
    private String gLat;
    /**
     * 参考价格
     */
    private BigDecimal advicePrice;
    /**
     * 座机-固定电话
     */
    private String telNo;
    /**
     * 图片列表
     */
    private String pic;
    /**
     * 星级 - 文
     */
    private String star;
    /**
     * 星级 5 1-5
     */
    private String starV;
    /**
     * 省份 广东省
     */
    private String province;
    /**
     * 城市 深圳
     */
    private String city;
    /**
     * 详细地址
     */
    private String address;
    /**
     * 简介
     */
    private String brief;
    /**
     * 简介列表
     */
    private String facility;
    /**
     * 标签 ["十亿豪补减3", "限时折扣减28", "会议厅", "商务中心", "中文服务"]
     */
    private String tags;
    /**
     * 详情url
     */
    private String detailUrl;
    /**
     * 第三方网站hotelID
     */
    private Long hotelId;
}
