package com.somytrip.constant;

/**
 * @Description: 用户op相关Redis key
 * @author: pigeon
 * @created: 2025-01-07 9:47
 */
public interface UserRedisKeyConstant {
    public final String BINDING_REDIS_KEY_PREFIX = "user:oauth2:wx:applet:";
    public final String PHONE_VERIFY_CODE_REDIS_KEY = "user:verify:phone:code:";
    public final String PHONE_VERIFY_CODE_SERVICE_REDIS_KEY = "user:verify:phone:code:service:";
    public final String PHONE_VERIFY_CODE_RESET_REDIS_KEY = "user:verify:phone:code:reset:";
    public final String PHONE_VERIFY_CODE_COUNT_REDIS_KEY = "user:verify:phone:code:count:";
    public final String EMAIL_VERIFY_CODE_REDIS_KEY = "user:verify:email:code:";
    public final String EMAIL_RESET_VERIFY_CODE_REDIS_KEY = "user:verify:reset:email:code:";
}
