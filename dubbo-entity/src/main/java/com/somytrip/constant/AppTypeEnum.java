package com.somytrip.constant;


import lombok.Getter;

/**
 * 请求头枚举类型 APP类型，APPLET-WX、IOS、Android、H5、WEB
 */
@Getter
public enum AppTypeEnum {

    /**
     *
     */
    APPLET_WX("APPLET-WX", "微信小程序"),
    APPLET_DY("APPLET-DY", "抖音小程序"),
    APPLET_ALI("APPLET-ALI", "支付宝小程序"),
    IOS("IOS", "苹果"),
    ANDROID("Android", "安卓"),
    H5("H5", "h5端"),
    WEB("WEB", "pc web端"),
    ;

    private final String appTypeKey;

    private final String appTypeDesc;

    AppTypeEnum(String appTypeKey, String appTypeDesc) {
        this.appTypeDesc = appTypeDesc;
        this.appTypeKey = appTypeKey;
    }

    /**
     * appTypeKey to AppTypeEnum
     *
     * @param appTypeKey
     * @return
     */
    public static AppTypeEnum getAppTypeEunm(String appTypeKey) {
        for (AppTypeEnum appTypeEnum : AppTypeEnum.values()) {
            if (appTypeEnum.getAppTypeKey().equals(appTypeKey)) {
                return appTypeEnum;
            }
        }
        return H5;
    }
}
