package com.somytrip.constant;

import cn.hutool.crypto.symmetric.AES;

/**
 * @Description:
 * @author: pigeon
 * @created: 2025-05-16 17:26
 */
public class UserSm4Constant {
    private final static String REQ_PASSWORD_AES_KEY = "4pyd0zgevedwqt6c";
    private final static String REQ_PASSWORD_AES_IV = "a8al4ffzgfe3tkl0";

    private static final AES REQ_PWD_AES = new AES("CBC", "PKCS7Padding",
            REQ_PASSWORD_AES_KEY.getBytes(), REQ_PASSWORD_AES_IV.getBytes());

    public static String decryptPassword(String password) {
        return REQ_PWD_AES.decryptStr(password);
    }

    public static String encryptPassword(String password) {
        return REQ_PWD_AES.encryptHex(password);
    }
}
