package com.somytrip.validator;

import com.somytrip.target.AtLeastOneNotNull;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.springframework.beans.BeanWrapperImpl;

/**
 * @ClassName: AtLeastOneNotNullValidator
 * @Description: 至少一个不为null 验证器
 * @Author: shadow
 * @Date: 2024/1/5 10:32
 */
public class AtLeastOneNotNullValidator implements ConstraintValidator<AtLeastOneNotNull, Object> {

    private String[] fieldNames;
    private String message;

    @Override
    public void initialize(AtLeastOneNotNull constraintAnnotation) {
        fieldNames = constraintAnnotation.fieldNames();
        message = constraintAnnotation.message();
    }

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {

        if (value == null) {
            return true;
        }

        BeanWrapperImpl wrapper = new BeanWrapperImpl(value);
        for (String fieldName : fieldNames) {
            Object fieldValue = wrapper.getPropertyValue(fieldName);
            if (fieldValue != null) {
                return true;
            }
        }

        context.disableDefaultConstraintViolation();

        context.buildConstraintViolationWithTemplate(message)
                .addConstraintViolation();

        return false;
    }
}