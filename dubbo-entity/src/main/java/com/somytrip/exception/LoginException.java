package com.somytrip.exception;

import lombok.Getter;


@Getter
public class LoginException extends RuntimeException {
    private final int code;
    private final String msg;

    public LoginException() {
        code = 500;
        msg = "系统内部错误";
    }

    public LoginException(String msg) {
        code = 500;
        this.msg = msg;
    }

    public LoginException(int code) {
        this.code = code;
        this.msg = "系统内部错误";
    }

    public LoginException(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }


    @Override
    public String toString() {
        return "LoginException(code=" + code + ", msg=" + msg + ")";
    }
}
