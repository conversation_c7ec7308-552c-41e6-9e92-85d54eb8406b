package com.somytrip.exception;

import com.somytrip.entity.response.ResponseResult;
import com.somytrip.utils.JSONResultUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.sql.SQLIntegrityConstraintViolationException;
import java.sql.SQLSyntaxErrorException;

/**
 * @ClassName: SQLExceptionHandler
 * @Description: SQL异常处理类
 * @Author: shadow
 * @Date: 2023/11/10 11:50
 */
@Slf4j
@RestControllerAdvice
@Order(Ordered.LOWEST_PRECEDENCE - 2)
public class SQLExceptionHandler {

    @ExceptionHandler(SQLIntegrityConstraintViolationException.class)
    public ResponseResult exceptionHandler(SQLIntegrityConstraintViolationException e) {
//        log.error(e.getMessage(), e);
        log.info("SQL错误信息: {}", e.getMessage());
        // 完整性约束错误
        if (e.getMessage().contains("Duplicate entry")) {
            // 空格分隔
            String[] split = e.getMessage().split(" ");
            String msg = split[2] + "已存在";
            return JSONResultUtils.fail(msg);
        }
        // 在前端或者客户端返回友好提示
        return JSONResultUtils.fail("未知错误,无法定位");
    }

    @ExceptionHandler(SQLSyntaxErrorException.class)
    public ResponseResult exceptionHandler(SQLSyntaxErrorException e) {
        log.info("SQL错误信息: {}", e.getMessage(), e);
        if (e.getMessage().startsWith("Unknown column")) {
            String[] split = e.getMessage().split(" ");
            String columnName = split[2].replaceAll("'", "");
            if (columnName.contains(".")) {
                columnName = columnName.split("\\.")[1];
            }
            return JSONResultUtils.fail(columnName + "字段不存在");
        }
        return JSONResultUtils.fail("未知错误");
    }
}
