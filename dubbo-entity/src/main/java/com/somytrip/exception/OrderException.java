package com.somytrip.exception;

import lombok.Getter;

/**
 * @Description:
 * @author: pigeon
 * @created: 2023-11-01 14:52
 */
public class OrderException extends Exception {
    @Getter
    private final int code;
    private final String message;

    public OrderException(String message) {
        super(message);
        this.code = 500;
        this.message = message;
    }

    public OrderException(int code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public OrderException() {
        super();
        this.code = 500;
        this.message = "系统内部错误";
    }

    @Override
    public String getMessage() {
        return message;
    }
}
