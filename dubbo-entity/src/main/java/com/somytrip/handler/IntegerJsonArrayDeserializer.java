package com.somytrip.handler;

import com.alibaba.fastjson2.JSONArray;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;

import java.io.IOException;

/**
 * @ClassName: IntegerJsonArrayDeserializer
 * @Description:
 * @Author: shadow
 * @Date: 2023/11/10 17:18
 */
public class IntegerJsonArrayDeserializer extends JsonDeserializer<JSONArray> {

    @Override
    public JSONArray deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {
        JsonNode node = jsonParser.getCodec().readTree(jsonParser);
        return JSONArray.parseArray(node.toString());
    }
}
