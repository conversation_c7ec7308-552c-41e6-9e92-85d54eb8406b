package com.somytrip.handler;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;
import java.util.Arrays;

/**
 * @ClassName: IntegerArrayDeserializer
 * @Description:
 * @Author: shadow
 * @Date: 2023/11/10 10:48
 */
public class IntegerArrayDeserializer extends JsonDeserializer<Integer[]> {

    @Override
    public Integer[] deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {
        String[] stringNumbers = jsonParser.getValueAsString().split(",");
        return Arrays.stream(stringNumbers).map(Integer::parseInt).toArray(Integer[]::new);
    }
}
