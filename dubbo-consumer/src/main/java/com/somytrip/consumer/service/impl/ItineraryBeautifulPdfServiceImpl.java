package com.somytrip.consumer.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.itextpdf.html2pdf.ConverterProperties;
import com.itextpdf.html2pdf.HtmlConverter;
import com.itextpdf.html2pdf.css.apply.ICssApplierFactory;
import com.itextpdf.html2pdf.css.apply.impl.DefaultCssApplierFactory;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.font.FontProvider;
import com.somytrip.api.service.TencentCosService;
import com.somytrip.api.service.hotel.HotelGeneralService;
import com.somytrip.api.service.itinerary.ItineraryBeautifulPdfService;
import com.somytrip.api.service.tourism.GlobalCityService;
import com.somytrip.api.service.tourism.ScenicService;
import com.somytrip.consumer.service.ConfigService;
import com.somytrip.consumer.util.BeautifulLocaleUtil;
import com.somytrip.entity.dto.Scenic;
import com.somytrip.entity.dto.city.GlobalCityEntity;
import com.somytrip.entity.enums.ConfigEnum;
import com.somytrip.entity.enums.hotel.HotelOrigin;
import com.somytrip.entity.enums.itinerary.ItineraryActivityType;
import com.somytrip.entity.hotel.HotelGeneralEntity;
import com.somytrip.entity.itinerary.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.TextStyle;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.consumer.service.impl
 * @className: ItineraryBeautifulPdfServiceImpl
 * @author: shadow
 * @description: 攻略美观pdf Service实现类
 * @date: 2025/1/7 10:37
 * @version: 1.0
 */
@Slf4j
@Service
public class ItineraryBeautifulPdfServiceImpl implements ItineraryBeautifulPdfService {

    private static final String ACTIVITY_COVER_URL_PREFIX = "https://minio.somytrip.com/scenic/";
    @Resource
    private GlobalCityService globalCityService;
    @Resource
    private ScenicService scenicService;
    @Resource
    private ConfigService configService;
    @Resource
    private ResourceLoader resourceLoader;
    @Resource
    private TencentCosService tencentCosService;
    @DubboReference
    private HotelGeneralService hotelGeneralService;

    /**
     * 导出美观pdf
     *
     * @param infoEntity    攻略信息Entity
     * @param dayEntityList 攻略天Entity
     * @param tpData        交通数据
     * @return java.io.ByteArrayInputStream
     * <AUTHOR>
     * @date 2025/1/7 10:42
     */
    @Override
    public ByteArrayInputStream exportBeautifulPdf(ItineraryInfoEntity infoEntity,
                                                   List<ItineraryDayEntity> dayEntityList,
                                                   Map<LocalDate, List<ItineraryTransportationVo>> tpData) {

        // 查询处理城市数据
        String cityCover = null;
        Integer departureCityId = infoEntity.getDepartureCityId();
        List<Integer> destinationCityIds = infoEntity.getDestinationCityIds();
        if (departureCityId != null) {
            destinationCityIds.add(departureCityId);
        }
        List<GlobalCityEntity> cityEntityList = globalCityService.queryEntityListByIds(destinationCityIds);
        BeautifulPdfCityDto departureCity = null;
        List<BeautifulPdfCityDto> destinationCityList = new ArrayList<>();
        if (CollUtil.isNotEmpty(cityEntityList)) {
            destinationCityList = cityEntityList.stream().map(BeautifulPdfCityDto::new)
                    .collect(Collectors.toCollection(ArrayList::new));
            if (departureCityId != null) {
                departureCity = destinationCityList.remove(cityEntityList.size() - 1);
            }

            // 封面
            GlobalCityEntity firstDestination = cityEntityList.get(0);
            String logoPic = firstDestination.getLogoPic();
            if (StrUtil.isNotBlank(logoPic)) {
                cityCover = tencentCosService.getUrl("scenic", logoPic);
            }
        }

        // 封装导出数据实体
        BeautifulPdfDto beautifulPdfDto = new BeautifulPdfDto();

        // 获取攻略信息
        BeautifulPdfDto.ItineraryInfo itineraryInfo = getItineraryInfoFromInfoEntity(infoEntity, destinationCityList);
        // 封面
        itineraryInfo.setCover(cityCover);
        beautifulPdfDto.setItineraryInfo(itineraryInfo);

        // 处理天数据
        List<BeautifulPdfDto.DayInfo> dayInfos
                = convertDayEntity(dayEntityList, departureCity, destinationCityList, tpData);
        beautifulPdfDto.setDayInfos(dayInfos);

        // 出行清单
        String checklistConfig = configService.getConfigValue(ConfigEnum.ITINERARY_BEAUTIFUL_PDF_CHECKLIST);
        List<BeautifulPdfDto.ChecklistType> checkListTypes
                = JSON.parseArray(checklistConfig, BeautifulPdfDto.ChecklistType.class);
        beautifulPdfDto.setCheckListTypes(checkListTypes);

        // 生成pdf
        try {
            log.info("Start generate beautiful-pdf");
            long start = System.currentTimeMillis();
            ByteArrayInputStream byteArrayInputStream = generatePdf(beautifulPdfDto);
            long end = System.currentTimeMillis();
            log.info("Over generate beautiful-pdf, Execute Time: {}", end - start);
            return byteArrayInputStream;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return null;
    }

    /**
     * 根据攻略信息Entity获取pdf攻略信息
     *
     * @param infoEntity          攻略信息Entity
     * @param destinationCityList 目的地城市实体列表
     * @return com.somytrip.entity.itinerary.BeautifulPdfDto.ItineraryInfo
     * <AUTHOR>
     * @date 2025/1/7 11:19
     */
    private BeautifulPdfDto.ItineraryInfo getItineraryInfoFromInfoEntity(ItineraryInfoEntity infoEntity,
                                                                         List<BeautifulPdfCityDto> destinationCityList) {

        Locale locale = LocaleContextHolder.getLocale();

        BeautifulPdfDto.ItineraryInfo itineraryInfo = new BeautifulPdfDto.ItineraryInfo();
        // 标题
        itineraryInfo.setTitle(infoEntity.getItineraryTitle());
        // 天数
        List<String> daysAndCountDescList = new ArrayList<>();
        String days = BeautifulLocaleUtil.formatDays(infoEntity.getDays(), locale);
        daysAndCountDescList.add(days);
        // 出发日期
        LocalDateTime goTime = infoEntity.getGoTime();
        if (goTime != null) {
            String departureDate = BeautifulLocaleUtil.formatDepartureDate(goTime.toLocalDate(), locale);
            itineraryInfo.setDepartureDate(departureDate);
        }
        // 国家、城市数量
        if (CollUtil.isNotEmpty(destinationCityList)) {
            // 国家数量
            Map<Integer, List<BeautifulPdfCityDto>> groupByCountry = destinationCityList.stream()
                    .collect(Collectors.groupingBy(BeautifulPdfCityDto::getCountryId));
            String countryCount = BeautifulLocaleUtil.formatCountryCount(groupByCountry.keySet().size(), locale);
            daysAndCountDescList.add(countryCount);
            // 城市数量
            String cityCount = BeautifulLocaleUtil.formatCityCount(destinationCityList.size(), locale);
            daysAndCountDescList.add(cityCount);
        }
        itineraryInfo.setDaysAndCountDesc(StrUtil.join("，", daysAndCountDescList));


        return itineraryInfo;
    }

    /**
     * 转换天数据
     *
     * @param dayEntityList       天实体列表
     * @param departureCity       出发城市实体
     * @param destinationCityList 目的地城市实体列表
     * @param tpData              交通数据
     * @return java.util.List<com.somytrip.entity.itinerary.BeautifulPdfDto.DayInfo>
     * <AUTHOR>
     * @date 2025/1/7 11:42
     */
    private List<BeautifulPdfDto.DayInfo> convertDayEntity(List<ItineraryDayEntity> dayEntityList,
                                                           BeautifulPdfCityDto departureCity,
                                                           List<BeautifulPdfCityDto> destinationCityList,
                                                           Map<LocalDate, List<ItineraryTransportationVo>> tpData) {

        List<BeautifulPdfDto.DayInfo> result = new ArrayList<>();
        Locale locale = LocaleContextHolder.getLocale();

        BeautifulPdfCityDto lastCity = null;
        for (int i = 0; i < dayEntityList.size(); i++) {
            ItineraryDayEntity dayEntity = dayEntityList.get(i);
            BeautifulPdfDto.DayInfo dayInfo = new BeautifulPdfDto.DayInfo();

            // 第n天
            dayInfo.setDayNum(String.format("%02d", dayEntity.getDayNum()));
            // 日期
            LocalDate date = dayEntity.getDate();
            String dateDesc = BeautifulLocaleUtil.formatDate(date, locale);
            dayInfo.setDate(dateDesc);
            // 周
            String week = dayEntity.getDate().getDayOfWeek().getDisplayName(TextStyle.FULL, locale);
            dayInfo.setWeek(week);
            // 城市
            String cityCode = dayEntity.getCityCode();
            BeautifulPdfCityDto curCity = destinationCityList.stream()
                    .filter(item -> Objects.equals(cityCode, item.getCityCode()))
                    .findFirst().orElse(new BeautifulPdfCityDto());
            List<BeautifulPdfCityDto> cities = new ArrayList<>();
            if (i == 0) {
                // 第一天 出发地-第一目的地
                cities = new ArrayList<>(List.of(departureCity, curCity));
            } else if (i == dayEntityList.size() - 1) {
                cities = new ArrayList<>(List.of(curCity, departureCity));
            } else {
                if (!Objects.equals(lastCity, curCity)) {
                    // 跨城市
                    cities.add(lastCity);
                }
                cities.add(curCity);
            }
            lastCity = curCity;
            dayInfo.setCities(cities);

            List<ItineraryDayEntity.ActivityEntity> activityEntityList = dayEntity.getActivities();

            // 酒店(住宿)
            ItineraryDayEntity.ActivityEntity hotel;
            Optional<ItineraryDayEntity.ActivityEntity> hotelOpt = activityEntityList.stream()
                    .filter(item -> Objects.equals(3, item.getType()))
                    .reduce((first, second) -> second);
            if (hotelOpt.isPresent()) {
                hotel = hotelOpt.get();
                HotelGeneralEntity hotelEntity = hotelGeneralService.queryOneByHotelId(
                        hotel.getActivityId(), HotelOrigin.getOriginFromName(hotel.getActivityOrigin())
                );
                if (hotelEntity != null) {
                    BeautifulPdfDto.Activity hotelActivity = new BeautifulPdfDto.Activity(hotelEntity);
                    dayInfo.setHotel(hotelActivity);
                }
            }

            // 活动列表
            List<BeautifulPdfDto.Activity> activities = new ArrayList<>();
            for (ItineraryDayEntity.ActivityEntity activityEntity : activityEntityList) {
                ItineraryActivityType activityType = ItineraryActivityType.fromValue(activityEntity.getType());
                BeautifulPdfDto.Activity activity = null;
                switch (activityType) {
                    case SCENIC -> {
                        Scenic scenicEntity = scenicService.getById(activityEntity.getActivityId());
                        if (scenicEntity == null) {
                            continue;
                        }
                        activity = new BeautifulPdfDto.Activity(scenicEntity);
                    }
                    case HOTEL -> {
                        HotelGeneralEntity hotelEntity = hotelGeneralService.queryOneByHotelId(
                                activityEntity.getActivityId(),
                                HotelOrigin.getOriginFromName(activityEntity.getActivityOrigin())
                        );
                        if (hotelEntity == null) {
                            continue;
                        }
                        activity = new BeautifulPdfDto.Activity(hotelEntity);
                    }
                    case RESTAURANT -> activity = new BeautifulPdfDto.Activity(activityEntity);
                    default -> {
                    }
                }
                if (activity == null) {
                    continue;
                }

                activity.setDistanceToNext(activityEntity.getDistanceToNext());
                activities.add(activity);
            }
            dayInfo.setActivities(activities);
            // 交通
            if (tpData != null) {
                List<ItineraryTransportationVo> curDateTp = tpData.get(date);
                if (CollUtil.isNotEmpty(curDateTp)) {
                    List<BeautifulPdfDto.Transportation> tp = processTpData(curDateTp, cities);
                    dayInfo.setTp(tp);
                }
            }

            result.add(dayInfo);
        }

        return result;
    }

    /**
     * 处理交通数据
     *
     * @param tpData 交通原数据
     * @param cities 城市列表
     * @return java.util.List<com.somytrip.entity.itinerary.BeautifulPdfDto.Transportation>
     * <AUTHOR>
     * @date 2025/1/16 10:51
     */
    private List<BeautifulPdfDto.Transportation> processTpData(List<ItineraryTransportationVo> tpData,
                                                               List<BeautifulPdfCityDto> cities) {

        List<BeautifulPdfDto.Transportation> result = new ArrayList<>();

        tpData = JSONArray.from(tpData).toJavaList(ItineraryTransportationVo.class);
        for (ItineraryTransportationVo tpVo : tpData) {
            BeautifulPdfDto.Transportation transportation = new BeautifulPdfDto.Transportation();

            // 班次
            transportation.setName(tpVo.getName());

            // 出发站
            transportation.setDeparture(tpVo.getDeparture());
            transportation.setDepartureTime(tpVo.getDepartureTime().toLocalTime().toString());

            // 到达站
            transportation.setDestination(tpVo.getArrival());
            transportation.setArrivalTime(tpVo.getArrivalTime().toLocalTime().toString());

            // 城市
            transportation.setDepartureCity(CollUtil.getFirst(cities));
            transportation.setArrivalCity(CollUtil.getLast(cities));

            result.add(transportation);
        }

        return result;
    }

    /**
     * 生成pdf
     *
     * @param dto 数据dto
     * @return java.io.ByteArrayInputStream
     * <AUTHOR>
     * @date 2025/1/8 10:45
     */
    private ByteArrayInputStream generatePdf(BeautifulPdfDto dto) throws Exception {

        // 生成HTML
        String htmlContent = generateHtmlFromExcel(dto);

        // 将HTML转为PDF
        return convertHtmlToPdf(htmlContent);
    }

    /**
     * 生成html
     *
     * @param dto 数据dto
     * @return java.lang.String
     * <AUTHOR>
     * @date 2025/1/8 10:33
     */
    private String generateHtmlFromExcel(BeautifulPdfDto dto) {

        // 多语言
        Locale locale = LocaleContextHolder.getLocale();

        // 攻略信息
        BeautifulPdfDto.ItineraryInfo itineraryInfo = dto.getItineraryInfo();
        // 每日数据
        List<BeautifulPdfDto.DayInfo> dayInfos = dto.getDayInfos();
        // 出行清单
        List<BeautifulPdfDto.ChecklistType> checkListTypes = dto.getCheckListTypes();
        // 总览表格行下标
        int index = 0;

        String cityCover = itineraryInfo.getCover();
        String cover = "https://minio.somytrip.com/static/beautiful-pdf/default_cover.jpg";
        cover = StrUtil.isNotBlank(cityCover) ? cityCover : cover;
        String coverLogo = "https://minio.somytrip.com/static/beautiful-pdf/cover_logo.png";

        StringBuilder htmlSb = new StringBuilder();
        htmlSb.append("<!DOCTYPE html><html><head>");
        // 获取css样式内容
        String styleSb = getStyleScript();
        htmlSb.append(styleSb);
        htmlSb.append("</head><body>");

        /* =============================== 标题[开始] =============================== */
        htmlSb.append("<div class=\"header\">");
        String title = itineraryInfo.getTitle();
        if (title.length() > 20) {
            title = title.substring(0, 19) + "...";
        }
        htmlSb.append("<div class=\"title\">").append(title).append("</div>");
        htmlSb.append("<div class=\"sub-title grey-text\">")
                .append(itineraryInfo.getDepartureDate())
                .append(" &nbsp;|&nbsp; ")
                .append(itineraryInfo.getDaysAndCountDesc())
                .append("</div>");
        htmlSb.append("<div class=\"author grey-text\">");
        String author = itineraryInfo.getAuthor();
        if (StrUtil.isNotBlank(author)) {
            htmlSb.append(BeautifulLocaleUtil.formatAuthor(locale)).append(": ").append(author);
        }
        htmlSb.append("</div>");
        htmlSb.append("</div>");
        /* =============================== 标题[结束] =============================== */

        /* =============================== 封面图[开始] =============================== */
        htmlSb.append("<div class=\"cover\"><img src=\"").append(cover).append("\"/></div>");
        /* =============================== 封面图[结束] =============================== */

        /* =============================== 封面logo[开始] =============================== */
        htmlSb.append("<div class=\"cover-logo\">").append("<div class=\"logo-img\">");
        htmlSb.append("<img src=\"").append(coverLogo).append("\">").append("</div>");
        htmlSb.append("<div class=\"title\">");
        htmlSb.append("<div class=\"main-title\">").append("花得少 . 玩更好").append("</div>");
        htmlSb.append("</div>");
        htmlSb.append("</div>");
        /* =============================== 封面logo[结束] =============================== */

        /* =============================== 总览表格[开始] =============================== */
        htmlSb.append("<div class=\"overview\"><table><thead><tr>");
        // 获取多语言表头名
        String overviewThNameDate = BeautifulLocaleUtil.formatOverviewThNameDate(locale);
        String overviewThNameCity = BeautifulLocaleUtil.formatOverviewThNameCity(locale);
        String overviewThNameTp = BeautifulLocaleUtil.formatOverviewThNameTp(locale);
        String overviewThNameActivity = BeautifulLocaleUtil.formatOverviewThNameActivity(locale);
        String overviewThNameHotel = BeautifulLocaleUtil.formatOverviewThNameHotel(locale);
        htmlSb.append("<th class=\"date\">").append(overviewThNameDate).append("</th>");
        htmlSb.append("<th class=\"city\">").append(overviewThNameCity).append("</th>");
        htmlSb.append("<th class=\"tp\">").append(overviewThNameTp).append("</th>");
        htmlSb.append("<th class=\"activity\">").append(overviewThNameActivity).append("</th>");
        htmlSb.append("<th class=\"hotel\">").append(overviewThNameHotel).append("</th>");
        htmlSb.append("</tr></thead>");
        htmlSb.append("<tbody>");
        // 遍历每日数据
        for (BeautifulPdfDto.DayInfo dayInfo : dayInfos) {
            if (index++ % 2 == 0) {
                htmlSb.append("<tr class=\"bg-high-light\">");
            } else {
                htmlSb.append("<tr>");
            }
            // 日期td
            htmlSb.append("<td class=\"border-right\">");
            htmlSb.append("<div class=\"date-cell\">");
            htmlSb.append("<div class=\"day-num\">").append(dayInfo.getDayNum()).append("</div>");
            htmlSb.append("<div class=\"week-date\">").append(dayInfo.getWeek())
                    .append("<br/>").append(dayInfo.getDate()).append("</div>");
            htmlSb.append("</div></td>");
            // 城市td
            htmlSb.append("<td class=\"border-right\">");
            List<BeautifulPdfCityDto> cities = dayInfo.getCities();
            if (CollUtil.isNotEmpty(cities)) {
                for (BeautifulPdfCityDto city : cities) {
                    htmlSb.append("<div class=\"city-cell\">").append(city.getCityNameCn())
                            .append("<br/><span class=\"grey-text\">").append(city.getCityNameEn()).append("</span></div>");
                }
            }
            htmlSb.append("</td>");
            // 交通td
            htmlSb.append("<td class=\"border-right\">");
            List<BeautifulPdfDto.Transportation> tpData = dayInfo.getTp();
            htmlSb.append("<div class=\"tp-cell\">");
            if (CollUtil.isNotEmpty(tpData)) {
                for (BeautifulPdfDto.Transportation tp : tpData) {
                    htmlSb.append(tp.getDepartureTime()).append(" - ").append(tp.getArrivalTime())
                            .append("<br/>").append(tp.getDepartureCity().getCityNameCn())
                            .append(" - ").append(tp.getArrivalCity().getCityNameCn());
                }
            }
            htmlSb.append("</div>");
            htmlSb.append("</td>");
            // 景点td
            htmlSb.append("<td class=\"border-right\">");
            List<BeautifulPdfDto.Activity> activities = dayInfo.getActivities();
            htmlSb.append("<div class=\"activity-cell\">");
            if (CollUtil.isNotEmpty(activities)) {
                for (int i = 0; i < activities.size(); i++) {
                    BeautifulPdfDto.Activity activity = activities.get(i);
                    htmlSb.append("<div>").append(i + 1).append(".").append(activity.getName());
                    if (StrUtil.isNotBlank(activity.getNameEn())) {
                        htmlSb.append("，").append("<span class=\"grey-text\">").append(activity.getNameEn()).append("</span>");
                    }
                    htmlSb.append("</div>");
                }
            }
            htmlSb.append("</div>");
            htmlSb.append("</td>");
            // 住宿td
            htmlSb.append("<td>");
            htmlSb.append("<div class=\"hotel-cell\">");
            BeautifulPdfDto.Activity hotel = dayInfo.getHotel();
            if (hotel != null) {
                htmlSb.append(hotel.getName())
                        .append("<br/><span class=\"grey-text\">地址：")
                        .append(hotel.getAddress()).append("</span>");
            }
            htmlSb.append("</div>");
            htmlSb.append("</td>");
            htmlSb.append("</tr>");
        }
        htmlSb.append("</tbody>");
        htmlSb.append("</table></div>");
        /* =============================== 总览表格[结束] =============================== */

        /* =============================== 每日列表[开始] =============================== */
        htmlSb.append("<div class=\"day-list\">");
        for (int i = 0; i < dayInfos.size(); i++) {
            BeautifulPdfDto.DayInfo dayInfo = dayInfos.get(i);
            htmlSb.append("<div class=\"day-box\">");
            // 天头部
            htmlSb.append("<div class=\"day-header\">");
            htmlSb.append("<div class=\"day-date\">");
            htmlSb.append("<div class=\"day\">DAY").append(i + 1).append("</div>");
            htmlSb.append("<div class=\"date\">").append(dayInfo.getDate()).append(" ")
                    .append(dayInfo.getWeek()).append("</div>");
            htmlSb.append("</div>");
            List<BeautifulPdfCityDto> cities = dayInfo.getCities();
            if (CollUtil.isNotEmpty(cities)) {
                List<String> cityList = new ArrayList<>();
                for (BeautifulPdfCityDto city : cities) {
                    cityList.add(city.getCityNameCn() + " " + city.getCityNameEn());
                }
                htmlSb.append("<div class=\"city\">").append(String.join("，", cityList)).append("</div>");
            }
            htmlSb.append("</div>");

            // 交通
            List<BeautifulPdfDto.Transportation> tpData = dayInfo.getTp();
            if (CollUtil.isNotEmpty(tpData)) {
                htmlSb.append("<div class=\"tp\">");
                // 交通城市
                htmlSb.append("<div class=\"cities-box\">");
                htmlSb.append("<div class=\"circle-tp\"></div>");
                List<String> tpCityList = new ArrayList<>(List.of(tpData.get(0).getDepartureCity().getCityNameCn()));
                for (BeautifulPdfDto.Transportation tp : tpData) {
                    tpCityList.add(tp.getArrivalCity().getCityNameCn());
                }
                htmlSb.append("<div class=\"cities\">").append(String.join(" / ", tpCityList)).append("</div>");
                htmlSb.append("</div>");
                htmlSb.append("<div class=\"line\"></div>");

                // 交通表格
                htmlSb.append("<table class=\"tb\">").append("<thead>").append("<tr>");
                String tpThNameDepartureTime = BeautifulLocaleUtil.formatTpThNameDepartureTime(locale);
                String tpThNameName = BeautifulLocaleUtil.formatTpThNameName(locale);
                String tpThNameDeparture = BeautifulLocaleUtil.formatTpThNameDeparture(locale);
                String tpThNameDestination = BeautifulLocaleUtil.formatTpThNameDestination(locale);
                String tpThNameArrivalTime = BeautifulLocaleUtil.formatTpThNameArrivalTime(locale);
                htmlSb.append("<th class=\"departure-time\">").append(tpThNameDepartureTime).append("</th>");
                htmlSb.append("<th class=\"name\">").append(tpThNameName).append("</th>");
                htmlSb.append("<th class=\"departure\">").append(tpThNameDeparture).append("</th>");
                htmlSb.append("<th class=\"destination\">").append(tpThNameDestination).append("</th>");
                htmlSb.append("<th class=\"arrival-time\">").append(tpThNameArrivalTime).append("</th>");
                htmlSb.append("</tr>").append("</thead>");
                htmlSb.append("<tbody>");
                for (BeautifulPdfDto.Transportation tp : tpData) {
                    htmlSb.append("<tr>");
                    htmlSb.append("<td>").append(StrUtil.nullToEmpty(tp.getDepartureTime())).append("</td>");
                    htmlSb.append("<td>").append(StrUtil.nullToEmpty(tp.getName())).append("</td>");
                    htmlSb.append("<td>").append(StrUtil.nullToEmpty(tp.getDeparture())).append("</td>");
                    htmlSb.append("<td>").append(StrUtil.nullToEmpty(tp.getDestination())).append("</td>");
                    htmlSb.append("<td>").append(StrUtil.nullToEmpty(tp.getArrivalTime())).append("</td>");
                    htmlSb.append("</tr>");
                }
                htmlSb.append("</tbody>");
                htmlSb.append("</table>");
                htmlSb.append("<div class=\"line\"></div>");
                htmlSb.append("</div>");
            }

            /* 活动列表[开始] */
            List<BeautifulPdfDto.Activity> activities = dayInfo.getActivities();
            if (CollUtil.isNotEmpty(activities)) {
                for (BeautifulPdfDto.Activity activity : activities) {
                    ItineraryActivityType activityType = ItineraryActivityType.fromValue(activity.getType());

                    htmlSb.append("<div class=\"activity-box\">");
                    htmlSb.append("<div class=\"activity\">");
                    // 名称
                    htmlSb.append("<div class=\"name-box\">");
                    if (Objects.equals(activityType, ItineraryActivityType.SCENIC)) {
                        htmlSb.append("<div class=\"circle-scenic\"></div>");
                    } else if (Objects.equals(activityType, ItineraryActivityType.RESTAURANT)) {
                        htmlSb.append("<div class=\"circle-restaurant\"></div>");
                    } else if (Objects.equals(activityType, ItineraryActivityType.HOTEL)) {
                        htmlSb.append("<div class=\"circle-hotel\"></div>");
                    }
                    StringBuilder activityNameSb = new StringBuilder();
                    activityNameSb.append(activity.getName());
                    if (StrUtil.isNotBlank(activity.getNameEn())) {
                        activityNameSb.append("，").append(activity.getNameEn());
                    }
                    htmlSb.append("<div class=\"name\">").append(activityNameSb).append("</div>");
                    htmlSb.append("</div>");
                    htmlSb.append("<div class=\"tags\">").append(StrUtil.nullToEmpty(activity.getTags())).append("</div>");
                    // 获取多语言属性标题
                    String attrTitleAddress = BeautifulLocaleUtil.formatAttrTitleAddress(locale);
                    String attrTitleBusinessHours = BeautifulLocaleUtil.formatAttrTitleBusinessHours(locale);
                    String attrTitlePrice = BeautifulLocaleUtil.formatAttrTitlePrice(locale);
                    String attrTitleIntroduction = BeautifulLocaleUtil.formatAttrTitleIntroduction(locale);
                    // 地址
                    String address = activity.getAddress();
                    if (StrUtil.isNotBlank(address)) {
                        htmlSb.append("<div class=\"attr-box\">");
                        htmlSb.append("<div class=\"title\">").append(attrTitleAddress).append("</div>");
                        htmlSb.append("<div class=\"value\">").append(address).append("</div>");
                        htmlSb.append("</div>");
                    }
                    if (!Objects.equals(activityType, ItineraryActivityType.HOTEL)) {
                        // 时间
                        String businessHours = activity.getBusinessHours();
                        if (StrUtil.isNotBlank(businessHours)) {
                            htmlSb.append("<div class=\"attr-box\">");
                            htmlSb.append("<div class=\"title\">").append(attrTitleBusinessHours).append("</div>");
                            htmlSb.append("<div class=\"value\">").append(businessHours).append("</div>");
                            htmlSb.append("</div>");
                        }
                        // 票价
                        String priceDesc = activity.getPriceDesc();
                        if (StrUtil.isNotBlank(priceDesc)) {
                            htmlSb.append("<div class=\"attr-box\">");
                            htmlSb.append("<div class=\"title\">").append(attrTitlePrice).append("</div>");
                            htmlSb.append("<div class=\"value\">").append(priceDesc).append("</div>");
                            htmlSb.append("</div>");
                        }
                    }
                    // 介绍
                    String introduction = activity.getIntroduction();
                    if (StrUtil.isNotBlank(introduction)) {
                        htmlSb.append("<div class=\"attr-box\">");
                        htmlSb.append("<div class=\"title\">").append(attrTitleIntroduction).append("</div>");
                        htmlSb.append("<div class=\"value\">").append(introduction).append("</div>");
                        htmlSb.append("</div>");
                    }
                    htmlSb.append("</div>");
                    // 图片
                    htmlSb.append("<div class=\"activity-img\">");
                    String activityCover = activity.getCover();
                    if (StringUtils.isNotBlank(activityCover)) {
                        if (!activityCover.startsWith("http")) {
                            activityCover = ACTIVITY_COVER_URL_PREFIX + activityCover;
                        }
                        htmlSb.append("<img src=\"").append(activityCover).append("\"/>");
                    }
                    htmlSb.append("</div>");
                    htmlSb.append("</div>");

                    String distanceToNext = activity.getDistanceToNext();
                    if (StrUtil.isNotBlank(distanceToNext)) {
                        htmlSb.append("<div class=\"distance\"><span>").append(distanceToNext).append("</span></div>");
                    }
                }
            }
            /* 活动列表[结束] */

            htmlSb.append("</div>");
        }
        htmlSb.append("</div>");
        /* =============================== 每日列表[结束] =============================== */

        /* =============================== 出行清单[开始] =============================== */
        if (CollUtil.isNotEmpty(checkListTypes)) {
            htmlSb.append("<div class=\"checklist\">");
            String checkListTitle = BeautifulLocaleUtil.formatCheckListTitle(locale);
            htmlSb.append("<div class=\"title\">").append(checkListTitle).append("</div>");
            htmlSb.append("<div class=\"types\">");
            for (BeautifulPdfDto.ChecklistType checkListType : checkListTypes) {
                List<BeautifulPdfDto.ChecklistItem> items = checkListType.getItems();
                if (CollUtil.isEmpty(items)) {
                    continue;
                }
                htmlSb.append("<div class=\"type-box\">");
                htmlSb.append("<div class=\"type-name\">").append(checkListType.getType()).append("</div>");
                htmlSb.append("<table class=\"checklist-tb\">");
                htmlSb.append("<thead><tr>");
                htmlSb.append("<th class=\"name\">名称</th>");
                htmlSb.append("<th class=\"num\">数量</th>");
                htmlSb.append("<th class=\"ok\">OK</th>");
                htmlSb.append("</tr></thead>");
                htmlSb.append("<tbody>");
                for (BeautifulPdfDto.ChecklistItem item : items) {
                    htmlSb.append("<tr>");
                    htmlSb.append("<td>").append(item.getName()).append("</td>")
                            .append("<td></td>").append("<td></td>");
                    htmlSb.append("</tr>");
                }
                htmlSb.append("</tbody>");
                htmlSb.append("</table>");
                htmlSb.append("</div>");
            }
            htmlSb.append("</div>");
            htmlSb.append("</div>");
        }
        /* =============================== 出行清单[结束] =============================== */

        htmlSb.append("</body></html>");
        return htmlSb.toString();
    }

    /**
     * 获取<style>样式内容
     *
     * @return java.lang.String
     * <AUTHOR>
     * @date 2025/1/15 14:22
     */
    private String getStyleScript() {

        String footerLogo = "https://minio.somytrip.com/static/beautiful-pdf/footer_logo.png";
        return "<style>" +
                "body {width: 100%;} " +
                ".header {padding: 50px 30px;} " +
                ".header .title {font-size: 60px;} " +
                ".header .sub-title {margin-top: 8px; font-size: 14px;} " +
                ".header .author {margin-top: 6px; font-size: 14px;} " +
                ".header .grey-text {color: #696969;} " +
                ".cover {width: 100%; height: 465px; overflow: hidden; margin-top: 120px;} " +
                ".cover img {width: 100%;}" +
                ".cover-logo {display: flex; margin: 60px 0 0 4px;} " +
                ".cover-logo .logo-img img {width: 40px; margin-right: 10px;} " +
                ".cover-logo .title {display: flex; flex-direction: column; justify-content: center; align-items: center;} " +
                ".cover-logo .title .main-title {font-size: 18px; font-weight: 900; color: #898989;} " +
                ".cover-logo .title .sub-title {font-size: 12px;}" +
                ".overview {width: 100%; page-break-before: always; page-break-after: always;} " +
                ".overview table {border-spacing: 0;} " +
                ".overview thead {color: white; font-size: 12px;} " +
                ".overview thead th {padding: 6px; height: 12px; background-color: #646464; text-align: left;} " +
                ".overview thead .date {width: 140px;} " +
                ".overview thead .city {width: 70px;} " +
                ".overview thead .tp {width: 120px;} " +
                ".overview thead .activity {width: 200px;} " +
                ".overview thead .hotel {width: 110px;} " +
                ".overview tbody .bg-high-light td {background-color: #f7f7f7;} " +
                ".overview tbody .border-right {border-right: 1px solid #a9a9a9;} " +
                ".overview tbody .border-left {border-left: 1px solid #a9a9a9;} " +
                ".overview .grey-text {color: #696969;}" +
                ".overview td {padding: 4px}" +
                ".overview tbody {font-size: 15px;} " +
                ".overview .date-cell {width: 140px; display: flex; align-items: center;} " +
                ".overview .day-num {margin-right: 4px; font-size: 36px; font-weight: 500;} " +
                ".overview .week-date {font-size: 12px; font-weight: normal;} " +
                ".overview .city-cell {width: 55px; font-size: 12px;} " +
                ".overview .tp-cell {width: 100px; font-size: 12px;} " +
                ".overview .activity-cell {width: 170px; font-size: 12px;} " +
                ".overview .hotel-cell {width: 110px; font-size: 12px;}" +
                ".day-box .day-header {width: 100%; display: flex; margin: 30px 0;} " +
                ".day-box .day-header .day-date {width: 20%;} " +
                ".day-box .day-header .day {height: 44px; font-size: 36px; font-weight: 500;} " +
                ".day-box .day-header .date {font-size: 12px;} " +
                ".day-box .day-header .city {width: 80%; font-size: 12px; font-weight: 500; text-align: right;}" +
                ".day-box .tp {margin: 30px 0 80px 0;} " +
                ".day-box .tp .cities-box {margin: 8px auto; display: flex; line-height: 25px;} " +
                ".day-box .tp .cities {font-weight: 500; font-size: 14px;} " +
                ".day-box .tp .tb {width: 100%; margin-top: 10px;} " +
                ".day-box .tp .tb thead th {text-align: left; font-size: 10px; font-weight: normal;} " +
                ".day-box .tp .tb thead .departure-time {width: 60px;} " +
                ".day-box .tp .tb thead .name {width: 60px;} " +
                ".day-box .tp .tb thead .departure {width: 200px;} " +
                ".day-box .tp .tb thead .destination {width: 200px;} " +
                ".day-box .tp .tb thead .arrival-time {width: 120px;} " +
                ".day-box .tp .tb tbody td {height: 40px; font-size: 14px; font-weight: 600;} " +
                ".day-box .line {height: 1px; background-color: black;}" +
                ".activity-box {width: 100%; display: flex; margin-bottom: 60px;} " +
                ".activity-box .activity {width: 60%;} " +
                ".activity-box .activity-img {width: 40%; text-align: right;} " +
                ".activity-box .activity-img img {width: 100%;} " +
                ".activity .name-box {display: flex; width: 94%; margin: 4px auto 4px 0; line-height: 25px;} " +
                ".activity .name-box .name {font-size: 14px; font-weight: 500;} " +
                ".activity .tags {margin-left: 42px; font-size: 12px;} " +
                ".activity .attr-box {display: flex; width: 100%; margin: 12px 0 0 6px;} " +
                ".activity .attr-box .title {width: 14%; font-size: 12px; font-weight: bold; word-break: break-all; word-wrap: break-word;} " +
                ".activity .attr-box .value {width: 76%; padding-left: 4px; font-size: 12px; font-weight: 400;}" +
                ".day-box .distance {height: 60px; line-height: 60px; margin-bottom: 60px; padding-left: 60px; font-size: 10px; background-color: #f5f5f5;} " +
                ".day-box .circle-tp {width: 25px; height: 25px; margin: auto 6px; border-radius: 50%; background-color: #00a9f0; page-break-inside: avoid;} " +
                ".day-box .circle-hotel {width: 25px; height: 25px; margin: auto 6px; border-radius: 50%; background-color: #f68b2c; flex-shrink: 0; page-break-inside: avoid;} " +
                ".day-box .circle-scenic {width: 25px; height: 25px; margin: auto 6px; border-radius: 50%; background-color: #20c179; flex-shrink: 0; page-break-inside: avoid;}" +
                ".day-box .circle-restaurant {width: 25px; height: 25px; margin: auto 6px; border-radius: 50%; background-color: #f1c355; flex-shrink: 0; page-break-inside: avoid;}" +
                ".checklist {width: 100%; page-break-before: always;} " +
                ".checklist .title {margin: 20px 0 40px 0; text-align: center; font-size: 18px; font-weight: 500;} " +
                ".checklist .types {column-count: 2; column-gap: 20px; overflow: hidden;} " +
                ".checklist .type-box {width: 100%; margin-bottom: 20px;} " +
                ".type-box .type-name {height: 40px; line-height: 40px; background-color: #646464; font-size: 14px; color: white; text-align: center;} " +
                ".checklist-tb {width: 100%; border-spacing: 0;} " +
                ".checklist-tb th {text-align: left; padding: 12px; font-size: 14px; font-weight: 500; background-color: #f0f0f0;} " +
                ".checklist-tb thead .name {width: 60%;} " +
                ".checklist-tb thead .num {width: 20%;} " +
                ".checklist-tb thead .ok {width: 20%;} " +
                ".checklist-tb tbody td {padding: 12px; font-size: 14px; border: 1px solid #f0f0f0;}" +
                "@page { " +
                "    margin-bottom: 2cm;" +
                "    @bottom-center {" +
                "        content: \"P\"counter(page);" +
                "        background-image: url(" + footerLogo + ");" +
                "        background-repeat: no-repeat;" +
                "        background-position: left center;" +
                "        text-align: right;" +
                "        color: #696969;" +
                "        }" +
                "}" +
                "@page :first { " +
                "    @bottom-center { " +
                "        content: normal; " +
                "    } " +
                "}" +
                "</style>";
    }

    /**
     * 将html转换为PDF
     *
     * @param htmlContent html内容
     * @return java.io.ByteArrayInputStream
     * <AUTHOR>
     * @date 2024/12/26 10:43
     */
    private ByteArrayInputStream convertHtmlToPdf(String htmlContent) throws IOException {

        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();

        // 初始化 PdfWriter 和 PdfDocument
        PdfWriter writer = new PdfWriter(byteArrayOutputStream);

        PdfDocument pdf = new PdfDocument(writer);
        pdf.setDefaultPageSize(PageSize.A4);

        // 创建并配置 ConverterProperties
        ConverterProperties properties = new ConverterProperties();
        // 注册字体
        FontProvider fontProvider = new FontProvider();
        // 读取字体文件
        Path normalFonPath = Paths.get(resourceLoader.getResource("classpath:fonts/SourceHanSansCN-Normal.ttf").getURI());
        Path boldFontPath = Paths.get(resourceLoader.getResource("classpath:fonts/SourceHanSansCN-Bold.ttf").getURI());
        Path mediumFontPath = Paths.get(resourceLoader.getResource("classpath:fonts/SourceHanSansCN-Medium.ttf").getURI());
        byte[] normalFontBytes = Files.readAllBytes(normalFonPath);
        byte[] boldFontBytes = Files.readAllBytes(boldFontPath);
        byte[] mediumFontBytes = Files.readAllBytes(mediumFontPath);
        fontProvider.addFont(normalFontBytes);
        fontProvider.addFont(boldFontBytes);
        fontProvider.addFont(mediumFontBytes);
        properties.setFontProvider(fontProvider);

        // 设置 CSS 解析工厂以应用字体设置
        ICssApplierFactory cssApplierFactory = new DefaultCssApplierFactory();
        properties.setCssApplierFactory(cssApplierFactory);

        // 转换 HTML 到 PDF
        HtmlConverter.convertToPdf(htmlContent, pdf, properties);

        // 关闭 PDF 文档
        pdf.close();

        // 将 ByteArrayOutputStream 转换为 ByteArrayInputStream
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());

//        // FIXME 保存本地调试
//        tmpSave(byteArrayInputStream);

        // 关闭 ByteArrayOutputStream
        byteArrayOutputStream.close();

        return byteArrayInputStream;
    }

    private void tmpSave(ByteArrayInputStream bis) {

        Path path = Paths.get("C:\\Users\\<USER>\\Desktop\\beautiful_pdf.pdf");
        try {
            Files.copy(bis, path, StandardCopyOption.REPLACE_EXISTING);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        } finally {
            // 确保流在最后被关闭
            if (bis != null) {
                try {
                    bis.close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
    }
}
