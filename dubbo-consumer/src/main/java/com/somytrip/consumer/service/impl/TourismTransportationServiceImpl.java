package com.somytrip.consumer.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.somytrip.api.service.tourism.TourismTransportationService;
import com.somytrip.consumer.mapper.TourismTransportationMapper;
import com.somytrip.consumer.service.RedisService;
import com.somytrip.consumer.util.ConstantUtil;
import com.somytrip.entity.enums.RedisKey;
import com.somytrip.entity.tourism.TourismTransportationDto;
import com.somytrip.entity.tourism.TourismTransportationEntity;
import com.somytrip.exception.BusinessException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName: TourismTransportationServiceImpl
 * @Description: 攻略交通Service实现类
 * @Author: shadow
 * @Date: 2024/1/6 11:14
 */
@Slf4j
//@DubboService
@Service
public class TourismTransportationServiceImpl
        extends ServiceImpl<TourismTransportationMapper, TourismTransportationEntity>
        implements TourismTransportationService {
    @Resource
    private RedisService redisService;


    /**
     * 根据方案ID查询交通列表
     *
     * @param schemeId 方案ID
     * @return Item<TourismTransportationEntity> 交通列表
     */
    @Override
    public List<TourismTransportationEntity> queryTransportationBySchemeId(Long schemeId) {

        return baseMapper.queryListBySchemeId(schemeId);
    }

    /**
     * 根据攻略标识查询交通列表
     *
     * @param schemeId 方案ID
     * @param schemeSn 方案序列号
     * @param uid      用户ID
     * @return Item<TourismTransportationEntity> 交通列表
     */
    @Override
    public List<TourismTransportationEntity> queryTransportationByFlag(Long schemeId, String schemeSn, Long uid) {

        List<TourismTransportationEntity> transportationEntityList;

        try {
            // 查询交通列表
//            transportationEntityList = tourismTransportationService.queryTransportationBySchemeId(schemeEntity.getId());
            transportationEntityList = baseMapper.queryListBySchemeId(schemeId);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException("api.tourism.transportation-search-error");
        }

        // 数据库中未获取到交通 在redis中查询
        if (transportationEntityList == null || transportationEntityList.isEmpty()) {

            RedisKey flagRedisKey = RedisKey.TOURISM_TRANSPORTATION_FLAG;
            String transportationFlagKey = flagRedisKey.appendKey(schemeSn);
//            String transportationFlagKey = RedisKey.appendKey(rediskey.getMd5Key(), schemeSn);
            String transportationFlag = redisService.getString(transportationFlagKey);
            if (transportationFlag == null) {
                // 未找到交通标识
//                return Collections.emptyList();
                log.warn("transportation flag is null, schemeSn: {}", schemeSn);
                throw new BusinessException("api.tourism.transportation-no-flag");
            }
            RedisKey dataRedisKey = RedisKey.TOURISM_TRANSPORTATION_DATA;
            String transportationDataKey = dataRedisKey.appendKey(uid, transportationFlag);
            // 获取redis交通数据
            String transportationData = redisService.getString(transportationDataKey);
            if (transportationData == null) {
                log.warn("transportation data not found, schemeSn: {}, transportationFlag: {}",
                        schemeSn, transportationFlag);
                throw new BusinessException("api.tourism.transportation-no-data");
            }

            // FIXME: 交通统一封装
            List<TourismTransportationDto> redisList =
                    JSONArray.parseArray(transportationData, TourismTransportationDto.class);
            // 转换交通数据为交通数据Entity
            transportationEntityList = convertTransportationData2EntityList(schemeId, redisList);
        }

        return transportationEntityList;
    }

    /**
     * 交通数据转换为TourismTransportationEntity
     *
     * @param schemeId           方案ID
     * @param transportationData 交通数据
     * @return Item<TourismTransportationEntity>
     */
    private List<TourismTransportationEntity> convertTransportationData2EntityList(
            Long schemeId,
            List<TourismTransportationDto> transportationData
    ) {

        List<TourismTransportationEntity> list = new ArrayList<>();

        for (TourismTransportationDto item : transportationData) {

            if (StringUtils.isNotBlank(item.getErrMsg()) || StringUtils.isNotBlank(item.getErrCode())) {
                throw new BusinessException(Integer.parseInt(item.getErrCode()), item.getErrMsg());
            }

            Integer mode = item.getTransportationMode();

            // 交通数据
            JSONObject data = item.getTransportationData();
            for (Integer level : ConstantUtil.Tourism.LEVEL_VALUE_ARR) {
                // 提取指定等级
                JSONArray curData = null;
                if (data != null) {
                    curData = data.getJSONArray(ConstantUtil.Tourism.LEVEL_EN_NAME_MAP.get(level));
                }
                // 封装entity
                TourismTransportationEntity trainEntity = new TourismTransportationEntity();
                trainEntity.setSchemeId(schemeId);
                trainEntity.setLevel(level);
                trainEntity.setTransportationMode(mode);
                trainEntity.setTransportationContent(curData);
                // 保存数据
//                tourismTransportationService.save(trainEntity);
                baseMapper.insert(trainEntity);
                list.add(trainEntity);
            }
        }

        return list;
    }
}
