package com.somytrip.consumer.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.somytrip.api.service.itinerary.*;
import com.somytrip.api.service.tourism.GlobalCityService;
import com.somytrip.consumer.config.IDGenConfig;
import com.somytrip.consumer.mapper.ItineraryInfoMapper;
import com.somytrip.consumer.service.RedisService;
import com.somytrip.consumer.util.DistanceUtil;
import com.somytrip.consumer.util.ItineraryExcelUtil;
import com.somytrip.consumer.util.ItineraryUtil;
import com.somytrip.entity.dto.city.CityDto;
import com.somytrip.entity.dto.city.GlobalCityEntity;
import com.somytrip.entity.dto.hotel.PaginationDto;
import com.somytrip.entity.enums.RedisKey;
import com.somytrip.entity.enums.itinerary.*;
import com.somytrip.entity.itinerary.*;
import com.somytrip.entity.tourism.excel.ItineraryExcelActivityVo;
import com.somytrip.entity.tourism.excel.ItineraryExcelDayVo;
import com.somytrip.entity.vo.CitySimpleVo;
import com.somytrip.exception.BusinessException;
import com.somytrip.utils.LocalDateTimeUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.TextStyle;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @projectName: dubbo-somytrip
 * @package: com.somytrip.consumer.service.impl
 * @className: ItineraryInfoServiceImpl
 * @author: shadow
 * @description: 攻略信息Service实现类
 * @date: 2024/4/13 16:04
 * @version: 1.0
 */
@Slf4j
@Service
@DubboService
public class ItineraryInfoServiceImpl
        extends ServiceImpl<ItineraryInfoMapper, ItineraryInfoEntity>
        implements ItineraryInfoService {

    @Resource
    private IDGenConfig idGenConfig;
    @Resource
    private ItineraryDayService itineraryDayService;
    @Resource
    private GlobalCityService globalCityService;
    @Resource
    private RedisService redisService;
    @Resource
    private ItineraryDataService itineraryDataService;
    @Resource
    private ItineraryTransportationService itineraryTransportationService;
    @Resource
    private ItineraryGuidebookService itineraryGuidebookService;
    @Resource
    private ItineraryOperationLogService itineraryOperationLogService;

    /**
     * 自动保存攻略V2
     * 多预算等级
     *
     * @param detailVo    攻略详情vo
     * @param searchParam 查询攻略参数
     * <AUTHOR>
     * @date 2024/6/18 15:35
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void autoSaveItineraryInfoV2(ItineraryDetailVo detailVo, ItinerarySearchParam searchParam) {

        ItineraryInfoEntity infoEntity = new ItineraryInfoEntity();
        infoEntity.setItinerarySn(detailVo.getItinerarySn());
        infoEntity.setUid(detailVo.getUid());
        infoEntity.setItineraryType(searchParam.getItineraryType());
        infoEntity.setItineraryTitle(detailVo.getItineraryTitle());
        infoEntity.setGoTime(searchParam.getGoTime());
        infoEntity.setBackTime(searchParam.getBackTime());
        LocalDateTime goTime = searchParam.getGoTime();
        LocalDateTime backTime = searchParam.getBackTime();
        int days = (int) goTime.toLocalDate().until(backTime.toLocalDate(), ChronoUnit.DAYS) + 1;
        infoEntity.setDays(days);
        infoEntity.setDepartureCityId(searchParam.getDepartureCityId());
        infoEntity.setDestinationCityIds(searchParam.getDestinationCityIds());
        infoEntity.setDestinationCityNames(searchParam.getDestinationCityNames());
        infoEntity.setTpType(searchParam.getTpType());
        infoEntity.setTravelMate(searchParam.getTravelMate());
        infoEntity.setTravelBudget(searchParam.getTravelBudget());
        infoEntity.setTravelRhythm(searchParam.getTravelRhythm());
        infoEntity.setTravelPreference(searchParam.getTravelPreference());
        infoEntity.setCityDateRanges(detailVo.getCityDateRanges());
        infoEntity.setMustGoActivities(searchParam.getMustActivities());
        infoEntity.setBudget(detailVo.getBudget());
        infoEntity.setLocale(searchParam.getLocale());
        // 保存攻略信息
        baseMapper.insert(infoEntity);
        Long itineraryId = infoEntity.getId();

        // 保存每日数据
        List<ItineraryBudgetLevelVo> budgetLevels = detailVo.getBudgetLevels();
        itineraryDayService.saveItineraryBudgetLevels(itineraryId, budgetLevels);

//        // 保存交通数据
//        List<ItineraryTransportationEntity> tpEntityList = ItineraryUtil.getTpEntityListFromLevels(itineraryId, budgetLevels);
//        itineraryTransportationService.saveBatch(tpEntityList);
    }

    /**
     * 查询攻略信息Entity分页列表
     *
     * @param itineraryQueryListParam 查询攻略列表参数
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.somytrip.entity.itinerary.ItineraryInfoEntity>
     * <AUTHOR>
     * @date 2024/4/13 21:39
     */
    @Override
    public IPage<ItineraryInfoEntity> queryList(ItineraryQueryListParam itineraryQueryListParam) {

        PaginationDto pagination = itineraryQueryListParam.getPagination();
        IPage<ItineraryInfoEntity> page = new Page<>(pagination.getPageNum(), pagination.getPageSize());
        return baseMapper.queryList(page, itineraryQueryListParam);
    }

    /**
     * 根据ID列表查询攻略列表
     *
     * @param ids ID列表
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.somytrip.entity.itinerary.ItineraryInfoEntity>
     * <AUTHOR>
     * @date 2024/5/30 15:21
     */
    @Override
    public List<ItineraryInfoEntity> queryListFromIds(List<Long> ids) {

        LambdaQueryWrapper<ItineraryInfoEntity> qw = new LambdaQueryWrapper<>();
        qw.in(ItineraryInfoEntity::getId, ids);
        return baseMapper.selectList(qw);
    }

    /**
     * 查询攻略列表年份筛选项
     *
     * @param uid 用户ID
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2024/5/14 11:58
     */
    @Override
    public List<String> queryYearFilters(String uid) {
        return baseMapper.queryYearFilters(uid);
    }

    /**
     * 查询攻略详情
     *
     * @param itinerarySn 攻略序列号
     * @param uid         用户ID
     * @return com.somytrip.entity.itinerary.ItineraryDetailVo
     * <AUTHOR>
     * @date 2024/4/14 1:11
     */
    @Override
    public ItineraryDetailVo queryDetail(String itinerarySn, String uid) {

        return queryDetail(itinerarySn, uid, null);
    }

    /**
     * 查询攻略详情
     * 携带预备数据map
     *
     * @param itinerarySn     攻略序列号
     * @param uid             用户ID
     * @param preparedDataMap 预备数据map
     * @return com.somytrip.entity.itinerary.ItineraryDetailVo
     * <AUTHOR>
     * @date 2024/8/6 14:20
     */
    @Override
    public ItineraryDetailVo queryDetail(
            String itinerarySn,
            String uid,
            Map<ItineraryActivityType, Map<String, ItineraryActivityDto>> preparedDataMap
    ) {

        log.info("Start Query Itinerary Detail, Uid: {}, ItinerarySn: {}", uid, itinerarySn);

        ItineraryInfoEntity infoEntity = queryOne(itinerarySn);
        if (infoEntity == null) {
            log.warn("ItineraryInfoEntity NotFound, itinerarySn: {}, uid: {}", itinerarySn, uid);
            throw new BusinessException("api.itinerary.not-existing");
        }

        // 国际化
        Locale locale = LocaleContextHolder.getLocale();

        // 读取缓存
        RedisKey detailCacheKey = RedisKey.ITINERARY_DETAIL_CACHE;
        String redisKey = detailCacheKey.appendKey(infoEntity.getUid(), itinerarySn, locale);
        String redisData = redisService.getString(redisKey);
        if (StringUtils.isNotBlank(redisData)) {
            log.info("Read Cache[itineraryDetail]");
            return JSONObject.parseObject(redisData, ItineraryDetailVo.class);
        }

        // 根据攻略信息Entity获取攻略详情vo
        ItineraryDetailVo detailVo = getDetailVoFromInfoEntity(infoEntity);

        // 预备数据判空
        if (preparedDataMap == null) {
            preparedDataMap = getActivityTypeIdMapV2(infoEntity);
        }

        // 查询每日数据列表
        List<ItineraryBudgetLevelVo> budgetLevelVoList =
                getBudgetLevelVosFromItineraryId(infoEntity, preparedDataMap, locale);
        detailVo.setBudgetLevels(budgetLevelVoList);

        // 查询攻略指南
        List<Integer> destinationCityIds = infoEntity.getDestinationCityIds();
        List<ItineraryGuidebookVo> guidebookVos = itineraryGuidebookService.queryGuidebookFromCityIds(destinationCityIds);
        detailVo.setGuidebook(guidebookVos);

        // 缓存
        boolean cacheResult = redisService.setString(
                redisKey,
                JSONObject.toJSONString(detailVo),
                detailCacheKey.getTimeout()
        );
        if (!cacheResult) {
            log.warn("Cache Itinerary Detail Fail, Uid: {}, ItinerarySn: {}", uid, itinerarySn);
        }

        log.info("Over Query Itinerary Detail, Uid: {}, ItinerarySn: {}", uid, itinerarySn);

        return detailVo;
    }

    /**
     * 查询攻略详情
     * 指定预算等级
     *
     * @param itinerarySn 攻略序列号
     * @param budgetLevel 预算等级
     * @return com.somytrip.entity.itinerary.ItineraryDetailVo
     * <AUTHOR>
     * @date 2024/6/19 16:05
     */
    @Override
    public ItineraryDetailVo queryDetail(String itinerarySn, Integer budgetLevel) {

        log.info("Start Query Itinerary Detail, ItinerarySn: {}", itinerarySn);

        ItineraryInfoEntity infoEntity = queryOne(itinerarySn);
        if (infoEntity == null) {
            log.warn("ItineraryInfoEntity NotFound, itinerarySn: {}", itinerarySn);
            throw new BusinessException("api.itinerary.not-existing");
        }

        Locale locale = LocaleContextHolder.getLocale();

        // 根据攻略信息Entity获取攻略详情vo
        ItineraryDetailVo detailVo = getDetailVoFromInfoEntity(infoEntity);

        // 获取预备数据
        Map<ItineraryActivityType, Map<String, ItineraryActivityDto>> activityTypeIdMap =
                getActivityTypeIdMapV2(infoEntity);

        // 查询每日数据列表
        ItineraryBudgetLevel budgetLevelEnum = ItineraryBudgetLevel.fromValue(budgetLevel);
        List<ItineraryBudgetLevelVo> budgetLevelVoList =
                getBudgetLevelVosFromItineraryId(infoEntity.getId(), budgetLevelEnum, activityTypeIdMap, locale);
        detailVo.setBudgetLevels(budgetLevelVoList);

        log.info("Over Query Itinerary Detail, ItinerarySn: {}", itinerarySn);

        return detailVo;
    }

    /**
     * 查询一条攻略信息
     *
     * @param itinerarySn 攻略序列号
     * @param uid         用户ID
     * @return com.somytrip.entity.itinerary.ItineraryInfoEntity
     * <AUTHOR>
     * @date 2024/4/15 23:06
     */
    @Override
    public ItineraryInfoEntity queryOne(String itinerarySn, String uid) {

        LambdaQueryWrapper<ItineraryInfoEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(ItineraryInfoEntity::getItinerarySn, itinerarySn)
                .eq(ItineraryInfoEntity::getUid, uid)
                .eq(ItineraryInfoEntity::isDel, false);
        return baseMapper.selectOne(qw);
    }

    /**
     * 根据攻略序列号查询攻略信息实体
     *
     * @param itinerarySn 攻略序列号
     * @return com.somytrip.entity.itinerary.ItineraryInfoEntity
     * <AUTHOR>
     * @date 2024/6/6 16:05
     */
    @Override
    public ItineraryInfoEntity queryOne(String itinerarySn) {

        LambdaQueryWrapper<ItineraryInfoEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(ItineraryInfoEntity::getItinerarySn, itinerarySn)
                .eq(ItineraryInfoEntity::isDel, false);
        return baseMapper.selectOne(qw);
    }

    /**
     * 根据攻略主键ID查询攻略信息Entity
     *
     * @param itineraryId 攻略主键ID
     * @return com.somytrip.entity.itinerary.ItineraryInfoEntity
     * <AUTHOR>
     * @date 2025/5/6 14:32
     */
    @Override
    public ItineraryInfoEntity queryOne(Long itineraryId) {

        LambdaQueryWrapper<ItineraryInfoEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(ItineraryInfoEntity::getId, itineraryId)
                .eq(ItineraryInfoEntity::isDel, false);
        return baseMapper.selectOne(qw);
    }

    /**
     * 根据攻略序列号和用户ID查询攻略ID
     *
     * @param itinerarySn 攻略序列号
     * @param uid         用户ID
     * @return java.lang.Long
     * <AUTHOR>
     * @date 2024/11/16 16:42
     */
    @Override
    public Long getItineraryIdFromSn(String itinerarySn, String uid) {

        LambdaQueryWrapper<ItineraryInfoEntity> qw = new LambdaQueryWrapper<>();
        qw.eq(ItineraryInfoEntity::getItinerarySn, itinerarySn)
                .eq(ItineraryInfoEntity::isDel, false)
                .select(ItineraryInfoEntity::getId);
        ItineraryInfoEntity infoEntity = baseMapper.selectOne(qw);
        if (infoEntity == null) {
            return null;
        }
        return infoEntity.getId();
    }

    /**
     * 处理更新活动V2(updateType=1/2/3/4)
     *
     * @param updateParam 更新攻略参数
     * @param infoEntity  攻略信息实体
     * @return java.util.Map<com.somytrip.entity.enums.itinerary.ItineraryActivityType, java.util.Map < java.lang.String, com.somytrip.entity.itinerary.ItineraryActivityDto>>
     * <AUTHOR>
     * @date 2024/5/10 15:07
     */
    @Transactional
    @Override
    public Map<ItineraryActivityType, Map<String, ItineraryActivityDto>> processUpdateActivity(
            ItineraryUpdateParam updateParam,
            ItineraryInfoEntity infoEntity
    ) {

        long start = System.currentTimeMillis();

        // 更新类型
        ItineraryUpdateType updateType = ItineraryUpdateType.fromValue(updateParam.getUpdateType());
        // 开始时间/目标时间
        LocalDateTime targetStartDateTime = updateParam.getStartTime();
        // 停留时间(分钟)
        Integer stayMinutes = Optional.ofNullable(updateParam.getStayMinutes()).orElse(0);
        // 目标结束时间
        LocalDateTime targetEndDateTime = targetStartDateTime.plusMinutes(stayMinutes);
//        // 开始时间
//        LocalTime startTime = startDateTime.toLocalTime();
        // 攻略序列号
        String itinerarySn = updateParam.getItinerarySn();
        // 预算等级
        Integer budgetLevel = updateParam.getBudgetLevel();
        // 目标活动
        ActivityDetailParam targetActivityDetail = updateParam.getActivityDetail();
        // 目标活动ID
        String targetActivityId = targetActivityDetail.getActivityId();

        Long itineraryId = infoEntity.getId();
        BigDecimal budget = infoEntity.getBudget() != null ? infoEntity.getBudget() : BigDecimal.ZERO;
        BigDecimal oldBudget, newBudget;

        // 查询操作目标日数据
        ItineraryDayEntity targetDayEntity =
                itineraryDayService.queryOneDay(itineraryId, budgetLevel, targetStartDateTime.toLocalDate());
//        log.info("targetDayEntity: {}", JSONObject.toJSONString(targetDayEntity));
        if (targetDayEntity == null) {
            log.warn("UpdateItinerary DayEntity Not Found, ItinerarySn: {}, Date: {}",
                    itinerarySn, targetStartDateTime.toLocalDate());
            throw new BusinessException("api.itinerary.date-not-existing");
        }

        // 目标日活动列表
        List<ItineraryDayEntity.ActivityEntity> activityEntityList = targetDayEntity.getActivities();
//        // 预检
//        int targetIndex = getTargetIndex(activityEntityList, startTime);
//        log.info("targetIndex: {}", targetIndex);
//        if (targetIndex < 0) {
//            targetIndex = activityEntityList.size() - 1;
//        }
        // 获取预备数据
        Map<ItineraryActivityType, Map<String, ItineraryActivityDto>> activityTypeIdMap =
                getActivityTypeIdMapV2(infoEntity, targetStartDateTime);
//        log.info("activityTypeIdMap HOTEL: {}", JSONObject.toJSONString(activityTypeIdMap.get(ItineraryActivityType.HOTEL)));

        // 将目标日活动列表Entity转为活动vo列表
        List<ItineraryActivityDto> activityDtoList = convertActivityEntityList2DtoList(activityEntityList, activityTypeIdMap);
//        log.info("activityVoList: {}", JSONArray.toJSONString(activityVoList));
        // 旧活动列表总预算
        oldBudget = ItineraryUtil.countPrice(activityDtoList);

        // 处理修改、删除、替换活动时 删除原本活动所在位置
        if (updateType.equals(ItineraryUpdateType.MODIFY_ACTIVITY)
                || updateType.equals(ItineraryUpdateType.REMOVE_ACTIVITY)
                || updateType.equals(ItineraryUpdateType.REPLACE_ACTIVITY)) {
//            LocalDateTime startTime = updateParam.getStartTime();
            LocalDateTime fromTime = updateParam.getFromTime();

//            String removeActivityId = updateType == 2 ? targetActivityId : updateParam.getFromActivityId();
            String removeActivityId = updateType.equals(ItineraryUpdateType.REPLACE_ACTIVITY)
                    ? updateParam.getFromActivityId()
                    : targetActivityId;

            // 删除目标活动原本记录
            if (updateType.equals(ItineraryUpdateType.REMOVE_ACTIVITY)
                    || targetStartDateTime.toLocalDate().isEqual(fromTime.toLocalDate())) {
                processRemoveActivity(removeActivityId, activityDtoList, targetDayEntity);
            } else {
                // 修改目标日与活动原本所在日不同
                ItineraryDayEntity fromDayEntity =
                        itineraryDayService.queryOneDay(itineraryId, budgetLevel, fromTime.toLocalDate());
                if (fromDayEntity == null) {
                    log.warn("FromDayEntity is null");
                    throw new BusinessException("api.itinerary.update-non-target");
                }
                List<ItineraryActivityDto> fromDayActivityVoList =
                        convertActivityEntityList2DtoList(fromDayEntity.getActivities(), activityTypeIdMap);
                processRemoveActivity(removeActivityId, fromDayActivityVoList, fromDayEntity);
            }
        }

        if (!updateType.equals(ItineraryUpdateType.REMOVE_ACTIVITY)) {
            // 获取目标活动vo
            ItineraryActivityDto targetActivityVo = getTargetActivityDto(targetActivityDetail, activityTypeIdMap);
            if (targetActivityVo == null) {
                log.warn("TargetActivityVo is null");
                throw new BusinessException("api.itinerary.update-non-target");
            }

            // 获取目标活动要插入的目标下标
            int targetIndex = getTargetIndexV2(activityDtoList, targetStartDateTime, targetEndDateTime);

            // 将目标活动插入活动列表目标位置
            activityDtoList.add(targetIndex, targetActivityVo);

            // 重新安排目标日时间
            List<ItineraryActivityDto> newList = processRearrangeDayList(activityDtoList, targetActivityVo, targetIndex, updateParam);
            itineraryDayService.updateWithNewActivityVos(targetDayEntity, newList);
        }

        // 新活动列表总预算
        newBudget = ItineraryUtil.countPrice(activityDtoList);
        BigDecimal difference = newBudget.subtract(oldBudget);
        if (difference.compareTo(BigDecimal.ZERO) != 0) {
            log.info("Budget Difference: {}", difference);
            // 价格有变化
            budget = budget.add(difference);
            infoEntity.setBudget(budget);
            baseMapper.updateById(infoEntity);
        }

        long end = System.currentTimeMillis();
        log.info("Over Process Update, Uid: {}, ItinerarySn: {}, Executing Time: {}",
                updateParam.getUid(), itinerarySn, end - start);

        return activityTypeIdMap;
    }

    /**
     * 更新攻略标题
     *
     * @param updateParam 更新攻略参数
     * @param infoEntity  攻略信息实体
     * <AUTHOR>
     * @date 2024/4/28 14:40
     */
    @Override
    public void updateItineraryTitle(ItineraryUpdateParam updateParam, ItineraryInfoEntity infoEntity) {
        infoEntity.setItineraryTitle(updateParam.getTitle());
        baseMapper.updateById(infoEntity);
    }

    /**
     * 更新用户选择预算等级
     *
     * @param infoEntity     攻略信息实体
     * @param newBudgetLevel 新预算等级
     * <AUTHOR>
     * @date 2024/6/19 17:23
     */
    @Override
    public void updateSelectedBudgetLevel(ItineraryInfoEntity infoEntity, Integer newBudgetLevel) {
        infoEntity.setTravelBudget(newBudgetLevel);
        baseMapper.updateById(infoEntity);
    }

    /**
     * 替换原攻略
     * 保持攻略序列号相同, 替换攻略内容
     *
     * @param infoEntity  原攻略信息实体
     * @param detailVo    新攻略详情vo
     * @param searchParam 攻略查询参数
     * @return com.somytrip.entity.itinerary.ItineraryDetailVo
     * <AUTHOR>
     * @date 2024/5/10 17:42
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ItineraryDetailVo replaceItinerary(
            ItineraryInfoEntity infoEntity,
            ItineraryDetailVo detailVo,
            ItinerarySearchParam searchParam
    ) {

        // 逻辑删除原攻略信息记录
        infoEntity.setDel(true);
        baseMapper.updateById(infoEntity);

        infoEntity.convert4Replace();

        // 标题
        infoEntity.setItineraryTitle(detailVo.getItineraryTitle());
        // 去程时间
        infoEntity.setGoTime(searchParam.getGoTime());
        // 返程时间
        infoEntity.setBackTime(searchParam.getBackTime());
        // 天数
        LocalDateTime goTime = searchParam.getGoTime();
        LocalDateTime backTime = searchParam.getBackTime();
        int days = (int) goTime.toLocalDate().until(backTime.toLocalDate(), ChronoUnit.DAYS) + 1;
        infoEntity.setDays(days);
        // 目的地城市列表
        infoEntity.setDestinationCityIds(searchParam.getDestinationCityIds());
        // 预算
        infoEntity.setBudget(detailVo.getBudget());
        // 各城市日期区间
        infoEntity.setCityDateRanges(detailVo.getCityDateRanges());
        baseMapper.insert(infoEntity);

        // 新的攻略信息主键ID
        Long newItineraryId = infoEntity.getId();

        // 保存每日数据
        boolean diyIsAdded = false;
        List<ItineraryBudgetLevelVo> budgetLevels = detailVo.getBudgetLevels();
        ItineraryType itineraryType = ItineraryType.fromValue(searchParam.getItineraryType());
        Long fromItineraryId = searchParam.getFromItineraryId();
        // DIY攻略替换原攻略 如已有用户自主添加的数据 保留处于新日期区间内的数据
        if (itineraryType.equals(ItineraryType.DIY) && fromItineraryId != null) {
            // 查询旧每日数据列表
            List<ItineraryDayEntity> existingEntityList = itineraryDayService.queryByItineraryId(fromItineraryId);
            if (CollUtil.isNotEmpty(existingEntityList)) {
                List<ItineraryDayEntity> newDayList = new ArrayList<>();
                for (ItineraryBudgetLevelVo budgetLevelVo : budgetLevels) {
                    Integer budgetLevel = budgetLevelVo.getBudgetLevel();
                    List<ItineraryDayVo> curDays = budgetLevelVo.getDays();
                    for (ItineraryDayVo curDay : curDays) {
                        LocalDate date = curDay.getDate();
                        CitySimpleVo cityInfo = curDay.getCityInfo();
                        Optional<ItineraryDayEntity> targetOptional = existingEntityList.stream()
                                .filter(item -> Objects.equals(item.getBudgetLevel(), budgetLevel))
                                .filter(item -> Objects.equals(item.getDate(), date))
                                .filter(item -> Objects.equals(item.getCityCode(), cityInfo.getCityCode()))
                                .findFirst();
                        ItineraryDayEntity target = new ItineraryDayEntity();
                        if (targetOptional.isPresent()) {
                            target = targetOptional.get();
                            target.convert4Copy();
                            target.setItineraryId(newItineraryId);
                        } else {
                            target.setItineraryId(newItineraryId);
                            target.setDate(date);
                            target.setCityCode(curDay.getCityInfo().getCityCode());
                            target.setBudgetLevel(budgetLevel);
                            target.setDayNum(curDay.getDayNum());
                            target.setActivities(new ArrayList<>());
                        }
                        newDayList.add(target);
                    }
                }
                log.info("newDayList: {}", JSON.toJSONString(newDayList));
                if (!newDayList.isEmpty()) {
                    diyIsAdded = true;
                    itineraryDayService.saveBatch(newDayList);
                }
            }
        } else {
            // 其他类型攻略 直接替换原攻略
            itineraryDayService.saveItineraryBudgetLevels(newItineraryId, budgetLevels);
        }

        // 保存交通数据
        List<ItineraryTransportationEntity> tpEntityList = ItineraryUtil.getTpEntityListFromLevels(newItineraryId, budgetLevels);
        itineraryTransportationService.saveBatch(tpEntityList);

        if (diyIsAdded) {
            detailVo = queryDetail(infoEntity.getItinerarySn(), infoEntity.getUid());
        }
        return detailVo;
    }

    /**
     * 删除攻略
     *
     * @param itinerarySn 攻略序列号
     * @param uid         用户ID
     * @return boolean
     * <AUTHOR>
     * @date 2024/4/20 15:10
     */
    @Override
    public boolean deleteItinerary(String itinerarySn, String uid) {

        log.info("Start Delete Itinerary, Uid: {}, ItinerarySn: {}", uid, itinerarySn);

        ItineraryInfoEntity infoEntity = queryOne(itinerarySn, uid);
        if (infoEntity == null) {
            log.warn("ItineraryInfoEntity Not Found, Uid: {}, ItinerarySn: {}", uid, itinerarySn);
            throw new BusinessException("api.itinerary.not-existing-non-auth");
        }

        infoEntity.setDel(true);
        int updateResult = baseMapper.updateById(infoEntity);
        if (updateResult < 1) {
            log.warn("Delete Itinerary Fail, itinerarySn: {}, uid: {}, updateResult: {}", itinerarySn, uid, updateResult);
            return false;
        }

        // 记录操作日志
        itineraryOperationLogService.log(new ItineraryOperationLogDto(uid, infoEntity.getId(), ItineraryOperationType.DELETE));

        log.info("Over Delete Itinerary, Uid: {}, ItinerarySn: {}", uid, itinerarySn);

        return true;
    }

    /**
     * 另存为攻略
     *
     * @param param 另存为参数
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/6/6 11:47
     */
    @Transactional
    @Override
    public String saveAsItinerary(ItinerarySaveAsParam param) {

        String itinerarySn = param.getItinerarySn();
        String uid = param.getUid();
        String title = param.getTitle();
        LocalDate goTime = param.getGoTime();

        // 查询目标攻略信息Entity
        ItineraryInfoEntity infoEntity = queryOne(itinerarySn);
        if (infoEntity == null) {
            throw new BusinessException("api.itinerary.not-existing");
        }
        // 旧攻略主键ID
        Long existingItineraryId = infoEntity.getId();

        // 清除默认属性
        infoEntity.convert4Copy();
        // 设置新属性
        String newItinerarySn = ItineraryUtil.getItinerarySn(idGenConfig);
        infoEntity.setItinerarySn(newItinerarySn);
        infoEntity.setUid(uid);
        if (StringUtils.isNotBlank(title)) {
            infoEntity.setItineraryTitle(title);
        }
        // 插入新攻略信息
        baseMapper.insert(infoEntity);

        // 新攻略主键ID
        Long newItineraryId = infoEntity.getId();
        // 复制日数据
        if (goTime == null) {
            goTime = infoEntity.getGoTime().toLocalDate();
        }
        itineraryDayService.saveAsDayList(existingItineraryId, newItineraryId, goTime);
        // 复制交通数据
        itineraryTransportationService.saveAsTransportation(existingItineraryId, newItineraryId);

        return newItinerarySn;
    }

    /**
     * 获取excel对象数据
     *
     * @param itinerarySn 攻略序列号
     * @param budgetLevel 预算等级
     * @return java.util.List<com.somytrip.entity.tourism.excel.ItineraryExcelDayVo>
     * <AUTHOR>
     * @date 2024/6/6 16:04
     */
    @Override
    public List<ItineraryExcelDayVo> getExcelData(String itinerarySn, ItineraryBudgetLevel budgetLevel) {

        ItineraryInfoEntity infoEntity = queryOne(itinerarySn);
        List<ItineraryDayEntity> dayEntityList = itineraryDayService.queryByItineraryId(infoEntity.getId(), budgetLevel);

        // 多语言
        Locale locale = LocaleContextHolder.getLocale();

        // 查询交通数据
        Map<ItineraryBudgetLevel, Map<LocalDate, List<ItineraryTransportationVo>>> tpData =
                itineraryTransportationService.queryFromItineraryIdMultipleCity(infoEntity.getId());
        Map<LocalDate, List<ItineraryTransportationVo>> tpMap = tpData.get(budgetLevel);

        List<ItineraryExcelDayVo> excelDayVoList = new ArrayList<>();
        for (ItineraryDayEntity dayEntity : dayEntityList) {
            ItineraryExcelDayVo excelDayVo = new ItineraryExcelDayVo();
            // 第几天
            excelDayVo.setDayNum(dayEntity.getDayNum());
            // 日期
            LocalDate date = dayEntity.getDate();
            // 获取星期几
            DayOfWeek dayOfWeek = date.getDayOfWeek();
            String dateStr = LocalDateTimeUtil.convertLocalDate2Str(
                    date,
                    "yyyy/M/d\n"
            ) + dayOfWeek.getDisplayName(TextStyle.FULL, locale);
            excelDayVo.setDate(dateStr);
            // 城市
            String cityCode = dayEntity.getCityCode();
            if (StringUtils.isBlank(cityCode) && dayEntity.getCityInfo() != null) {
                cityCode = dayEntity.getCityInfo().getCityCode();
            }
            GlobalCityEntity cityEntity = globalCityService.getEntityByCityCode(cityCode);
            String cityItem = cityEntity.getCityNameCn() + "\n" + cityEntity.getCityNameEn();
            excelDayVo.setCity(cityItem);
            // 活动
            List<ItineraryExcelActivityVo> excelActivityVoList = new ArrayList<>();
            String curDayHotelStr = "";
            List<ItineraryDayEntity.ActivityEntity> activityEntityList = dayEntity.getActivities();
            for (ItineraryDayEntity.ActivityEntity activityEntity : activityEntityList) {
                ItineraryActivityType activityType = ItineraryActivityType.fromValue(activityEntity.getType());
                String activityId = activityEntity.getActivityId();

                if (ItineraryActivityType.RESTAURANT_FLAG.equals(activityType)) {
                    continue;
                }

                // 酒店
                if (activityType.equals(ItineraryActivityType.HOTEL)) {
                    curDayHotelStr = activityEntity.getActivityName().concat("\n地址：").concat(activityEntity.getAddress());
                    continue;
                }

                ItineraryExcelActivityVo excelActivityVo = new ItineraryExcelActivityVo();
                // 餐厅
                if (activityType.equals(ItineraryActivityType.RESTAURANT)) {
                    excelActivityVo = new ItineraryExcelActivityVo(activityEntity);
                }
                // 景点
                if (activityType.equals(ItineraryActivityType.SCENIC)) {
                    excelActivityVo = itineraryDataService.queryExcelActivity(activityType, activityId, null);
                    excelActivityVo.setStayTimePeriod(activityEntity.getStayTimePeriod());
                }
                excelActivityVoList.add(excelActivityVo);
            }
            excelDayVo.setActivities(excelActivityVoList);
            // 住宿
            excelDayVo.setHotel(curDayHotelStr);
            // 交通
            if (tpMap != null) {
                List<ItineraryTransportationVo> transportationVos = tpMap.get(date);
                excelDayVo.setTp(ItineraryExcelUtil.getTpValue(transportationVos));
            }
            excelDayVoList.add(excelDayVo);
        }

        return excelDayVoList;
    }

    /**
     * 根据攻略信息Entity获取攻略详情vo
     *
     * @param infoEntity 攻略信息Entity
     * @return com.somytrip.entity.itinerary.ItineraryDetailVo
     * <AUTHOR>
     * @date 2024/4/28 15:46
     */
    private ItineraryDetailVo getDetailVoFromInfoEntity(ItineraryInfoEntity infoEntity) {

        // 国际化
        Locale locale = LocaleContextHolder.getLocale();

        ItineraryDetailVo detailVo = new ItineraryDetailVo();
        // 用户ID
        detailVo.setUid(infoEntity.getUid());
        // 攻略序列号
        detailVo.setItinerarySn(infoEntity.getItinerarySn());
        // 攻略标题
        detailVo.setItineraryTitle(infoEntity.getItineraryTitle());
        // 公路而类型
        detailVo.setItineraryType(infoEntity.getItineraryType());
        // 天数
        if (infoEntity.getDays() != null) {
            String daysUnit = DaysUnitLocale.fromLocale(locale);
            detailVo.setDays(infoEntity.getDays() + daysUnit);
        }
        // 年份
        LocalDateTime goTime = infoEntity.getGoTime();
        if (goTime != null) {
            detailVo.setItineraryYear(String.valueOf(goTime.getYear()));
        }
        // 出发城市
        Integer departureCityId = infoEntity.getDepartureCityId();
        if (departureCityId != null) {
            CityDto cityDto = globalCityService.queryOneById(departureCityId);
            detailVo.setDepartureCity(new CitySimpleVo(cityDto));
        }

        // 旅行伴侣
        Integer travelMate = infoEntity.getTravelMate();
        if (travelMate != null) {
            String travelMateName = TravelMateLocale.fromLocale(travelMate, locale);
            detailVo.setTravelMate(travelMateName);
        }
        // 当前预算等级
        detailVo.setBudgetLevel(infoEntity.getTravelBudget());
        // 预算
        detailVo.setBudget(infoEntity.getBudget());
        // 行程节奏
        Integer travelRhythmValue = infoEntity.getTravelRhythm();
        if (travelRhythmValue != null) {
            String travelRhythmName = TravelRhythmLocale.fromLocale(travelRhythmValue, locale);
            detailVo.setTravelRhythm(travelRhythmName);
        }
        // 各城市日期区间
        detailVo.setCityDateRanges(infoEntity.getCityDateRanges());

        return detailVo;
    }

    /**
     * 根据攻略ID查询指定预算等级列表
     *
     * @param itineraryId       攻略ID
     * @param budgetLevel       预算等级
     * @param activityTypeIdMap 预备数据
     * @return java.util.List<com.somytrip.entity.itinerary.ItineraryBudgetLevelVo>
     * <AUTHOR>
     * @date 2024/7/18 11:27
     */
    private List<ItineraryBudgetLevelVo> getBudgetLevelVosFromItineraryId(
            Long itineraryId,
            ItineraryBudgetLevel budgetLevel,
            Map<ItineraryActivityType, Map<String, ItineraryActivityDto>> activityTypeIdMap,
            Locale locale
    ) {

        List<ItineraryBudgetLevelVo> budgetLevelVoList = new ArrayList<>();

        // 查询每日数据
        List<ItineraryDayEntity> itineraryDayEntityList =
                itineraryDayService.queryByItineraryId(itineraryId, budgetLevel);

        // 查询交通数据
        Map<ItineraryBudgetLevel, Map<LocalDate, List<ItineraryTransportationVo>>> tpData =
                itineraryTransportationService.queryFromItineraryIdMultipleCity(itineraryId);

        ItineraryBudgetLevelVo budgetLevelVo = new ItineraryBudgetLevelVo();

        // 转换每日数据列表
        List<ItineraryDayVo> dayVoList = convertDayEntityList2DayVos(itineraryDayEntityList, activityTypeIdMap);
        budgetLevelVo.setDays(dayVoList);
        // 预算等级
        budgetLevelVo.setBudgetLevel(budgetLevel.getValue());
        String budgetLevelName = TravelBudgetLocale.fullNameFromLocale(budgetLevel.getValue(), locale);
        budgetLevelVo.setBudgetLevelName(budgetLevelName);

        budgetLevelVoList.add(budgetLevelVo);

        // 合并交通数据到攻略数据
        ItineraryUtil.mergeTpData2LevelsMultipleCity(budgetLevelVoList, tpData);

        return budgetLevelVoList;
    }

    /**
     * 处理根据攻略ID查询预算等级列表
     *
     * @param infoEntity        攻略信息Entity
     * @param activityTypeIdMap 预备数据
     * @return java.util.List<com.somytrip.entity.itinerary.ItineraryBudgetLevelVo>
     * <AUTHOR>
     * @date 2024/6/19 16:22
     */
    private List<ItineraryBudgetLevelVo> getBudgetLevelVosFromItineraryId(
            ItineraryInfoEntity infoEntity,
            Map<ItineraryActivityType, Map<String, ItineraryActivityDto>> activityTypeIdMap,
            Locale locale
    ) {

        List<ItineraryBudgetLevelVo> budgetLevelVoList = new ArrayList<>();

        Long itineraryId = infoEntity.getId();
        String itinerarySn = infoEntity.getItinerarySn();

        // 查询每日数据
        List<ItineraryDayEntity> itineraryDayEntityList =
                itineraryDayService.queryByItineraryId(itineraryId);
        // 按照预算等级分组 无预算等级设为-1
        Function<ItineraryDayEntity, Integer> budgetLevelKeyMapper = itineraryDay ->
                itineraryDay.getBudgetLevel() == null ? -1 : itineraryDay.getBudgetLevel();
        Map<Integer, List<ItineraryDayEntity>> groupByBudgetLevel = itineraryDayEntityList.stream()
                .collect(Collectors.groupingBy(budgetLevelKeyMapper));

        // 查询交通数据
        Map<ItineraryBudgetLevel, Map<LocalDate, List<ItineraryTransportationVo>>> tpData =
                itineraryTransportationService.queryTpMapByItineraryIdOrSn(itineraryId, itinerarySn);

        for (Map.Entry<Integer, List<ItineraryDayEntity>> entry : groupByBudgetLevel.entrySet()) {
            Integer budgetLevelValue = entry.getKey();
            ItineraryBudgetLevelVo budgetLevelVo = new ItineraryBudgetLevelVo();

            // 转换每日数据列表
            List<ItineraryDayEntity> dayEntityList = entry.getValue();
            List<ItineraryDayVo> dayVoList = convertDayEntityList2DayVos(dayEntityList, activityTypeIdMap);
            budgetLevelVo.setDays(dayVoList);

            // 计算预算
            BigDecimal budget = ItineraryUtil.countBudgetFromDays(dayVoList);
            budgetLevelVo.setBudget(budget);

            // 预算等级
            budgetLevelVo.setBudgetLevel(budgetLevelValue);
            String budgetLevelName = TravelBudgetLocale.fullNameFromLocale(budgetLevelValue, locale);
            budgetLevelVo.setBudgetLevelName(budgetLevelName);

            budgetLevelVoList.add(budgetLevelVo);
        }

        // 合并交通数据到攻略数据
        ItineraryUtil.mergeTpData2LevelsMultipleCity(budgetLevelVoList, tpData);

        return budgetLevelVoList;
    }

    /**
     * 转换每日Entity列表为vo列表
     *
     * @param dayEntityList     每日Entity列表
     * @param activityTypeIdMap 活动类型ID映射map
     * @return java.util.List<com.somytrip.entity.itinerary.ItineraryDayVo>
     * <AUTHOR>
     * @date 2024/6/19 16:25
     */
    private List<ItineraryDayVo> convertDayEntityList2DayVos(
            List<ItineraryDayEntity> dayEntityList,
            Map<ItineraryActivityType, Map<String, ItineraryActivityDto>> activityTypeIdMap
    ) {

        Locale locale = LocaleContextHolder.getLocale();

        List<String> cityCodes = dayEntityList.stream().map(ItineraryDayEntity::getCityCode).distinct().toList();
        Map<String, CityDto> cityCodeMap = globalCityService.getDtoMapFromCodes(cityCodes);

        List<ItineraryDayVo> dayVoList = new ArrayList<>();
        for (ItineraryDayEntity dayEntity : dayEntityList) {
            ItineraryDayVo dayVo = new ItineraryDayVo();
            LocalDate date = dayEntity.getDate();
            dayVo.setDate(date);
            String week = ItineraryUtil.getWeekByDate4Itinerary(date);
            dayVo.setWeek(week);
            dayVo.setDayNum(dayEntity.getDayNum());
            String cityCode = dayEntity.getCityCode();
            if (StringUtils.isNotBlank(cityCode)) {
                CityDto cityDto = cityCodeMap.get(cityCode);
                dayVo.setCityInfo(new CitySimpleVo(cityDto));
            } else {
                dayVo.setCityInfo(dayEntity.getCityInfo());
            }

            // 活动列表
            List<ItineraryActivityVo> activityVoList = new ArrayList<>();
            List<ItineraryDayEntity.ActivityEntity> activityEntityList = dayEntity.getActivities();
            activityEntityList = JSONArray.from(activityEntityList).toJavaList(ItineraryDayEntity.ActivityEntity.class);

            for (int i = 0; i < activityEntityList.size(); i++) {
                ItineraryDayEntity.ActivityEntity activityEntity = activityEntityList.get(i);

                ItineraryActivityDto activityDto = null;
                Integer typeValue = activityEntity.getType();
                ItineraryActivityType type = ItineraryActivityType.fromValue(typeValue);
                Map<String, ItineraryActivityDto> curIdMap = activityTypeIdMap.get(type);
                // 针对餐饮的特殊处理"type != 2"
                if (curIdMap == null
                        && !ItineraryActivityType.RESTAURANT.equals(type)
                        && !ItineraryActivityType.RESTAURANT_FLAG.equals(type)) {
                    log.warn("ActivityIdMap Not Found, type: {}", type);
                    throw new BusinessException("api.itinerary.non-type-map");
                }
                switch (type) {
                    case SCENIC, HOTEL -> activityDto = curIdMap.get(activityEntity.getActivityId());
                    case RESTAURANT -> activityDto = new ItineraryActivityDto(activityEntity);
                    case RESTAURANT_FLAG -> {
                        activityDto = new ItineraryActivityDto(activityEntity);
                        ItineraryDayEntity.ActivityEntity tmpActivity = null;
                        if (i > 0) {
                            tmpActivity = activityEntityList.get(i - 1);
                        } else if (i < activityEntityList.size() - 1) {
                            tmpActivity = activityEntityList.get(i + 1);
                        }
                        if (tmpActivity != null) {
                            activityDto.setLon(tmpActivity.getLon());
                            activityDto.setLat(tmpActivity.getLat());
                        }
                    }
                    default -> {
                    }
                }

                if (activityDto == null) {
                    continue;
                }
                // 合并活动数据
                activityDto.mergeDayEntity(activityEntity);
                // 国际化处理
                // 到下一地点时间
                BigDecimal hoursToNext = activityDto.getHoursToNext();
                if (hoursToNext != null) {
                    String timeToNext = ItineraryUtil.getTimeByHours(hoursToNext, locale);
                    activityDto.setTimeToNext(timeToNext);
                }
                // 到下一地点距离
                BigDecimal distanceToNextValue = activityDto.getDistanceToNextValue();
                if (distanceToNextValue != null) {
                    double distanceValue = distanceToNextValue.doubleValue() / 1000;
                    String distanceToNext = ItineraryUtil.convertDistance(distanceValue, locale);
                    activityDto.setDistanceToNext(distanceToNext);
                }
                // 当前地点停留时间
                BigDecimal stayTimeValue = activityDto.getStayTimeValue();
                if (stayTimeValue != null) {
                    String hourUnit = HourUnitLocale.fromLangName(locale.toString());
                    activityDto.setStayTime(stayTimeValue.stripTrailingZeros() + hourUnit);
                }

                // 避免引用赋值
                activityDto = JSONObject.from(activityDto).toJavaObject(ItineraryActivityDto.class);
                activityVoList.add(new ItineraryActivityVo(activityDto));
            }
            dayVo.setActivityList(activityVoList);
            dayVoList.add(dayVo);
        }
        return dayVoList;
    }

    /**
     * 根据攻略信息获取预备数据类型ID映射map
     *
     * @param infoEntity 攻略信息实体
     * @return java.util.Map<com.somytrip.entity.enums.itinerary.ItineraryActivityType, java.util.Map < java.lang.String, com.somytrip.entity.itinerary.ItineraryActivityVo>>
     * <AUTHOR>
     * @date 2024/6/25 14:34
     */
    private Map<ItineraryActivityType, Map<String, ItineraryActivityDto>> getActivityTypeIdMapV2(
            ItineraryInfoEntity infoEntity
    ) {

        return getActivityTypeIdMapV2(infoEntity, null);
    }

    /**
     * 根据攻略信息获取预备数据类型ID映射map
     *
     * @param infoEntity 攻略信息实体
     * @param datetime   datetime
     * @return java.util.Map<com.somytrip.entity.enums.itinerary.ItineraryActivityType, java.util.Map < java.lang.String, com.somytrip.entity.itinerary.ItineraryActivityVo>>
     * <AUTHOR>
     * @date 2024/11/12 18:13
     */
    private Map<ItineraryActivityType, Map<String, ItineraryActivityDto>> getActivityTypeIdMapV2(
            ItineraryInfoEntity infoEntity,
            LocalDateTime datetime
    ) {

        ItineraryPrepareDataParam prepareDataParam = new ItineraryPrepareDataParam();
        // 目的地城市ID列表
        List<Integer> destinationCityIds = infoEntity.getDestinationCityIds();
        prepareDataParam.setDestinationCityIds(destinationCityIds);
        // 目的地城市Dto列表
        List<CityDto> destinationCityDtoList = globalCityService.queryListByIds(destinationCityIds);
        prepareDataParam.setDestinationCityDtoList(destinationCityDtoList);
        // 目的地城市code列表
        List<String> destinationCityCodes = destinationCityDtoList.stream().map(CityDto::getCityCode).toList();
        prepareDataParam.setDestinationCityCodes(destinationCityCodes);
        // 查询数据类型
        List<ItineraryActivityType> activityTypes =
                new ArrayList<>(List.of(ItineraryActivityType.SCENIC, ItineraryActivityType.HOTEL));
        prepareDataParam.setActivityTypes(activityTypes);
        // 各城市日期区间
        prepareDataParam.setCityDateRanges(infoEntity.getCityDateRanges());
        // 查询数据目标日期
        prepareDataParam.setGoTime(datetime != null ? datetime : infoEntity.getGoTime());
        // 多语言
        Locale locale = LocaleContextHolder.getLocale();
        prepareDataParam.setLocale(locale);
        return itineraryDataService.getActivityTypeIdMap(prepareDataParam);
    }

    /**
     * 将活动entity合并转换为dto
     *
     * @param activityIdMapByType 根据类型分类的活动ID映射map
     * @param activityEntity      活动entity
     * @return com.somytrip.entity.itinerary.ItineraryActivityDto
     * <AUTHOR>
     * @date 2024/4/16 20:05
     */
    private ItineraryActivityDto convertActivityEntity2Dto(
            Map<ItineraryActivityType, Map<String, ItineraryActivityDto>> activityIdMapByType,
            ItineraryDayEntity.ActivityEntity activityEntity) {

        if (activityEntity == null) {
            return null;
        }
        ItineraryActivityDto activityDto;
        // 针对当前餐饮特殊处理
        if (activityEntity.getType() == 2) {
            activityDto = new ItineraryActivityDto(activityEntity);
        } else {
            ItineraryActivityType activityType = ItineraryActivityType.fromValue(activityEntity.getType());
            activityDto = activityIdMapByType.get(activityType).get(activityEntity.getActivityId());
        }
        if (activityDto == null) {
            return null;
        }

        activityDto.mergeDayEntity(activityEntity);

        ItineraryActivityVo activityVo = new ItineraryActivityVo(activityDto);
        return JSONObject.parseObject(JSONObject.toJSONString(activityVo), ItineraryActivityDto.class);
    }

    /**
     * 处理活动间信息 返回新的结束时间
     *
     * @param curActivity  当前活动
     * @param prevActivity 上一个活动
     * @return java.time.LocalTime
     * <AUTHOR>
     * @date 2024/4/16 20:03
     */
    private LocalDateTime processActivityBetweenInfo(ItineraryActivityDto curActivity,
                                                     ItineraryActivityDto prevActivity) {

        double distanceValue = DistanceUtil.getDistanceV2(
                prevActivity.getLon(), prevActivity.getLat(), curActivity.getLon(), curActivity.getLat()
        );

        // 国际化
        Locale locale = LocaleContextHolder.getLocale();

        // 距离(带单位)
        String distance = ItineraryUtil.convertDistance(distanceValue, locale);
        BigDecimal distanceToNextValue = ItineraryUtil.convertDistance2Meter(distanceValue);
        // 到下一活动地点的交通方式
        Integer transportation = ItineraryUtil.getTransportationByDistance(distanceValue);
        // 到下一活动地点的小时数
        BigDecimal hoursToNext = ItineraryUtil.getHoursToNext(distanceValue, transportation);
//        log.info("hoursToNext: {}", hoursToNext);
        // 到下一活动地点的时间
        String timeToNext = ItineraryUtil.getTimeByHours(hoursToNext, locale);

        LocalDateTime newNextStartTime = ItineraryUtil.addHours2Time(prevActivity.getStayDateTimeEnd(), hoursToNext);

        prevActivity.setDistanceToNext(distance);
        prevActivity.setDistanceToNextValue(distanceToNextValue);
        prevActivity.setTransportationModeToNext(transportation);
        prevActivity.setTimeToNext(timeToNext);
        prevActivity.setHoursToNext(hoursToNext);

        return newNextStartTime;
    }

    /**
     * 将活动Entity列表转换为vo列表
     *
     * @param entityList        活动Entity列表
     * @param activityIdTypeMap 活动ID类型Map
     * @return java.util.List<com.somytrip.entity.itinerary.ItineraryActivityDto>
     * <AUTHOR>
     * @date 2024/4/24 10:13
     */
    private List<ItineraryActivityDto> convertActivityEntityList2DtoList(
            List<ItineraryDayEntity.ActivityEntity> entityList,
            Map<ItineraryActivityType, Map<String, ItineraryActivityDto>> activityIdTypeMap) {

        return entityList.stream()
                .map(activityEntity -> convertActivityEntity2Dto(activityIdTypeMap, activityEntity))
                .filter(Objects::nonNull)
                .collect(Collectors.toCollection(ArrayList::new));
    }

    /**
     * 处理删除活动
     *
     * @param activityId 活动ID
     * @param aList      活动vo列表
     * @param dayEntity  当日数据entity
     * <AUTHOR>
     * @date 2024/4/24 10:59
     */
    private void processRemoveActivity(String activityId,
                                       List<ItineraryActivityDto> aList,
                                       ItineraryDayEntity dayEntity) {

        // 找到目标活动的下标
        int targetIndex = ItineraryUtil.getActivityIndexInList(activityId, aList);

        if (targetIndex < 0) {
            log.warn("UpdateItinerary[Delete] Activity Not Found, activityId: {}, targetIndex: {}, date: {}",
                    activityId, targetIndex, dayEntity.getDate());
            log.warn("activityList: {}", JSONArray.toJSONString(aList));
            throw new BusinessException("api.itinerary.activity-not-existing");
        }

        // 国际化
        Locale locale = LocaleContextHolder.getLocale();

        int size = aList.size();
        // 处理目标活动前后活动距离等信息
        if (targetIndex > 0 && targetIndex < size - 1) {
            ItineraryUtil.processActivityBetweenInfo(aList.get(targetIndex - 1), aList.get(targetIndex + 1), locale, Boolean.TRUE);
        }

        // 目标活动在列表的最后一个 将目标活动前一个活动的活动间信息清除
        if (targetIndex == size - 1 && targetIndex != 0) {
            ItineraryUtil.clearActivityBetweenInfo(aList.get(targetIndex - 1));
        }

        // 将目标活动从列表中删除
        aList.remove(targetIndex);

        itineraryDayService.updateWithNewActivityVos(dayEntity, aList);
    }

    /**
     * 根据更新参数中活动详情
     *
     * @param activityDetail    活动详情参数
     * @param activityIdTypeMap 活动ID类型Map
     * @return com.somytrip.entity.itinerary.ItineraryActivityDto
     * <AUTHOR>
     * @date 2024/4/27 15:15
     */
    private ItineraryActivityDto getTargetActivityDto(ActivityDetailParam activityDetail,
                                                      Map<ItineraryActivityType, Map<String, ItineraryActivityDto>> activityIdTypeMap) {

        ItineraryActivityDto activityDto;
        ItineraryActivityType activityType = ItineraryActivityType.fromValue(activityDetail.getActivityType());
        // 针对当前餐饮的处理
        if (Objects.equals(ItineraryActivityType.RESTAURANT, activityType)) {
            activityDto = new ItineraryActivityDto(activityDetail);
        } else {
            // 转换目标活动
            activityDto = activityIdTypeMap
                    .get(activityType)
                    .get(activityDetail.getActivityId());
        }

        ItineraryActivityVo targetActivityVo = new ItineraryActivityVo(activityDto);
        return JSONObject.parseObject(JSONObject.toJSONString(targetActivityVo), ItineraryActivityDto.class);
    }

    /**
     * 获取目标活动在活动列表中的目标下标V2
     *
     * @param activityDtoList 原活动列表
     * @param startDateTime   目标活动开始时间
     * @return int
     * <AUTHOR>
     * @date 2024/8/8 16:01
     */
    private int getTargetIndexV2(List<ItineraryActivityDto> activityDtoList,
                                 LocalDateTime startDateTime,
                                 LocalDateTime endDateTime) {

        int targetIndex = activityDtoList.size();
//        List<ItineraryActivityDto> tmpList = new ArrayList<>(activityDtoList);
//        log.info("activityVoList: {}", JSONArray.toJSONString(activityVoList));

//        if (!tmpList.isEmpty()) {
//            ItineraryActivityDto first = tmpList.get(0);
//            if (startDateTime.isBefore(first.getStayDateTimeStart())) {
//                // 早于第一个 插入在第一个
//                targetIndex = 0;
//            } else {
//                // 包含于某个已有活动 插入到该活动前
//                for (int i = 0; i < tmpList.size(); i++) {
//                    ItineraryActivityDto cur = tmpList.get(i);
//                    LocalDateTime curStart = cur.getStayDateTimeStart();
//                    LocalDateTime curEnd = cur.getStayDateTimeEnd();
//                    if (curEnd == null) break;
////                    if ((!startDateTime.isBefore(curStart) && !startDateTime.isAfter(curEnd))) {
////                        // 目标开始时间在当前活动点时间范围内(左闭右闭)
//                    if (startDateTime.isAfter(curStart) && startDateTime.isBefore(curEnd)) {
//                        // 左开右开
//                        log.warn("与cur时间冲突: {}", JSON.toJSONString(cur));
//                        throw new BusinessException("api.itinerary.update-time-wrong-p?" + cur.getActivityName());
//                    } else {
//                        ItineraryActivityDto next = tmpList.size() > i + 1 ? tmpList.get(i + 1) : null;
//                        if (next != null) {
//                            if (startDateTime.isAfter(curEnd) && startDateTime.isBefore(next.getStayDateTimeStart())) {
//                                if (endDateTIme != null && !endDateTIme.isBefore(next.getStayDateTimeStart())) {
//                                    throw new BusinessException("api.itinerary.update-time-wrong");
//                                }
//                                // 目标开始时间在当前活动点结束时间和下一个活动开始时间之间
//                                targetIndex = i + 1;
//                                break;
//                            } else if (startDateTime.equals(curEnd) || startDateTime.equals(next.getStayDateTimeStart())) {
//                                targetIndex = i + 1;
//                                break;
//                            }
//                        }
//                    }
//                }
//            }
//        }
//        if (targetIndex == -1) {
//            // 插入到最后
//            targetIndex = tmpList.size();
//        }

        for (int i = 0; i < activityDtoList.size(); i++) {
            ItineraryActivityDto cur = activityDtoList.get(i);
            LocalDateTime curStart = cur.getStayDateTimeStart();
            LocalDateTime curEnd = cur.getStayDateTimeEnd();
            // 判断目标时间是否在第一个活动前
            if (i == 0 && !endDateTime.isAfter(curStart)) {
                return 0;
            }
            // 判断目标时间是否在最后一个活动后
            log.info("curEnd: {}", curEnd);
            if (i == activityDtoList.size() - 1) {
                return activityDtoList.size();
            }
            // 判断目标区间是否与已有活动重叠
            if (startDateTime.isAfter(curStart) && startDateTime.isBefore(curEnd)
                    || endDateTime.isAfter(curStart) && endDateTime.isBefore(curEnd)) {
                log.warn("活动时间冲突: {}", JSON.toJSONString(cur));
                throw new BusinessException("api.itinerary.update-time-wrong-p?" + cur.getActivityName());
            }
            // 只有目标活动区间处于当前活动后与下个活动前才可插入
            ItineraryActivityDto next = activityDtoList.get(i + 1);
            LocalDateTime nextStart = next.getStayDateTimeStart();
//            if (startDateTime.isAfter(curEnd) && endDateTime.isBefore(nextStart)) {
            if (!startDateTime.isBefore(curEnd) && !endDateTime.isAfter(nextStart)) {
                targetIndex = i + 1;
                break;
            }
        }

        return targetIndex;
    }

    /**
     * 处理重新安排当天活动
     *
     * @param activityVoList    活动列表
     * @param targetActivityDto 目标活动dto
     * @param updateParam       更新攻略参数
     * @return java.util.List<com.somytrip.entity.itinerary.ItineraryActivityVo>
     * <AUTHOR>
     * @date 2024/4/27 16:02
     */
    private List<ItineraryActivityDto> processRearrangeDayList(List<ItineraryActivityDto> activityVoList,
                                                               ItineraryActivityDto targetActivityDto,
                                                               int targetIndex,
                                                               ItineraryUpdateParam updateParam) {

//        LocalDateTime targetStartDateTime =
        Integer stayMinutes = updateParam.getStayMinutes();
        LocalDateTime targetStartTime = updateParam.getStartTime();
        LocalDateTime targetEndTime = targetStartTime.plusMinutes(stayMinutes);

        targetActivityDto.setStayDateTimeStart(targetStartTime);
        targetActivityDto.setStayDateTimeEnd(targetEndTime);

        // 国际化
        Locale locale = LocaleContextHolder.getLocale();

        // 重新安排目标日时间
        // 将列表分为两部分: 目标活动前、目标活动后
        //      目标活动前: 最后一个与目标活动计算距离等信息
        //      目标活动后: 第一个与目标活动计算距离等信息, 后续活动相关时间后移
        List<ItineraryActivityDto> listBeforeTarget =
                activityVoList.subList(0, Math.min(targetIndex, activityVoList.size()));
        List<ItineraryActivityDto> listAfterTarget =
                targetIndex < activityVoList.size() - 1
                        ? activityVoList.subList(Math.min(targetIndex + 1, activityVoList.size() - 1), activityVoList.size())
                        : new ArrayList<>();
//        log.info("targetIndex: {}", targetIndex);
//        log.info("listBeforeTarget: {}", JSONArray.toJSONString(listBeforeTarget));
//        log.info("listAfterTarget: {}", JSONArray.toJSONString(listAfterTarget));

        // 处理前半部分
        if (!listBeforeTarget.isEmpty()) {
            // 目标活动前一个活动
            ItineraryActivityDto prev = listBeforeTarget.get(listBeforeTarget.size() - 1);
            if (prev != null) {
//                curEndTime = tmpActivity.getStayTimeEnd();
                ItineraryUtil.processActivityBetweenInfo(prev, targetActivityDto, locale, true);
                // 如果上一个是之前最后一个景点 补充结束时间
                if (prev.getStayDateTimeEnd() == null) {
                    prev.setStayDateTimeEnd(prev.getStayDateTimeStart());
                }
                targetActivityDto.setStayDateTimeEnd(targetActivityDto.getStayDateTimeStart().plusMinutes(stayMinutes));
            }
        }
        ItineraryUtil.setActivityStayInfo(targetActivityDto, locale);

        // 处理后半部分
        LocalDateTime curStartDateTime;
        LocalDateTime curEndDateTime;
        if (!listAfterTarget.isEmpty()) {
            LocalDateTime originalCurStartDateTime = listAfterTarget.get(0).getStayDateTimeStart();
            curStartDateTime = processActivityBetweenInfo(listAfterTarget.get(0), targetActivityDto);
            if (curStartDateTime.isBefore(originalCurStartDateTime)) {
                curStartDateTime = originalCurStartDateTime;
            }

            if (listAfterTarget.size() > 1) {
                for (int i = 1; i < listAfterTarget.size(); ++i) {
                    ItineraryActivityDto prevActivity = listAfterTarget.get(i - 1);
//                    ItineraryActivityVo curActivity = listAfterTarget.get(i);
                    prevActivity = JSONObject.from(prevActivity).toJavaObject(ItineraryActivityDto.class);

                    prevActivity.setStayDateTimeStart(curStartDateTime);
                    curEndDateTime = ItineraryUtil.addHours2Time(curStartDateTime, prevActivity.getStayTimeValue());
                    prevActivity.setStayDateTimeEnd(curEndDateTime);
                    ItineraryUtil.setActivityStayInfo(prevActivity, locale);
                    listAfterTarget.set(i - 1, prevActivity);
//                    log.info("curStartTime: {}", curStartTime);
//                    log.info("curEndTime: {}", curEndTime);
//                    log.info("prevActivity: {}", JSONObject.toJSONString(prevActivity));

                    curStartDateTime = ItineraryUtil.addHours2Time(curEndDateTime, prevActivity.getHoursToNext());
                }
            }
        }

        List<ItineraryActivityDto> newList = new ArrayList<>(listBeforeTarget);
        newList.add(targetActivityDto);
        newList.addAll(listAfterTarget);

        return newList;
    }
}
